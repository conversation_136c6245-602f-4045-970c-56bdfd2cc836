const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { I18n } = require('i18n');
const User = require('../models/user');
const Interest = require('../models/interest');
const Question = require('../models/question');
const Comment = require('../models/comment');
const { notFoundError, invalidInputError } = require('./http-errors');
const { translate, locales, translate_frontend } = require('./translate');
const { hasDuplicates, removeDuplicates } = require('./basic');
const constants = require('./constants');
const popularInterests = require('./popular-interests');
const { detectLanguage } = require('../lib/detect-language');
const { datingPreferencesMap, relationshipStatusMap, sexualOrientationMap } = require('../lib/interest-dating-preferences');
const openai = require('../lib/openai');
// eslint-disable-next-line import/no-unresolved
const parse = require('csv-parse/sync');

const translationsForEthnicity = JSON.parse(fs.readFileSync(path.join(__dirname, 'ethnicities_translated.json')));
const { excludedUniverseLinkingKeyWords } =require('../lib/exclude-from-universe-programmatic-linking');
const InterestCountryCount = require('../models/interest-country-count');
const interestOnboardingCarousel = require('./interest-onboarding-carousel')
const { executeAggregationWithRetry } = require('./retry-aggregate-query');

// i18n instance specifically for interests

const i18n_interests = new I18n({
  directory: path.join(__dirname, 'locales-interests'),
  updateFiles: !!process.env.UPDATE_INTEREST_TRANSLATION_FILES,
  indent: '  ',
});

function getEthnicityTranslation(ethnicity, locale) {
  if(locale == 'zh-Hans') locale = 'zh_Hans'
  if(locale == 'zh-Hant') locale = 'zh_Hant'
  if (translationsForEthnicity[ethnicity] && translationsForEthnicity[ethnicity][locale]) {
    return translationsForEthnicity[ethnicity][locale];
  } else {
   return ethnicity
  }
};

function cleanInterestName(name) {
  // only lowercase alphanumeric (unicode letters and numbers) and underscore
  name = name.toLowerCase();
  name = name.replace(/[^\p{L}\p{M}\p{N}_]/gu, '');
  return name;
}

let allInterests = [];
let allInterestsFormatted = [];
let allInterestsIdMap = {};
let allInterestsNameMap = {};
const languageToInterestsMap = {};
const languageToPopularInterestsMap = {};
const newOnboardingInterests = {};
const newOnboardingInterestsByCategory = {};
let newGamingOnboardingInterests = new Set();
let newAnimeOnboardingInterests = new Set();
let whatsNewMessage = {};

async function loadInterestsFromDatabase() {
  allInterests = await Interest
    .find({
      sortIndex: { $ne: null },
      status: null,
    })
    .sort('sortIndex');
  allInterestsIdMap = allInterests.reduce((map, doc) => {
    map[doc._id] = doc;
    return map;
  }, {});
  allInterestsNameMap = allInterests.reduce((map, doc) => {
    map[doc.interest] = doc;
    return map;
  }, {});
  allInterestsFormatted = allInterests.map(formatInterestObj);

  for (const locale of locales) {
    if (locale) {
      languageToInterestsMap[locale] = allInterests.map((interest) => (
        {
          category: interest.category,
          name: cleanInterestName(i18n_interests.__({ phrase: interest.name, locale })),
        }
      ));
      languageToPopularInterestsMap[locale] = popularInterests.map((name) => (
        cleanInterestName(i18n_interests.__({ phrase: name, locale }))
      ));
    }
  }
}

function formatInterestObj(interest) {
  return {
    _id: interest._id,
    category: interest.category,
    interest: interest.interest,
    name: interest.name,
    allowImages: interest.allowImages,
    numFollowers: interest.numFollowers,
    numQuestions: interest.numQuestions,
    numQuestionsPerLanguage: interest.numQuestionsPerLanguage,
    similar: interest.similar,
  };
}

function getAllInterests(user) {
  if (user && user.versionAtLeast('1.11.55')) {
    return languageToInterestsMap[user.locale] || languageToInterestsMap.en;
  }
  return allInterestsFormatted;
}
function getPopularInterestsForOnboarding(locale) {
  return languageToPopularInterestsMap[locale] || languageToPopularInterestsMap.en;
}

const hasSetIntersection = (setA, setB) => [...setA].some(interest => setB.has(interest));

function shouldIncludeCategoryForOnboarding(category, interestSet, locale) {
  const interestList = interestOnboardingCarousel[category] || [];
  
  if (category === 'fitness' || category === 'kpop') {
    for (const interest of interestList) {
      const translatedInterest = locale === 'en' ? interest : cleanInterestName(i18n_interests.__({ phrase: interest, locale }));
      if (interestSet.has(interest) || interestSet.has(translatedInterest)) {
        return true;
      }
    }
  } else if (category === 'gaming') {
    if (hasSetIntersection(interestSet, newGamingOnboardingInterests)) {
      return true;
    }
  } else if (category === 'anime') {
    if (hasSetIntersection(interestSet, newAnimeOnboardingInterests)) {
      return true;
    }
  }

  return false;
}

function getOnboardingInterestsCarousel(interestNames, locale = 'en') {
  const result = [];
  const interestSet = new Set(interestNames);
  Object.keys(interestOnboardingCarousel).forEach(category => {
    if (shouldIncludeCategoryForOnboarding(category, interestSet, locale)) {
      result.push({
        name: category,
        translated: cleanInterestName(i18n_interests.__({ phrase: category, locale })) || category
      });
    }
  });
  return result;
}

function getOnboardingInterestsEvents(interestNames, locale = 'en') {
  const returnCategoryInterest = {};
  const interestSet = new Set(interestNames);
  Object.keys(interestOnboardingCarousel).forEach(category => {
    if (shouldIncludeCategoryForOnboarding(category, interestSet, locale)) {
      returnCategoryInterest[`added_interest_${category}`] = true;
    }
  });
  return returnCategoryInterest;
}

function getInterest(interestId) {
  return allInterestsIdMap[interestId];
}
function getInterestIdByName(interestName) {
  if (interestName == 'questions') {
    return null;
  }
  const interest = allInterestsNameMap[`#${interestName}`];
  if (!interest) {
    return;
  }
  return interest._id;
}

function formatInterest(interestId, locale) {
  const interest = getInterest(interestId);
  if (!interest) {
    return null;
  }
  return formatInterestObj(interest);
}

function formatInterests(user, locale) {
  const { interests } = user;
  if (!Array.isArray(interests)) {
    return [];
  }

  const formatted = interests.map((id) => formatInterest(id, locale))
    .filter((interest) => interest !== null);

  return formatted;
}

function getInterestNamesFromIds(interestIds) {
  if (!Array.isArray(interestIds) || hasDuplicates(interestIds)) {
    return;
  }
  const interests = interestIds.map((x) => getInterest(x));
  if (interests.some((x) => !x)) {
    return;
  }
  return interests.map((x) => x.name);
}

function cleanInterestNames(interestNames) {
  for (const [incorrect, correct] of constants.getReplacedInterests()) {
    interestNames = interestNames.map((x) => (x == incorrect ? correct : x));
  }
  interestNames = removeDuplicates(interestNames);
  return interestNames;
}

async function validateInterestNames(interestNames) {
  if (
    !Array.isArray(interestNames)
    || hasDuplicates(interestNames)
    || interestNames.some((x) => typeof x !== 'string')
  ) {
    return false;
  }

  const validInterests = await Interest.find({
    name: { $in: interestNames },
    status: null,
  });
  if (validInterests.length != interestNames.length) {
    return false;
  }

  return validInterests;
}

async function addNamesToInterests() {
  const interests = await Interest.find();
  for (const interest of interests) {
    interest.name = interest.interest.replace('#', '');
    await interest.save();
  }
}

async function addNumFollowersToInterests() {
  const interests = await Interest.find();
  for (const interest of interests) {
    const count = await User.countDocuments({ interestNames: interest.name });
    console.log(interest.name, count);
    interest.numFollowers = count;
    interest.numFollowersSortIndex = count + Math.random();
    await interest.save();
  }
}

async function migrateQuestionsInterestName() {
  const questions = mongoose.connection.db.collection('questions');
  const query = { interestName: null };
  const options = {
    projection: {
      _id: 1,
      parent: 1,
    },
  };
  const cursor = questions.find(query, options);
  let i = 0;
  let bulk = Question.collection.initializeUnorderedBulkOp();
  for await (const question of cursor) {
    const filter = { _id: question._id };
    let interestName;
    if (!question.parent) {
      interestName = 'questions';
    } else {
      const interestNames = getInterestNamesFromIds([question.parent]);
      if (interestNames.length != 1) {
        console.log('WARNING');
        continue;
      }
      interestName = interestNames[0];
    }
    const updates = {
      $set: {
        interestName,
      },
    };
    bulk.find(filter).update(updates);
    i++;
    if (i % 100 == 0) {
      const res = await bulk.execute();
      console.log(i, res);
      bulk = Question.collection.initializeUnorderedBulkOp();
    }
  }
  const res = await bulk.execute();
  console.log(i, res);
}

async function migrateUsersInterestName() {
  const users = mongoose.connection.db.collection('users');
  const query = {
    $or: [
      { interestNames: null },
      { 'preferences.interestNames': null },
    ],
  };
  const options = {
    projection: {
      _id: 1,
      interests: 1,
      'preferences.interests': 1,
    },
  };
  const cursor = users.find(query, options);
  let i = 0;
  let bulk = User.collection.initializeUnorderedBulkOp();
  for await (const user of cursor) {
    const filter = { _id: user._id };
    const updates = {
      $set: {
        interestNames: getInterestNamesFromIds(user.interests),
        'preferences.interestNames': getInterestNamesFromIds(user.preferences.interests),
      },
    };
    bulk.find(filter).update(updates);
    i++;
    if (i % 100 == 0) {
      const res = await bulk.execute();
      console.log(i, res);
      bulk = User.collection.initializeUnorderedBulkOp();
    }
  }
  const res = await bulk.execute();
  console.log(i, res);
}

async function getInterestRouteHandler(req, res, next) {
  const query = req.query.name;
  if (!query || typeof query !== 'string') {
    return next(invalidInputError());
  }
  if (query == 'questions') {
    return res.json({
      interest: {
        name: 'questions',
        numFollowers: null,
        similar: null,
      },
    });
  }
  const interest = await Interest.findOne({
    name: query,
    status: null,
  });
  if (!interest) {
    return next(notFoundError());
  }
  res.json({
    noIndex : interest.numQuestions < 5 ? true : false,
    interest: formatInterestObj(interest),
  });
}

async function getSimilarInterestsRouteHandler(req, res, next) {
  const query = req.query.name;
  if (!query || typeof query !== 'string') {
    return next(invalidInputError());
  }
  const interest = await Interest.findOne({
    name: query,
    status: null,
  });
  if (!interest) {
    return next(notFoundError());
  }

  let interests = [];
  if (interest.similar && interest.similar.length > 0) {
    interests = await Interest.find({
      name: { $in: interest.similar },
      status: null,
    });
    interests = interests.map((x) => formatInterestObj(x));
  }

  res.json({
    interests,
  });
}

async function getPopularInterestsRouteHandler(req, res, next) {
  const query = { status: null };
  if (req.query.before) {
    const interest = await Interest.findOne({ name: req.query.before });
    if (interest) {
      query.numFollowersSortIndex = { $lt: interest.numFollowersSortIndex };
    }
  }

  const interests = await Interest
    .find(query, 'interest name numFollowers')
    .sort({ numFollowersSortIndex: -1 })
    .limit(constants.getInterestPageSize());

  res.json({
    interests,
  });
}

async function getSuggestedInterestRouteHandler(req, res, next) {
  const { personality, enneagram, horoscope, moreAboutUser, ethnicities, datingSubPreferences, relationshipStatus, relationshipType, preferences, locale = 'en', sexuality, gender } = req.user;
  let suggestedInterests = [];
  if (personality?.mbti) suggestedInterests.push(personality?.mbti)
  if (enneagram) suggestedInterests.push(enneagram);
  if (horoscope) suggestedInterests.push(i18n_interests.__({ phrase: cleanInterestName(horoscope), locale }));
  if (moreAboutUser?.religion && moreAboutUser.religion != 'Other' ) suggestedInterests.push(translate_frontend(moreAboutUser.religion,locale));
  if (ethnicities?.length) suggestedInterests.push(...ethnicities.map((x)=>getEthnicityTranslation(x,locale)));
  if (preferences?.friends?.length) suggestedInterests.push(translate_frontend('Friends',locale));
  if (preferences?.dating?.length) {
    suggestedInterests.push(translate_frontend('Dating',locale));
    if (relationshipType) suggestedInterests.push(translate_frontend(relationshipType,locale));
    if (datingSubPreferences && datingSubPreferences != 'Undecided') suggestedInterests.push(...datingPreferencesMap[datingSubPreferences].map((datingInterest) => i18n_interests.__({ phrase: datingInterest, locale })));
    if (relationshipStatus && !['Divorced', 'Widowed'].includes(relationshipStatus)) {
      suggestedInterests.push(...relationshipStatusMap[relationshipStatus].map((relationshipInterest) => i18n_interests.__({ phrase: relationshipInterest, locale })));
    }
  }
  if (['Divorced', 'Widowed'].includes(relationshipStatus)) {
    suggestedInterests.push(translate_frontend(relationshipStatus,locale));
  }
  if (sexuality) {
    const sexualityInterestMap = {
      homosexual: {
        lesbian: sexualOrientationMap.lesbian,
        gay: sexualOrientationMap.gay,
        default: sexualOrientationMap.homosexual,
      },
      bisexual: sexualOrientationMap.bisexual,
      pansexual: sexualOrientationMap.pansexual,
      asexual: sexualOrientationMap.asexual,
    };

    if (sexuality === 'homosexual') {
      const genderKey = gender === 'female' ? 'lesbian' : (gender === 'male' ? 'gay' : 'default');
      const interests = sexualityInterestMap.homosexual[genderKey] || [];
      suggestedInterests.push(...interests.map(sexualityInterest => i18n_interests.__({ phrase: sexualityInterest, locale })));
    } else {
      const interests = sexualityInterestMap[sexuality] || [];
      suggestedInterests.push(...interests.map(sexualityInterest => i18n_interests.__({ phrase: sexualityInterest, locale })));
    }
  }
  suggestedInterests = suggestedInterests.map((x) => `${cleanInterestName(x)}`);

  res.json({
    suggestedInterests,
  });
}

async function replaceIncorrectInterest(incorrectInterest, replacementInterest) {
  // unset preferences.interestNames
  // unset preferences.interests

  // interests
  // hiddenInterests

  // unset interestNames

  // rename question.interest

  // get users

  const incorrectInterestDoc = await Interest.findOne({ name: incorrectInterest });
  const incorrectInterestId = incorrectInterestDoc._id;

  const replacementInterestDoc = await Interest.findOne({ name: replacementInterest });
  const replacementInterestId = replacementInterestDoc._id;

  // set incorrect interest to rejected
  incorrectInterestDoc.status = 'rejected';
  await incorrectInterestDoc.save();

  // update each field separately to avoid $pull error on non-array values
  const fields = [
    ['interests', incorrectInterestId, replacementInterestId],
    ['interestNames', incorrectInterest, replacementInterest],
    ['hiddenInterests', incorrectInterest, replacementInterest],
    ['preferences.interests', incorrectInterestId, replacementInterestId],
    ['preferences.interestNames', incorrectInterest, replacementInterest],
  ];

  let numFollowersAdded;

  for (const field of fields) {
    const fieldName = field[0];
    const incorrectValue = field[1];
    const correctValue = field[2];

    // if both are present, then pull the incorrect one
    let res = await User.updateMany(
      { [fieldName]: { $all: [incorrectValue, correctValue] } },
      { $pull: { [fieldName]: incorrectValue } },
    );
    console.log(fieldName, incorrectValue, correctValue, '$pull', res);

    // if only the incorrect one is present, then replace it with the correct one
    res = await User.updateMany(
      { [fieldName]: incorrectValue },
      { $set: { [`${fieldName}.$`]: correctValue } },
    );
    console.log(fieldName, incorrectValue, correctValue, '$set', res);
    if (fieldName == 'interestNames') {
      numFollowersAdded = res.modifiedCount;
    }
  }

  const updateQuestions = await Question.updateMany(
    {
      $or: [
        {
          interestName: incorrectInterest,
        },
        {
          parent: incorrectInterestId,
        },
      ],
    },
    {
      $set: {
        parent: replacementInterestId,
        interestName: replacementInterest,
      },
    },
  );
  console.log('updateQuestions', updateQuestions);

  await Question.updateMany(
    { keywords: incorrectInterest },
    { $pull: { linkedKeywords: incorrectInterest } },
  );
  await Comment.updateMany(
    { keywords: incorrectInterest },
    { $pull: { linkedKeywords: incorrectInterest } },
  );

  replacementInterestDoc.numQuestions += incorrectInterestDoc.numQuestions;
  replacementInterestDoc.numFollowers += numFollowersAdded;
  replacementInterestDoc.lastPostAddedTime = new Date()
  await replacementInterestDoc.save();

  console.log('numQuestionsAdded', incorrectInterestDoc.numQuestions);
  console.log('numFollowersAdded', numFollowersAdded);

  if(replacementInterestDoc && replacementInterestDoc.name && replacementInterestDoc.numQuestions >= 5 && !excludedUniverseLinkingKeyWords.has(replacementInterestDoc.name.toLowerCase())){
    await Question.updateMany(
      { keywords: replacementInterestDoc.name },
      { $addToSet: { linkedKeywords: replacementInterestDoc.name } },
    );
    await Comment.updateMany(
      { keywords: replacementInterestDoc.name },
      { $addToSet: { linkedKeywords: replacementInterestDoc.name } },
    );
  }
}

function escapeRegex(string) {
  return string.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
}

async function autocomplete(query) {
  query = cleanInterestName(query);
  query = escapeRegex(query);

  let interests = [];

  if (query) {
    if(['prod','beta'].includes(process.env.NODE_ENV)) {
      const maxEdits = query.length > 6 ? 2 : 1;
      interests = await executeAggregationWithRetry(Interest, [
        {
          $search: {
            index: "interests",  
            compound: {
              should: [
                {
                  text: {
                    query: query,  
                    path: "name",
                    score: { 
                      boost: { 
                        value: 7 
                      } 
                    },
                  },
                },
                {
                  text: {
                    query: query,
                    path: "name",
                    fuzzy: {
                      maxEdits: maxEdits,
                    },
                    score: { 
                      boost: { 
                        value: 5 
                      } 
                    },
                  },
                },
                {
                  autocomplete: {
                    query: query,  
                    path: "name",     
                    tokenOrder: "sequential",
                    score: {
                      boost: {
                        value: 3 
                      }
                    }
                  }
                },
                {
                  autocomplete: {
                    query: query, 
                    path: "name",     
                    fuzzy: {
                      maxEdits: maxEdits 
                    }
                  }
                }
              ]
            }
          }
        },
        {
          $match: {
            status: null,
          }
        },
        {
          $limit: 10,
        }
      ]);
    } else {
      interests = await Interest
      .find({
        name: { $regex: `^${query}` },
        status: null,
      })
      .limit(10);
    }
    interests = interests.map((x) => formatInterestObj(x));
  }

  return interests;
}

async function autocompleteRouteHandler(req, res, next) {
  let { query } = req.query;
  if (!query || typeof query !== 'string') {
    return next(invalidInputError());
  }

  const interests = await autocomplete(query);

  res.json({
    interests,
  });
}

async function shouldInterestBeApproved(name) {
  if (name.includes('_')) {
    return false;
  }

  // reject bold/italic unicode fonts
  // https://www.compart.com/en/unicode/block/U+1D400
  const unicodeValue = name.codePointAt(0);
  if (119808 <= unicodeValue && unicodeValue <= 120831) {
    return false;
  }

  const maxEdits = name.length > 5 ? 2 : 1;
  let similarInterests = await executeAggregationWithRetry(Interest, [
    {
      $search: {
        index: 'interests',
        autocomplete: {
          query: name,
          path: 'name',
          fuzzy: {"maxEdits": maxEdits, "prefixLength": 0, "maxExpansions": 50},
        },
      },
    },
    {
      $match: {
        status: null,
      }
    },
    {
      $limit: 100,
    },
    {
      $project: {
        _id: 0,
        name: 1,
      }
    },
  ]);
  similarInterests = similarInterests.map(x => x.name);

  let language;
  const detectLanguageResult = await detectLanguage(name);
  if (detectLanguageResult && detectLanguageResult.language != 'und') {
    language = detectLanguageResult.language;
  }

  const approved = await openai.isInterestAppropriate(name, similarInterests, language);
  return approved;
}

saveCountsBasedOnCountryForInterest = async function(interestName, category) {
  const BATCH_SIZE = 1000;
  let cursor;
  let firstPipeline = [
    {
      $match: {
        interestNames: interestName,
        'ipData.country': { $exists: true },
        locale: { $exists: true }
      }
    }
  ]
  if (process.env.NODE_ENV == 'prod' || process.env.NODE_ENV == 'beta') {
  firstPipeline = [
    {
    $search: {
      index: "users",
              compound: {
                must: [
                  {
                    text: {
                      path: 'interestNames',
                      query: interestName,
                    }
                  },
                ],
              }
    }
  },
  {
    $match: {
      "locale": { $exists: true },
      "ipData.country": { $exists: true }
    }
  }]
}
  try {
    cursor = User.aggregate([
      ...firstPipeline,
      {
        $group: {
          _id: {
            country: '$ipData.country',
            locale: '$locale'
          },
          count: { $sum: 1 }
        }
      },
      {
        $project: {
          _id: 0,
          country: '$_id.country',
          locale: '$_id.locale',
          count: 1
        }
      }
    ])
    .cursor({ batchSize: BATCH_SIZE })

    const bulkOps = [];

    for await (const result of cursor) {
      bulkOps.push({
        updateOne: {
          filter: { interestName: interestName, country: result.country, locale: result.locale },
          update: {
            $set: {
              category: category,
              count: result.count
            }
          },
          upsert: true
        }
      });

      if (bulkOps.length >= BATCH_SIZE) {
        await InterestCountryCount.bulkWrite(bulkOps);
        bulkOps.length = 0;
      }
    }

    if (bulkOps.length > 0) {
      await InterestCountryCount.bulkWrite(bulkOps);
    }
  } catch (err) {
    console.error('Error in save count for interest in respective to country:', err);
  } finally {
    if (cursor) {
      await cursor.close();
    }
  }
};

function loadNewOnboardingInterests() {
  const fileContent = fs.readFileSync(path.join(__dirname, 'onboarding_interests.csv'), 'utf-8');
  const records = parse.parse(fileContent, {
    columns: true,
    skip_empty_lines: true,
  });
  const tempData = {};

  records.forEach(row => {
    const category = row.category.trim() === 'Games' ? 'Gaming' : row.category.trim();
    const interestName = row.name.trim();
    const numFollowers = parseInt(row.numFollowers, 10);

    if (!tempData[category]) {
      tempData[category] = [];
    }

    tempData[category].push({ interestName, numFollowers });
  });

  const groupedInterests = Object.keys(tempData).map(category => ({
    category,
    interestNames: Array.from(
      new Set(
        tempData[category]
          .sort((a, b) => b.numFollowers - a.numFollowers)
          .map(item => item.interestName)
      )
    ),
  }))
  const allInterestNamesGaming = [];
  const allInterestNamesAnime = []; 

  for (const locale of locales) {
    if (locale) {
      newOnboardingInterests[locale] = translateGroupedInterests(groupedInterests, locale);
      allInterestNamesGaming.push(...(newOnboardingInterests[locale]?.find(item => item.category === 'Gaming')?.interestNames || []));
      allInterestNamesAnime.push(...(newOnboardingInterests[locale]?.find(item => item.category === 'Anime')?.interestNames || []));
      allInterestNamesAnime.push(cleanInterestName(i18n_interests.__({ phrase: 'anime', locale }))) // as anime is not there in the onboarding csv file

    }
  }
  newGamingOnboardingInterests = new Set(allInterestNamesGaming);
  newAnimeOnboardingInterests = new Set(allInterestNamesAnime);
  Object.keys(newOnboardingInterests).forEach(locale => {
    newOnboardingInterestsByCategory[locale] = {};
    newOnboardingInterests[locale].forEach(group => {
      const category = group.category.toLowerCase();
      const interestNames = group.interestNames;
      newOnboardingInterestsByCategory[locale][category] = [...new Set(interestNames)];
    });
  });
}

function translateGroupedInterests(groupedData, locale) {
  // Category names translations are maintained in frontend
  return groupedData.map(group => ({
    category: group.category,
    interestNames: Array.from(
      new Set(
        group.interestNames.map(interest =>
          cleanInterestName(i18n_interests.__({ phrase: interest, locale }))
        )
      )
    ),
  }));
}

function getAllOnboardingInterests(user) {
  return newOnboardingInterests[user.locale] || newOnboardingInterests.en;
}

function getCategoryOnboardingInterests(locale, category) {
  return newOnboardingInterestsByCategory[locale][category]
}

function reorderFirstCategory(response, categoryKey) {
  const categoryIndex = response.findIndex(item => item.category === categoryKey);
  if (categoryIndex > 0) {
    const [categoryData] = response.splice(categoryIndex, 1);
    response.unshift(categoryData);
  }

  return response;
}

if (Object.keys(newOnboardingInterests).length === 0) {
  try {
    console.log('Loading new onboarding interests');
    loadNewOnboardingInterests();
  } catch (err) {
    console.log('Error in loading new onboarding interests:', err);
  }
}

function loadWhatsNewMessages() {
  const filePath = path.join(__dirname, 'whatsnew_messages.csv');
  const fileContent = fs.readFileSync(filePath, 'utf-8');
  const records = parse.parse(fileContent, {
    columns: true,
    skip_empty_lines: true,
  });

  // Set the latest record
  whatsNewMessage = records[records.length - 1] || {};
}

try {
  if (!Object.keys(whatsNewMessage).length) {
    console.log('Loading latest "What\'s New" message...');
    loadWhatsNewMessages();
  }
} catch (err) {
  console.log('Error loading "What\'s New" message:', err.message);
}

module.exports = {
  loadInterestsFromDatabase,
  getAllInterests,
  getPopularInterestsForOnboarding,
  getInterest,
  getInterestIdByName,
  formatInterest,
  formatInterests,
  formatInterestObj,
  getInterestNamesFromIds,
  validateInterestNames,
  addNamesToInterests,
  migrateUsersInterestName,
  migrateQuestionsInterestName,
  addNumFollowersToInterests,
  getPopularInterestsRouteHandler,
  getSuggestedInterestRouteHandler,
  replaceIncorrectInterest,
  cleanInterestName,
  languageToInterestsMap,
  cleanInterestNames,
  getInterestRouteHandler,
  autocompleteRouteHandler,
  getSimilarInterestsRouteHandler,
  shouldInterestBeApproved,
  getEthnicityTranslation,
  i18n_interests,
  saveCountsBasedOnCountryForInterest,
  getOnboardingInterestsCarousel,
  getOnboardingInterestsEvents,
  loadNewOnboardingInterests,
  getAllOnboardingInterests,
  getCategoryOnboardingInterests,
  reorderFirstCategory,
  whatsNewMessage,
};
