const lookup = require('country-code-lookup');
const locationLib = require('../lib/location');

  const COUNTRY_TO_NATIONALITY = {
    Africa: 'African',
    Asia: 'Asian',
    Europe: 'European',
    'North America': 'North American',
    Oceania: 'Oceanian',
    'South America': 'South American',
    Algeria: 'Algerian',
    Angola: 'Angolan',
    Benin: 'Beninese',
    Botswana: 'Batswana',
    'Burkina Faso': 'Burkinabé',
    Burundi: 'Burundian',
    Cameroon: 'Cameroonian',
    'Cape Verde': 'Cape Verdean',
    'Central African Republic': 'Central African',
    Chad: 'Chadian',
    Comoros: 'Comoran',
    'Congo (Republic and DRC)': 'Congolese',
    "Côte d'Ivoire": 'Ivorian',
    Djibouti: 'Djiboutian',
    Egypt: 'Egyptian',
    'Equatorial Guinea': 'Equatorial Guinean',
    Eritrea: 'Eritrean',
    Eswatini: 'Swazi',
    Ethiopia: 'Ethiopian',
    Gabon: 'Gabonese',
    Gambia: 'Gambian',
    Ghana: 'Ghanaian',
    Guinea: 'Guinean',
    'Guinea-Bissau': 'Bissau-Guinean',
    Kenya: 'Kenyan',
    Lesotho: 'Basotho',
    Liberia: 'Liberian',
    Libya: 'Libyan',
    Madagascar: 'Malagasy',
    Malawi: 'Malawian',
    Mali: 'Malian',
    Mauritania: 'Mauritanian',
    Mauritius: 'Mauritian',
    Morocco: 'Moroccan',
    Mozambique: 'Mozambican',
    Namibia: 'Namibian',
    Niger: 'Nigerien',
    Nigeria: 'Nigerian',
    Rwanda: 'Rwandan',
    'Sao Tome and Principe': 'Santomean',
    Senegal: 'Senegalese',
    Seychelles: 'Seychellois',
    'Sierra Leone': 'Sierra Leonean',
    Somalia: 'Somali',
    'South Africa': 'South African',
    'South Sudan': 'South Sudanese',
    Sudan: 'Sudanese',
    Tanzania: 'Tanzanian',
    Togo: 'Togolese',
    Tunisia: 'Tunisian',
    Uganda: 'Ugandan',
    'Western Sahara': 'Sahrawi',
    Zambia: 'Zambian',
    Zimbabwe: 'Zimbabwean',
    Afghanistan: 'Afghan',
    Armenia: 'Armenian',
    Azerbaijan: 'Azerbaijani',
    Bahrain: 'Bahraini',
    Bangladesh: 'Bangladeshi',
    Bhutan: 'Bhutanese',
    Brunei: 'Bruneian',
    Cambodia: 'Cambodian',
    China: 'Chinese',
    Georgia: 'Georgian',
    'Hong Kong': 'Hongkongese',
    India: 'Indian',
    Indonesia: 'Indonesian',
    Iran: 'Iranian',
    Iraq: 'Iraqi',
    Israel: 'Israeli',
    Japan: 'Japanese',
    Jordan: 'Jordanian',
    Kazakhstan: 'Kazakhstani',
    Kuwait: 'Kuwaiti',
    Kyrgyzstan: 'Kyrgyzstani',
    Laos: 'Laotian',
    Lebanon: 'Lebanese',
    Malaysia: 'Malaysian',
    Maldives: 'Maldivian',
    Mongolia: 'Mongolian',
    Myanmar: 'Burmese',
    Nepal: 'Nepali',
    'North Korea': 'North Korean',
    Oman: 'Omani',
    Pakistan: 'Pakistani',
    Palestine: 'Palestinian',
    Philippines: 'Filipino',
    Qatar: 'Qatari',
    'Saudi Arabia': 'Saudi Arabian',
    Singapore: 'Singaporean',
    'South Korea': 'South Korean',
    'Sri Lanka': 'Sri Lankan',
    Syria: 'Syrian',
    Taiwan: 'Taiwanese',
    Tajikistan: 'Tajikistani',
    Thailand: 'Thai',
    'Timor-Leste': 'Timorese',
    Turkmenistan: 'Turkmen',
    'United Arab Emirates': 'Emirati',
    Uzbekistan: 'Uzbekistani',
    Vietnam: 'Vietnamese',
    Yemen: 'Yemeni',
    Albania: 'Albanian',
    Andorra: 'Andorran',
    Austria: 'Austrian',
    Belarus: 'Belarusian',
    Belgium: 'Belgian',
    'Bosnia and Herzegovina': 'Bosnian',
    Bulgaria: 'Bulgarian',
    Croatia: 'Croatian',
    Cyprus: 'Cypriot',
    Czechia: 'Czech',
    Denmark: 'Danish',
    Estonia: 'Estonian',
    Finland: 'Finnish',
    France: 'French',
    Germany: 'German',
    Greece: 'Greek',
    Hungary: 'Hungarian',
    Iceland: 'Icelandic',
    Ireland: 'Irish',
    Italy: 'Italian',
    Kosovo: 'Kosovar',
    Latvia: 'Latvian',
    Liechtenstein: 'Liechtensteiner',
    Lithuania: 'Lithuanian',
    Luxembourg: 'Luxembourger',
    Malta: 'Maltese',
    Moldova: 'Moldovan',
    Monaco: 'Monegasque',
    Montenegro: 'Montenegrin',
    Netherlands: 'Dutch',
    'North Macedonia': 'Macedonian',
    Norway: 'Norwegian',
    Poland: 'Polish',
    Portugal: 'Portuguese',
    Romania: 'Romanian',
    Russia: 'Russian',
    'San Marino': 'Sammarinese',
    Serbia: 'Serbian',
    Slovakia: 'Slovak',
    Slovenia: 'Slovenian',
    Spain: 'Spanish',
    Sweden: 'Swedish',
    Switzerland: 'Swiss',
    Turkey: 'Turkish',
    UK: 'British',
    Ukraine: 'Ukrainian',
    'Vatican City': 'Vatican',
    Yugoslavia: 'Yugoslav',
    Canada: 'Canadian',
    US: 'American',
    'Antigua and Barbuda': 'Antiguan and Barbudan',
    Bahamas: 'Bahamian',
    Barbados: 'Bajan',
    Belize: 'Belizean',
    Bermuda: 'Bermudian',
    'Cayman Islands': 'Caymanian',
    'Costa Rica': 'Costa Rican',
    Cuba: 'Cuban',
    Dominica: 'Dominican',
    'Dominican Republic': 'Dominican',
    'El Salvador': 'Salvadoran',
    'French Polynesia': 'French Polynesian',
    Grenada: 'Grenadian',
    Guatemala: 'Guatemalan',
    Haiti: 'Haitian',
    Honduras: 'Honduran',
    Jamaica: 'Jamaican',
    Mexico: 'Mexican',
    Montserrat: 'Montserratian',
    'Netherlands Antilles': 'Dutch Caribbean',
    Nicaragua: 'Nicaraguan',
    Panama: 'Panamanian',
    'Saint Kitts and Nevis': 'Kittitian and Nevisian',
    'Saint Lucia': 'Saint Lucian',
    'Saint Vincent and the Grenadines': 'Vincentian',
    'Trinidad and Tobago': 'Trinidadian and Tobagonian',
    Australia: 'Australian',
    Fiji: 'Fijian',
    Kiribati: 'I-Kiribati',
    'Marshall Islands': 'Marshallese',
    Micronesia: 'Micronesian',
    Nauru: 'Nauruan',
    'New Zealand': 'New Zealander',
    Palau: 'Palauan',
    'Papua New Guinea': 'Papua New Guinean',
    Samoa: 'Samoan',
    'Solomon Islands': 'Solomon Islander',
    Tonga: 'Tongan',
    Tuvalu: 'Tuvaluan',
    Vanuatu: 'Vanuatuan',
    Argentina: 'Argentine',
    Aruba: 'Aruban',
    Bolivia: 'Bolivian',
    Brazil: 'Brazilian',
    Chile: 'Chilean',
    Colombia: 'Colombian',
    'Curaçao': 'Curaçaoan',
    Ecuador: 'Ecuadorian',
    Guyana: 'Guyanese',
    Paraguay: 'Paraguayan',
    Peru: 'Peruvian',
    Suriname: 'Surinamese',
    Uruguay: 'Uruguayan',
    Venezuela: 'Venezuelan'
}

const group1Names = [
  'Philippines', 'Bolivia', 'Ukraine', 'Morocco', 'Egypt', 'Laos',
  'Vietnam', 'India', 'Comoros', 'Botswana', 'Armenia', 'Georgia',
  'Democratic Republic of the Congo', 'Senegal', 'Sri Lanka', 'Nigeria',
  'Bangladesh', 'Kenya', 'Pakistan', 'Cameroon', 'Cambodia',
  'Ethiopia', 'Sudan', 'Afghanistan', 'Liberia', 'Indonesia',
  'Sierra Leone', 'Togo', 'Haiti', 'Burundi', 'Benin', 'Rwanda',
  'Chad', 'Zambia', 'Mali', 'Burkina Faso', 'Niger', 'Madagascar',
  'Nepal', 'Yemen', 'Ghana', 'Angola', 'Uganda', 'Tanzania',
]
const group1 = group1Names.map((x) => lookup.byCountry(x).iso2);

function getCountryGroup(countryCode) {
  if (!countryCode) {
    return undefined;
  }
  if (group1.includes(countryCode)) {
    return 1;
  }
  return 0;
}

function shouldRemoveCountryFilter(user) {
  const country = ['India', 'Philippines'].find(x => [user.actualCountry, user.ipData?.country, locationLib.getCountryNameFromTimezone(user.timezone)].includes(x));
  if (country == 'India') {
    return 'IN';
  }
  if (country == 'Philippines') {
    return 'PH';
  }
  return;
}

module.exports = {
  COUNTRY_TO_NATIONALITY,
  getCountryGroup,
  group1,
  group1Names,
  shouldRemoveCountryFilter,
};
