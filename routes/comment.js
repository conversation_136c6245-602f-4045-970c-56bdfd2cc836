const express = require('express');
const mongoose = require('mongoose');
const asyncHandler = require('express-async-handler');
const cmp = require('semver-compare');
const moment = require('moment');

const router = express.Router();
const {
  notFoundError, forbiddenError, badRequestError, conflictError, applicationError, invalidInputError,
} = require('../lib/http-errors');
const s3 = require('../lib/s3');
const { findUser } = require('../middleware/user');
const {
  verifyOwnComment, verifyParentId, verifyRootParentId, findDuplicateComment,
} = require('../middleware/comment');
const userMiddleware = require('../middleware/user');
const { formatProfile } = require('../lib/chat');
const { isValidGif } = require('../lib/gif');
const { containsBannedPostKeywords } = require('../lib/report-constants');
const admin = require('../config/firebase-admin');
const constants = require('../lib/constants');
const { pageSize } = require('../lib/constants');
const {
  getPosts, updateNotification, getLikes, updateQuestionScore, updateCommentScore, likePost, unlikePost, reportPost, incrementNumComments, formatComment, getComment, getCommentContext, getSingleCommentById, awardPost, addHiveFlag,
} = require('../lib/social');
const socialLib = require('../lib/social');
const socketLib = require('../lib/socket');
const interestLib = require('../lib/interest');
const coinsLib = require('../lib/coins');
const actionLib = require('../lib/action');
const { translate } = require('../lib/translate');

const User = require('../models/user');
const Action = require('../models/action');
const Block = require('../models/block');
const Question = require('../models/question');
const Comment = require('../models/comment');
const ProfileVote = require('../models/profile-vote');
const Profile = require('../models/profile');
const WebPage = require('../models/web-page');
const LanguageMismatch = require('../models/language-mismatch');
const { onPostComments } = require('../lib/coins');

const { validMbti } = require('../lib/personality');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');
const { languageCodes } = require('../lib/languages');
const { isLanguageMismatch } = require('../lib/language-moderation');
const { isVideoInappropriate } = require('../lib/video-moderation');
const openai = require('../lib/openai');
const PostReport = require('../models/post-report');
const { shadowBan, unban, hasUserReportedFieldValue } = require('../lib/report');
const { DateTime } = require('luxon');
const databaseLib = require('../lib/database');
const { convertHls } = require('../lib/mediaconvert');
const { ImageModerationService } = require('../lib/image-moderation');
const { transcribeAudio } = require('../lib/deepgram');
const { isMatched } = require('../lib/common')
const { isAppUser } = require('../lib/basic')

module.exports = function () {
  router.get('/', asyncHandler(getComment));
  router.get('/v2', asyncHandler(getComment));
  router.get('/context', asyncHandler(getCommentContext));
  router.get('/context/v2', asyncHandler(getCommentContext));
  router.get('/single', asyncHandler(getSingleCommentById));


  async function updateProfileHasComment(user,profileId){

    {
      const updateProfile = await Profile.updateOne(
        {
          _id: profileId,
          hasComment: { $ne: true },
        },
        {
          $set: {
            hasComment: true,
          },
        },
      );
      if (updateProfile.modifiedCount < 1) {
        return;
      };
    }

    //grant coins to user.
    const rewards = await coinsLib.onProfileComment(user);
    socketLib.sendCoinRewards(user._id, rewards);

  }

  router.post('/', userMiddleware.checkVerified, verifyParentId, verifyRootParentId, asyncHandler(findDuplicateComment), asyncHandler(async (req, res, next) => {
    if (req.body.text && req.body.text.length > 10000) {
      return next(invalidInputError('Character limit exceeded'));
    }
    if (req.body.gif && !isValidGif(req.body.gif)) {
      return next(invalidInputError());
    }

    /*
    if (req.body.postedAnonymously) {
      if(!req.user.versionAtLeast('1.13.74')) return next(invalidInputError('comment anonymously not allowed'));
      if(!isAppUser(req.user)) return next(invalidInputError('comment anonymously not allowed from web'));
      if(req.parentType === 'profile') return next(invalidInputError('comment anonymously not allowed for profile comment'));
      if(!req.user.anonymousProfileNickname) return next(invalidInputError('Anonymous profile not found'));
    }
    */

    let language;
    let interestName;

    let parentPost = req.parent;
    let parentId = parentPost._id;
    const postRepliedTo = parentPost;
    let repliedTo = null;
    let depth;
    let vote;
    let updateHasComment = false;
    let notifyUser = true;
    let repliedToIsAnonymous

    switch (req.parentType) {
    case 'profile':
      if (req.body.vote && (req.body.vote.mbti || req.body.vote.enneagram || req.body.vote.horoscope)) {
        vote = req.body.vote;
        if (vote.mbti === undefined
            && vote.enneagram === undefined
            && vote.horoscope === undefined) {
          return next(invalidInputError());
        }
        if ((vote.mbti != undefined && !validMbti.includes(vote.mbti))
          || (vote.enneagram != undefined && !enneagrams.includes(vote.enneagram))
          || (vote.horoscope != undefined && !horoscopes.includes(vote.horoscope))) {
          return next(invalidInputError());
        }

        vote = {
          mbti: vote.mbti || undefined,
          enneagram: vote.enneagram || undefined,
          horoscope: vote.horoscope || undefined,
        };
      } else if (!req.body.text) {
        return next(invalidInputError());
      }
      if(!req.profile.hasComment){
        updateHasComment=true;
      }
      depth = 1;
      break;
    case 'question':
      language = req.question.language;
      interestName = req.question.interestName;
      depth = 1;
      break;
    case 'webPage':
      depth = 1;
      break;
    case 'comment':
      depth = parentPost.depth + 1;
      language = parentPost.language;
      interestName = parentPost.interestName;
      // if replying to a depth 2 comment, convert it to a depth 2 comment
      if (parentPost.depth >= 2) {
        parentId = parentPost.parent;
        depth = 2;
        repliedTo = parentPost.createdBy;
      }
      if(parentPost.postedAnonymously){
        repliedToIsAnonymous = true
      }
      break;
    }

    // check if user was blocked
    if (parentPost && parentPost.createdBy && parentPost.createdBy._id) {
      const blocked = await Block.isEitherBlocked(req.user._id, parentPost.createdBy._id);
      if (blocked) {
        if (interestName === 'questions') {
          notifyUser = false;
        } else {
          return next(forbiddenError());
        }
      }
    }

    // no notifications for profile comments since app can't handle them
    const parentPostAuthor = parentPost && !req.profile ? parentPost.createdBy : null;

    // if replying to self, don't @yourself
    if (repliedTo && repliedTo._id == req.user._id) {
      repliedTo = null;
    }

    let banned;
    let bannedReason;

    // check language
    let detectedLanguage, detectedLanguageConfidence, languageMismatch, allow;
    if (req.question && req.body.text) {
      const textSample = req.body.text.trim().substring(0, 100);
      const targetLanguage = req.question.language;
      ({ detectedLanguage, detectedLanguageConfidence, languageMismatch, allow } = await isLanguageMismatch(textSample, targetLanguage, req.user));
      if (!allow) {
        if (req.body.checkLanguage) {
          await LanguageMismatch.create({
            user: req.uid,
            type: 'comment',
            text: req.body.text,
            targetLanguage,
            detectedLanguage,
            confidence: detectedLanguageConfidence,
          });
          return next(conflictError());
        } else {
          banned = true;
          bannedReason = 'language mismatch';
        }
      }
    }

    if (req.foundDuplicateComment) {
      banned = true;
      bannedReason = 'duplicate';
    } else {
      const keyword = containsBannedPostKeywords(req.body.text, language);
      if (keyword) {
        banned = true;
        bannedReason = `keyword: ${keyword}`;
      }
    }

    let mentionedUsersText;
    if (req.body.mentionedUsersText) {
      mentionedUsersText = await socialLib.validateMentionedUsers(req.body.mentionedUsersText);
    }

    const newComment = new Comment({
      // The date sent by frontend is missing timezone, ignore for now
      // "2021-07-03 10:43:10.334174"
      // createdAt: req.body.createdAt,
      createdBy: req.user,
      question: req.question ? req.question._id : undefined,
      profile: req.profile ? req.profile._id : undefined,
      webPage: req.webPage ? req.webPage._id : undefined,
      text: req.body.text,
      vote,
      gif: req.body.gif,
      parent: parentId,
      repliedTo,
      postRepliedTo,
      depth,
      numComments: 0,
      usersThatLiked: [],
      numLikes: 0,
      usersThatReported: [],
      isDeleted: false,
      isEdited: false,
      banned,
      bannedReason,
      language,
      interestName,
      mentionedUsersText,
      detectedLanguage,
      detectedLanguageConfidence,
      languageMismatch,
      // ...( req.body.postedAnonymously && { postedAnonymously: true }),
      repliedToIsAnonymous
    });

    if (req.body.aspectRatio && req.body.aspectRatio !== null) {
      newComment.aspectRatio = req.body.aspectRatio;
    }

    if (!req.user.shadowBanned) {
      await addHiveFlag(newComment, req.body.text);
    }

    const savedComment = await newComment.save();
    if(savedComment && savedComment.profile) {
      await Profile.updateOne({ _id: savedComment.profile }, { $set: { lastUpdated: new Date() } });
    }
    if (
      req.question
      && req.question.interestName == 'questions'
      && moment().diff(req.question.createdAt, 'days') < 1
      && !req.user.currentDayMetrics.freeSwipeFromQodReceived
      && !req.user.metrics.freeSwipeReceivedForQods.includes(req.question._id)
    ) {
      req.user.currentDayMetrics.freeSwipeFromQodReceived = true;
      req.user.currentDayMetrics.numAdditionalSwipes += 1;
      req.user.metrics.freeSwipeReceivedForQods.push(req.question._id);
      while (req.user.metrics.freeSwipeReceivedForQods.length > 5) {
        req.user.metrics.freeSwipeReceivedForQods.shift();
      }
    }

    if(savedComment.postedAnonymously){
      req.user.metrics.numCommentsPostedAnonymously >= 0 ? req.user.metrics.numCommentsPostedAnonymously += 1 : req.user.metrics.numCommentsPostedAnonymously = 1;
    }else{
      req.user.metrics.numComments += 1;
    }

    await req.user.save();

    // update votes
    let toUpdateVote;
    if (vote) {
      const oldVote = {};
      let newVote = {};
      const incQuery = {
      };
      let profileVote = await ProfileVote.findOne({
        from: req.user._id,
        to: req.profile._id,
      });
      if (profileVote) {
        if (vote.mbti && vote.mbti != profileVote.mbti) {
          oldVote.mbti = profileVote.mbti;
          profileVote.mbti = vote.mbti;
          newVote.mbti = vote.mbti;
          toUpdateVote = true;
        }
        if (vote.enneagram && vote.enneagram != profileVote.enneagram) {
          oldVote.enneagram = profileVote.enneagram;
          profileVote.enneagram = vote.enneagram;
          newVote.enneagram = vote.enneagram;
          toUpdateVote = true;
        }
        if (vote.horoscope && vote.horoscope != profileVote.horoscope) {
          oldVote.horoscope = profileVote.horoscope;
          profileVote.horoscope = vote.horoscope;
          newVote.horoscope = vote.horoscope;
          toUpdateVote = true;
        }
        profileVote.mbti = vote.mbti || profileVote.mbti;
        profileVote.enneagram = vote.enneagram || profileVote.enneagram;
        profileVote.horoscope = vote.horoscope || profileVote.horoscope;
        await profileVote.save();
      } else {
        toUpdateVote = true;
        profileVote = new ProfileVote({
          from: req.user._id,
          to: req.profile._id,
          ...vote,
        });
        await profileVote.save();
        newVote = vote;
        incQuery['vote.totalCount'] = 1;
        socialLib.incrementKarma(req.user._id, constants.PROFILE_COMMENT_KARMA_REWARD);
      }

      // Update Profile Vote Stats
      if (toUpdateVote) {
        for (const i of (req.profile.birthday ? ['mbti', 'enneagram'] : ['mbti', 'enneagram', 'horoscope'])) {
          if (oldVote[i]) {
            incQuery[`vote.${i}.${oldVote[i]}`] = -1;
          }
          if (newVote[i]) {
            incQuery[`vote.${i}.${newVote[i]}`] = 1;
          }
        }
        await Profile.updateOne({ _id: req.profile._id }, { $inc: incQuery });
      }
    }
    if (
      req.parentType == 'question'
      && req.user.versionAtLeast('1.11.58')
      && req.question.createdBy
      && (req.user._id != req.question.createdBy._id)
      && req.user.currentDayMetrics.karmaFromLikingPosts < 20
      && req.question.numComments < 10
    ) {
      // karma award for early comments
      if (req.question.numComments == 0) {
        await socialLib.incrementKarma(req.user._id, 2, req.question.createdBy._id);
      } else if (req.question.numComments < 10) {
        await socialLib.incrementKarma(req.user._id, 1, req.question.createdBy._id);
      }

      const user = req.user;
      user.metrics.karmaFromLikingPosts += 1;
      user.currentDayMetrics.karmaFromLikingPosts += 1;
      await user.save();
    }
    if (
      req.parentType == 'comment'
      && req.user.versionAtLeast('1.11.73')
      && parentPost.createdBy
      && req.user._id != parentPost.createdBy._id
      && req.user.currentDayMetrics.karmaFromLikingPosts < 20
      && parentPost.numComments == 0
    ) {
      // karma award for first comment
      await socialLib.incrementKarma(req.user._id, 1, parentPost.createdBy._id);

      const user = req.user;
      user.metrics.karmaFromLikingPosts += 1;
      user.currentDayMetrics.karmaFromLikingPosts += 1;
      await user.save();
    }

    socketLib.sendCoinRewards(
      req.user._id,
      await onPostComments(req.user),
    );

    if (savedComment.question) {
      await incrementNumComments(
        Question,
        updateQuestionScore,
        savedComment.question,
        req.user._id,
        savedComment.postedAnonymously
      );
      const question = req.question;
      const hour = moment().diff(question.createdAt, 'hours');
      if (hour >= 0 && hour <= 23) {
        question.hourlyEngagement[hour].numComments += 1;
        await question.save();
      }
    }

    if (savedComment.webPage) {
      await WebPage.updateOne(
        { _id: savedComment.webPage },
        { $inc: { numComments: 1 } },
      );
    }

    // to increment numComments of parent comments
    if (depth > 1) {
      await incrementNumComments(
        Comment,
        updateCommentScore,
        savedComment.parent,
        req.user._id,
        savedComment.postedAnonymously
      );
    }

    // increment users numComments of depth 1  parent
    if (!parentPost._id.equals(savedComment.parent)) {
      await incrementNumComments(
        Comment,
        updateCommentScore,
        parentPost._id,
        req.user._id,
        savedComment.postedAnonymously
      );
    }

    if(updateHasComment){
      updateProfileHasComment(req.user, req.profile._id);
    }

    // notify mentioned users
    if (mentionedUsersText
        && !savedComment.banned
        && !req.user.shadowBanned
        && req.question) {

      const commentData = {
        _id: savedComment._id,
        question: savedComment.question,
        parent: savedComment.parent,
        postRepliedTo: savedComment.postRepliedTo,
        interestName: req.question.interestName,
      };
      const data = { comment: JSON.stringify(commentData) };

      await socialLib.notifyMentionedUsers(mentionedUsersText, savedComment, 'comment', data, req.user, savedComment.text, parentPost);
    }

    // send notifications for forum comments only
    if (!savedComment.banned
        && !req.user.shadowBanned
        && parentPostAuthor
        && !(!parentPostAuthor.versionAtLeast('1.13.74') && savedComment.postedAnonymously)
        && req.user._id != parentPostAuthor._id
        && req.question) {

      const commentData = {
        _id: savedComment._id,
        question: savedComment.question,
        parent: savedComment.parent,
        postRepliedTo: savedComment.postRepliedTo,
        interest: interestLib.formatInterest(req.question.parent, parentPostAuthor.locale),
        interestName: req.question.interestName,
      };
      const data = { comment: JSON.stringify(commentData) };

      if (req.parentType == 'question') {
        parentPost = await Question.findById(parentPost._id);
      } else {
        parentPost = await Comment.findById(parentPost._id);

        // check if comment is visible to the comment author
        const query = { _id: parentPost._id };
        const posts = await getPosts(
          parentPostAuthor,
          null,
          null,
          query,
          null,
          null,
          Comment,
          formatComment,
          true,
        );
        if (posts.length == 0) {
          parentPost = null;
        }
      }

      if (parentPost && notifyUser) {
        await updateNotification(
          parentPost.createdBy,
          parentPost,
          req.parentType,
          'reply',
          JSON.stringify(data),
          savedComment.postedAnonymously ? null : req.user,
          parentPost.numUsersThatCommented,
        );

        let category, analyticsLabel;
        if(await isMatched(parentPostAuthor._id, req.user._id)){
          category = 'commentRepliesMatches'
          analyticsLabel = 'new-comment-on-post-matches'
        }else{
          category = 'commentRepliesOtherSouls'
          analyticsLabel = 'new-comment-on-post-other-souls'
        }

        let commenter = req.user.firstName
        if(savedComment.postedAnonymously){
          commenter = req.user.anonymousProfileNickname
        }

        let author = parentPostAuthor.firstName
        if(parentPost.postedAnonymously){
          author = parentPostAuthor.anonymousProfileNickname
        }

        admin.sendNotification(
          parentPostAuthor,
          category,
          req.question.title || req.question.text,
          `${commenter}: @${author} ${savedComment.text}`,
          data,
          parentPost._id,
          'social',
          analyticsLabel,
        );
      }
    }
    if (toUpdateVote) {
      await databaseLib.updateProfileDetailsBefore(req.profile.id);
    }
    res.json(formatComment(newComment, req.user));
    await socialLib.moderatePostUsingOpenai(newComment.text, req.question, newComment);
    if (toUpdateVote) {
      await databaseLib.updateProfileDetailsAfter(req.profile.id);
    }
    await Comment.updateSearchFields(savedComment._id);
  }));

  router.delete('/', findUser, asyncHandler(async (req, res, next) => {
    const comment = await Comment.findById(req.query.commentId);
    if (!comment || comment.createdBy != req.uid) {
      return next(notFoundError());
    }
    await comment.deleteOne();

    // check whether there are any other comments from this user
    const otherComment = await Comment.findOne({ parent: comment.parent, createdBy: comment.createdBy });
    if (!otherComment) {
      await Question.updateOne(
        {
          _id: comment.parent,
          usersThatCommented: comment.createdBy,
        },
        {
          $inc: { numUsersThatCommented: -1 },
          $pull: { usersThatCommented: comment.createdBy },
        },
      );
    }

    res.json({});
  }));

  router.patch('/like', findUser, asyncHandler(async (req, res, next) => {
    const notificationFn = async function (comment) {
      if (comment.profile) {
        // no notifications for profile comments since app can't handle them
        return;
      }
      const question = await Question.findById(comment.question);
      if (!question) {
        return;
      }

      // check if comment is visible to the comment author
      const query = { _id: comment._id };
      const posts = await getPosts(
        comment.createdBy,
        null,
        null,
        query,
        null,
        null,
        Comment,
        formatComment,
        true,
      );
      if (posts.length == 0) {
        return;
      }

      const commentData = {
        _id: comment._id,
        question: comment.question,
        parent: comment.parent,
        postRepliedTo: comment.postRepliedTo,
        interest: interestLib.formatInterest(question.parent, comment.createdBy.locale),
        interestName: question.interestName,
      };
      const data = { comment: JSON.stringify(commentData) };
      await updateNotification(
        comment.createdBy,
        comment,
        'comment',
        'like',
        JSON.stringify(data),
        req.user,
        comment.numLikes,
      );

      let category, analyticsLabel;
      if(await isMatched(comment.createdBy._id, req.user._id)){
        category = 'commentLikesMatches'
        analyticsLabel = 'new-comment-likes-matches'
      }else{
        category = 'commentLikesOtherSouls'
        analyticsLabel = 'new-comment-likes-other-souls'
      }

      admin.sendNotification(
        comment.createdBy,
        category,
        translate(
          '%s loved your comment',
          comment.createdBy.locale,
          req.user.firstName,
        ),
        translate(
          'You: %s',
          comment.createdBy.locale,
          comment.text,
        ),
        data,
        comment._id,
        'social',
        analyticsLabel,
      );
    };

    await likePost(
      Comment,
      updateCommentScore,
      req.user,
      req.body.commentId,
      notificationFn,
    );

    res.json({});
  }));

  router.patch('/unlike', findUser, asyncHandler(async (req, res, next) => {
    await unlikePost(
      Comment,
      updateCommentScore,
      req.user._id,
      req.body.commentId,
    );
    res.json({});
  }));

  router.patch('/edit', asyncHandler(verifyOwnComment), asyncHandler(async (req, res, next) => {
    if (req.comment.profile && req.comment.depth == 1) {
      if (!req.body.text && !req.comment.vote) {
        return next(invalidInputError());
      }
    }
    if (req.body.text && req.body.text.length > 10000) {
      return next(invalidInputError('Character limit exceeded'));
    }
    if (req.body.gif && !isValidGif(req.body.gif)) {
      return next(invalidInputError());
    }

    req.comment.text = req.body.text;
    req.comment.gif = req.body.gif;

    if (!req.comment.banned) {
      const keyword = containsBannedPostKeywords(req.comment.text);
      if (keyword) {
        req.comment.banned = true;
        req.comment.bannedReason = `keyword: ${keyword}`;
      }
    }

    // process mentioned users
    const comment = req.comment;
    let mentionedUsersText;
    if (req.body.mentionedUsersText) {
      mentionedUsersText = await socialLib.validateMentionedUsers(req.body.mentionedUsersText);
    }
    comment.mentionedUsersText = mentionedUsersText;

    if (mentionedUsersText
        && !comment.banned
        && !req.user.shadowBanned
        && comment.question) {

      const question = await Question.findById(comment.question, 'interestName');

      const commentData = {
        _id: comment._id,
        question: comment.question,
        parent: comment.parent,
        postRepliedTo: comment.postRepliedTo,
        interestName: question.interestName,
      };
      const data = { comment: JSON.stringify(commentData) };

      await socialLib.notifyMentionedUsers(mentionedUsersText, comment, 'comment', data, req.user, comment.text);
    }

    if (req.body.gif && req.comment.image) {
      await s3.deletePicture(req.comment.image);
      req.comment.image = undefined;
    }
    req.comment.isEdited = true;
    await req.comment.save();

    if (!process.env.TESTING) {
      res.json({});
    }
    if (comment.question) {
      const question = await Question.findById(comment.question);
      await socialLib.moderatePostUsingOpenai(req.comment.text, question, req.comment);
    }
    await Comment.updateSearchFields(req.comment._id);
    if (process.env.TESTING) {
      res.json({});
    }
  }));

  router.post('/image', asyncHandler(verifyOwnComment), s3.uploadCommentImage.single('image'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError());
    }
    console.log(req.file);

    const moderationService = new ImageModerationService(['Hive']);
    const moderationResults = await moderationService.moderatePicture(req.file.key);
    if (moderationResults.hive_v2) {
      console.log(`Inappropriate picture detected from user ${req.uid}`);
      req.comment.banned = true;
      req.comment.bannedReason = JSON.stringify(moderationResults);
    }
    if (req.comment.image) {
      console.log('Replacing image: ', req.comment.image);
      await s3.deletePicture(req.comment.image);
      req.comment.isEdited = true;
    }
    if (req.body.aspectRatio && req.body.aspectRatio !== null) {
      req.comment.aspectRatio = req.body.aspectRatio;
    }
    req.comment.gif = undefined;
    req.comment.image = req.file.key;
    console.log('Saving image: ', req.comment.image);
    await req.comment.save();
    return res.json({
      image: req.comment.image,
    });
  }));

  router.post('/video', asyncHandler(verifyOwnComment), s3.uploadCommentVideo.single('video'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Video was not provided'));
    }
    console.log(req.file);

    const status = await convertHls(req.file.key);
    if (status == 'COMPLETE') {
      const result = await isVideoInappropriate(req.file.key, req.user, true);
      if (result.hive_v2) {
        console.log(`Inappropriate video thumbnail detected from user ${req.uid}`);
        req.comment.banned = true;
        req.comment.bannedReason = JSON.stringify(result);
      }
    }

    if (req.body.aspectRatio && req.body.aspectRatio !== null) {
      req.comment.aspectRatio = req.body.aspectRatio;
    }

    if (req.comment.image) {
      console.log('Replacing image: ', req.comment.image);
      await s3.deletePicture(req.comment.image);
      req.comment.isEdited = true;
    }
    req.comment.mediaUploadPending = undefined;
    req.comment.gif = undefined;
    req.comment.image = req.file.key;
    req.comment.isVideo = true;
    console.log('Saving image: ', req.comment.image);
    await req.comment.save();

    res.json({
      image: req.comment.image,
    });
  }));

  router.delete('/image', asyncHandler(verifyOwnComment), asyncHandler(async (req, res, next) => {
    if (!req.comment.image) {
      return next(notFoundError());
    }

    await s3.deletePicture(req.comment.image);

    req.comment.image = undefined;
    req.comment.isEdited = true;
    await req.comment.save();
    return res.json({});
  }));

  router.post('/audio', asyncHandler(verifyOwnComment), s3.uploadCommentAudio.single('audio'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError());
    }
    console.log(req.file);

    if (req.comment.audio) {
      console.log('Replacing audio: ', req.comment.audio);
      await s3.deletePicture(req.comment.audio);
      req.comment.isEdited = true;
      req.comment.audioTranscription = undefined;
    }
    req.comment.audio = req.file.key;
    const transcription = await transcribeAudio(req.file.key);
    if (transcription) {
      req.comment.audioTranscription = transcription;
    }
    if (req.body.waveform && typeof req.body.waveform === 'string') {
      const waveform = JSON.parse(req.body.waveform);
      req.comment.audioWaveform = waveform;
      req.comment.audioDuration = req.body.duration;
    }
    console.log('Saving audio: ', req.comment.audio);
    await req.comment.save();
    return res.json({
      audio: req.comment.audio,
    });
  }));

  router.delete('/audio', asyncHandler(verifyOwnComment), asyncHandler(async (req, res, next) => {
    if (!req.comment.audio) {
      return next(notFoundError());
    }

    await s3.deletePicture(req.comment.audio);

    req.comment.audio = undefined;
    req.comment.isEdited = true;
    req.comment.audioTranscription = undefined;
    await req.comment.save();
    return res.json({});
  }));

  router.patch('/report', findUser, asyncHandler(async (req, res, next) => {
    if (!mongoose.isValidObjectId(req.body.commentId)) {
      return next(invalidInputError());
    }
    // If any user from the same device id reported this comment before, ignore this report
    const previouslyReported = await hasUserReportedFieldValue(req.user, 'postreports', 'reportedComment', req.body.commentId);
    if (previouslyReported) {
      return res.json({});
    }
    // max 10 post reports allowed per 24 hours
    const numPriorReports = await PostReport.countDocuments({
      reportedBy: req.uid,
      createdAt: { $gt: moment().subtract(24, 'hours').toDate() },
    });
    if (numPriorReports >= 10) {
      return res.json({});
    }

    // ignore if report success ratio over past 30 days is less than 10%
    {
      const counts = await PostReport.aggregate([
        {
          $match: {
            reportedBy: req.uid,
            createdAt: { $gt: moment().subtract(30, 'days').toDate() },
            openaiBan: { $ne: null },
          }
        },
        {
          $group: {
            _id: '$openaiBan',
            count: { $sum: 1 },
          }
        },
      ]);
      let numSuccess = 0;
      let numFail = 0;
      for (let count of counts) {
        if (count._id == false) {
          numFail += count.count;
        } else {
          numSuccess += count.count;
        }
      }
      let numTotal = numSuccess + numFail;
      if (numTotal >= 10 && numSuccess/numTotal <= 0.1) {
        return res.json({});
      }
    }

    if (!process.env.TESTING) {
      res.json({});
    }

    const comment = await Comment.findById(req.body.commentId).populate('createdBy').populate('question');
    if (comment && !comment.isVideo && (!comment.audio || (comment.audio && comment.audioTranscription))) {
      const priorReports = await PostReport.find({reportedComment: comment._id, openaiBan: {$ne:null}});
      if (priorReports.length > 0) {
        if (process.env.TESTING) {
          res.json({});
        }
        return;
      }
      const question = comment.question;
      let parentComment;
      if (comment.depth == 2) {
        parentComment = await Comment.findById(comment.parent);
      }
      let ban = await openai.handleCommentReport(comment, question, parentComment, req.body.reason, req.body.explanation, req.user._id);
      if (ban) {
        await socialLib.banPost(Comment, comment._id);
        if (comment.createdBy) {
          let bannedPosts = await PostReport.aggregate([
            {
              $match: {
                reportedUser: comment.createdBy._id,
                createdAt: { $gt: DateTime.utc().minus({ days: 30 }).toJSDate() },
                openaiBan: true,
              }
            },
            {
              $group: {
                _id: '$parentQuestion',
                count: { $sum: 1 },
              }
            },
          ]);
          if (bannedPosts.length >= 10) {
            await shadowBan(comment.createdBy, null, '10 comments within 10 different post threads banned within 30 days');
          }
        }
      }
    }
    else {
      await reportPost(Comment, req.body.commentId, req.user._id);
    }

    if (process.env.TESTING) {
      res.json({});
    }
  }));

  router.post('/award', asyncHandler(async (req, res, next) => {
    await awardPost(req, res, next, 'comment');
  }));
  router.get('/awardSenders', asyncHandler(socialLib.getAwardSenders));

  router.get('/likes', asyncHandler(async (req, res, next) => {
    // API for page is 0-indexed
    let pageNumber = 0;
    if (req.query.page) {
      if (isNaN(req.query.page)) {
        return next(invalidInputError());
      }
      pageNumber = parseInt(req.query.page);
    }

    const rv = await getLikes(req.query.commentId, pageNumber, Comment, req.user);
    return res.json(rv);
  }));

  router.put('/ban', asyncHandler(async (req, res, next) => {
    if (!mongoose.isValidObjectId(req.body.commentId)) {
      return next(invalidInputError());
    }

    const comment = await Comment
      .findById(req.body.commentId)
      .populate('question');

    if (!comment || !comment.question) {
      return next(notFoundError());
    }
    if (comment.question.createdBy != req.uid) {
      return next(forbiddenError());
    }

    comment.banned = true;
    comment.bannedDate = Date.now();
    comment.bannedBy = req.uid;
    comment.bannedByQuestionPoster = true;
    comment.flagged = false;
    await comment.save();

    res.json({});
  }));

  return router;
};
