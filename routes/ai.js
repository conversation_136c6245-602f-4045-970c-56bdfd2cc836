'use strict';

const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const userMiddleware = require('../middleware/user');
const chatMiddleware = require('../middleware/chat');
const httpErrors = require('../lib/http-errors');
const User = require('../models/user');
const Message = require('../models/message');
const Question = require('../models/question');
const Comment = require('../models/comment');
const UsersAiTailoredPrompts = require('../models/users-ai-tailored-prompts');
const userLib = require('../lib/user');
const openai = require('../lib/openai');
const pricing = require('../lib/neuron-pricing');
const { iap, processNeuronPurchase } = require('../lib/iap');

async function getRecentMessages(chatId, user, otherUser, numMessages, characterLimit, analyze) {
  const query = {
    chat: chatId,
    deletedAt: null,
    hidden: { $ne: true },
    image: { $exists: false },
    gif: { $exists: false },
    audio: { $exists: false },
    video: { $exists: false },
  };
  const messages = await Message.find(query)
    .limit(numMessages)
    .sort('-createdAt')
    .lean()

  // drop oldest message until under character limit
  while (messages.length > 1) {
    const numChar = messages.map(x => x.text.length).reduce((a, b) => a + b, 0);
    if (numChar > characterLimit) {
      messages.pop();
    } else {
      break;
    }
  }

  if (messages.length == 0) {
    return {
      recentMessages: '',
      lastSender: null,
      lastMessage: null,
      totalLength: 0,
    };
  }

  let totalLength = 0;
  let lastSender = (messages[0].sender == user._id) ? 'me' : 'match';
  let lastMessage = messages[0].text.substring(0, characterLimit);

  messages.reverse();
  const formatted = messages.map(function(message) {
    totalLength += message.text.length;
    const sender = (message.sender == user._id) ? 'Me' : otherUser.firstName;
    let prefix = '';
    if (analyze == 'me') {
      if (message.sender == user._id) {
        prefix = 'ANALYZE: ';
      } else {
        prefix = 'CONTEXT: ';
      }
    }
    if (analyze == 'match') {
      if (message.sender == user._id) {
        prefix = 'CONTEXT: ';
      } else {
        prefix = 'ANALYZE: ';
      }
    }
    return `---[${prefix}${sender}]: ${message.text.substring(0, characterLimit)}`;
  });
  const recentMessages = formatted.join('\n');
  return {
    recentMessages,
    lastSender,
    lastMessage,
    totalLength,
  };
}

async function deductPayment(user, useCase) {
  if (user.versionAtLeast('1.13.50')) {
    return;
  }
  const language = getLanguage(user);
  const prices = pricing.getPricing(user, language);
  const price = prices[useCase];
  user.numBooAINeurons -= price;
  user.metrics.numNeuronsUsed += price;
  await user.save();
}

function getLanguage(user) {
  if (['bn', 'el', 'fa', 'gl', 'ms', 'or', 'pa', 'pt', 'th', 'vi'].includes(user.locale)) {
    return user.locale;
  }
  return user.aiSettings?.outputLanguage || user.locale;
}

module.exports = function () {

  router.get('/prices', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const language = getLanguage(user);
    const prices = pricing.getPricing(user, language);
    return res.json({
      prices,
    });
  }));

  router.get('/neuronProductIds', asyncHandler(async (req, res, next) => {
    return res.json({
      neuronProductIds: [
        '1000_neurons',
        '300_neurons',
        '100_neurons',
        '40_neurons',
        '6_neurons',
      ],
    });
  }));

  router.put('/settings', asyncHandler(async (req, res, next) => {
    const user = req.user;
    if (req.body.aiSettings.tone) {
      if (await openai.isInappropriate(req.body.aiSettings.tone)) {
        return next(httpErrors.invalidInputError('Your tone could not be saved due to inappropriate content.'));
      }
    }
    user.aiSettings = req.body.aiSettings;
    await user.save();
    return res.json({});
  }));

  router.put('/icebreakers', asyncHandler(userMiddleware.findOtherUser), asyncHandler(async (req, res, next) => {
    const user = req.user;
    const otherUser = req.otherUser;
    let outputType = req.body.outputType;
    const { userInput } = req.body;
    if (!['topical icebreakers', 'pickup lines', 'jokes', 'compliments'].includes(outputType)) {
      return next(httpErrors.invalidInputError());
    }
    const contextType = req.body.contextType;
    if (!['interests', 'bio'].includes(contextType)) {
      return next(httpErrors.invalidInputError());
    }
    const selectedInterests = req.body.selectedInterests || [];
    if ((contextType == 'interests' && selectedInterests.length == 0) || (contextType != 'interests' && selectedInterests.length > 0)) {
      return next(httpErrors.invalidInputError());
    }

    // replace "rock" with "rock music"
    {
      const index = selectedInterests.indexOf('rock');

      if (index !== -1) {
        selectedInterests[index] = 'rock music';
      }
    }

    const language = getLanguage(user);

    let previousResponses = req.body.previousResponses;
    if (previousResponses) {
      previousResponses = previousResponses.slice(0, 12);
    }

    const output = await openai.getIcebreakers(user, otherUser, outputType, language, contextType, selectedInterests, previousResponses, userInput?.trim());

    if (output) {
      await deductPayment(user, 'icebreakers');
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.put('/continueConversation', asyncHandler(userMiddleware.findOtherUser), asyncHandler(chatMiddleware.findApprovedChat), asyncHandler(async (req, res, next) => {
    const user = req.user;
    const otherUser = req.otherUser;
    const language = getLanguage(user);
    const { userInput } = req.body;

    let previousResponses = req.body.previousResponses;
    if (previousResponses) {
      previousResponses = previousResponses.slice(0, 12);
    }

    const { recentMessages, lastSender, lastMessage, totalLength } = await getRecentMessages(req.chat._id, user, otherUser, 30, 3000);
    if (totalLength == 0) {
      // Error code 499 indicates frontend should hide Retry button.
      return next(new httpErrors.HttpError(499, 'Not enough chat history'));
    }

    const output = await openai.continueConversation(user, otherUser, language, recentMessages, lastSender, lastMessage, previousResponses, userInput?.trim());

    if (output) {
      await deductPayment(user, 'continueConversation');
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.put('/chatAnalysis', asyncHandler(userMiddleware.findOtherUser), asyncHandler(chatMiddleware.findApprovedChat), asyncHandler(async (req, res, next) => {
    const user = req.user;
    const otherUser = req.otherUser;
    const language = getLanguage(user);
    const outputType = req.body.outputType;
    const { userInput } = req.body;
    if (!['sentiment', 'performance', 'intent'].includes(outputType)) {
      return next(httpErrors.invalidInputError());
    }

    const analyze = (outputType == 'performance') ? 'me' : 'match';
    const {recentMessages, lastSender, lastMessage, totalLength} = await getRecentMessages(req.chat._id, user, otherUser, 30, 3000, analyze);
    if (totalLength < 10) {
      // Error code 499 indicates frontend should hide Retry button.
      return next(new httpErrors.HttpError(499, 'Not enough chat history'));
    }

    const output = await openai.chatAnalysis(user, otherUser, outputType, language, recentMessages, userInput?.trim());

    if (output) {
      await deductPayment(user, 'analysis');
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.put('/profileAnalysis', userMiddleware.findOtherUser, asyncHandler(async (req, res, next) => {
    const user = req.user;
    const otherUser = req.otherUser;
    const language = getLanguage(user);
    const outputType = req.body.outputType;
    const { userInput } = req.body;
    if (!['profile', 'compatibility'].includes(outputType)) {
      return next(httpErrors.invalidInputError());
    }

    const output = await openai.profileAnalysis(user, otherUser, outputType, language, userInput?.trim());

    if (output) {
      await deductPayment(user, 'analysis');
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.put('/social', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const language = getLanguage(user);
    const outputType = req.body.outputType;
    if (!['suggest', 'paraphrase', 'proofread'].includes(outputType)) {
      return next(httpErrors.invalidInputError());
    }
    if (await openai.isInappropriate(req.body.userInput)) {
      return next(httpErrors.invalidInputError('Your message could not be processed due to inappropriate content.'));
    }
    if (!req.body.questionId) {
      return next(httpErrors.invalidInputError());
    }
    const question = await Question.findById(req.body.questionId).populate('createdBy');
    if (!question) {
      return next(httpErrors.notFoundError());
    }

    let comment;
    if (req.body.commentId) {
      comment = await Comment.findById(req.body.commentId).populate('createdBy');
      if (!comment) {
        return next(httpErrors.notFoundError());
      }
    }

    let previousResponses = req.body.previousResponses;
    if (previousResponses) {
      previousResponses = previousResponses.slice(0, 12);
    }

    const output = await openai.processSocial(user, outputType, language, req.body.userInput, question, comment, previousResponses);

    if (output) {
      await deductPayment(user, outputType);
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.put('/bio/generate', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const language = getLanguage(user);
    const context = req.body.context;
    const content = req.body.content;
    const tone = req.body.tone || user.aiSettings?.tone;

    let previousResponses = req.body.previousResponses;
    if (previousResponses) {
      previousResponses = previousResponses.slice(0, 12);
    }

    const output = await openai.processBioGenerate(user, language, context, content, tone, previousResponses);

    if (output) {
      await deductPayment(user, 'bioGenerate');
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.put('/bio/improve', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const language = getLanguage(user);
    const context = req.body.context;
    const content = req.body.content;
    const bio = req.body.bio || user.description;

    let previousResponses = req.body.previousResponses;
    if (previousResponses) {
      previousResponses = previousResponses.slice(0, 12);
    }

    const output = await openai.processBioImprove(user, language, bio, context, content, previousResponses);

    if (output) {
      await deductPayment(user, 'bioImprove');
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.put('/bio/changeTone', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const language = getLanguage(user);
    const tone = req.body.tone || user.aiSettings?.tone;
    const bio = req.body.bio || user.description;

    let previousResponses = req.body.previousResponses;
    if (previousResponses) {
      previousResponses = previousResponses.slice(0, 12);
    }

    const output = await openai.processBioChangeTone(user, language, bio, tone, previousResponses);

    if (output) {
      await deductPayment(user, 'bioTone');
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.put('/bio/proofread', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const language = getLanguage(user);
    const bio = req.body.bio || user.description;

    let previousResponses = req.body.previousResponses;
    if (previousResponses) {
      previousResponses = previousResponses.slice(0, 12);
    }

    const output = await openai.processBioProofread(user, language, bio, previousResponses);

    if (output) {
      await deductPayment(user, 'bioProofread');
    }

    return res.json({
      output,
      numBooAINeurons: user.numBooAINeurons,
    });
  }));

  router.get('/aiTailoredPrompts', asyncHandler(async (req, res, next) => {
    const { user } = req;
    let prompts = []
    if (!user.isConfigTrue('app_204')) {
      return next(httpErrors.forbiddenError());
    }

    const retry = (req.query.retry == true || req.query.retry == 'true') ? true : false

    let promptsCount = await UsersAiTailoredPrompts.countDocuments({ userId: user._id, isError: false })
    if (retry && promptsCount > 2) {
      return next(httpErrors.forbiddenError());
    }

    let promptsResponse = await UsersAiTailoredPrompts.findOne({ userId: user._id }).select({ outputPrompts: 1, isError: 1 }).sort({ createdAt: -1 }).lean()
    if(!retry && promptsResponse && promptsResponse.outputPrompts && promptsResponse.outputPrompts.length && !promptsResponse.isError){
      prompts = promptsResponse.outputPrompts
    } else if(retry && promptsResponse && promptsResponse.outputPrompts && !promptsResponse.isError){
      promptsResponse = await userLib.processUserAiTailoredPrompts(user, promptsResponse.outputPrompts);
      if(promptsResponse && !promptsResponse.isError && promptsResponse.outputPrompts) promptsCount = promptsCount + 1
      prompts = promptsResponse.outputPrompts
    }

    if(!promptsResponse || promptsResponse.isError){
      promptsResponse = await userLib.processUserAiTailoredPrompts(user);
      if(promptsResponse && !promptsResponse.isError && promptsResponse.outputPrompts) promptsCount = promptsCount + 1
      prompts = promptsResponse.outputPrompts
    }

    if (!promptsResponse || promptsResponse.isError || !Array.isArray(prompts)) {
      return next(httpErrors.applicationError());
    }

    return res.json({
      prompts,
      retryAvailable: promptsCount < 3 ? true : false,
    });

  }));

  router.put('/purchaseNeurons', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const errorMsg = 'Purchase could not be validated.';

    if (!req.body.receipt) {
      return next(invalidInputError(errorMsg));
    }

    iap.validate(req.body.receipt, async (error, validatedData) => {
      if (error) {
        console.log(error, validatedData);
        if (
          validatedData && validatedData.status == 2 // hacked apple receipt
          || error.message == 'Status:400' // hacked google receipt
          || error.message == 'Status:403' // google receipt from elsewhere
        ) {
          console.log('Potentially a hacked receipt');
          return next(invalidInputError(errorMsg));
        }
        return next(applicationError(errorMsg));
      }

      const purchaseData = iap.getPurchaseData(validatedData);

      console.log(validatedData);
      console.log(purchaseData);

      await processNeuronPurchase(user, purchaseData, req.body.price, req.body.currency)
        .then(() => res.json({}))
        .catch((err) => {
          console.log(err);
          return next(applicationError());
        });
    });
  }));

  return router;
};
