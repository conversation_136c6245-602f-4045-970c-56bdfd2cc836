const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const moment = require('moment');
const { notFoundError, invalidInputError } = require('../lib/http-errors');
const User = require('../models/user');
const Interest = require('../models/interest');
const interestLib = require('../lib/interest');
const constants = require('../lib/constants');
const { profilePreviewProjection, getBlockLookup } = require('../lib/projections');
const { cloudwatch } = require('../lib/cloudwatch');
const { sendNotification } = require('../config/firebase-admin');
const Question = require('../models/question');
const Comment = require('../models/comment');
const InterestCountryCount = require('../models/interest-country-count')
const { translate } = require('../lib/translate');
const InterestPoint = require('../models/interest-point');
const { findUser } = require('../middleware/user');

module.exports = function () {
  router.get('/', asyncHandler(interestLib.getInterestRouteHandler));

  router.get('/similar', asyncHandler(interestLib.getSimilarInterestsRouteHandler));

  router.get('/popular', asyncHandler(interestLib.getPopularInterestsRouteHandler));

  router.get('/suggested',findUser, asyncHandler(interestLib.getSuggestedInterestRouteHandler));

  router.get('/popularInterestByCountry', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { selectedInterest, excludeInterests } = req.query;
    if (!user || !user.ipData.country) {
      return next(invalidInputError(req.__('Select Country and Language')));
    }
    let interests = []
    if (selectedInterest) {
      const interest = await InterestCountryCount.findOne({
        interestName: selectedInterest,
        country: user.ipData?.country,
        locale: user.locale,
      });
      if (interest?.category) {
        let onboardingCategoryData = interestLib.getCategoryOnboardingInterests(user.locale ? user.locale : 'en', interest.category.toLowerCase())
        let toExcludeInterests = excludeInterests?.length ? excludeInterests.split(',') : [];
        toExcludeInterests.push(selectedInterest);
        toExcludeInterests = toExcludeInterests.concat(onboardingCategoryData);
        toExcludeInterests = [...new Set(toExcludeInterests)];
        interests = await InterestCountryCount.find({
          country: user.ipData?.country,
          locale: user.locale,
          category: interest.category,
          count: { $gt: 0 },
          interestName: { $nin: toExcludeInterests },
        })
        .sort({ count: -1 })
        .limit(6)
        .select({ interestName: 1, category: 1, _id: 0 });
      }
    } else {
      interests = await InterestCountryCount.find({
        country: user.ipData?.country,
        locale: user.locale,
        count: { $gt: 0 }
      })
      .sort({ count: -1 })
      .limit(50)
      .select({ interestName: 1, category: 1, _id: 0 });
    }
    res.json({ interests });
  }));

  router.get('/popularInterestsForOnboarding', asyncHandler(async (req, res, next) => {
    res.json({
      interestNames: interestLib.getPopularInterestsForOnboarding(req.user.locale),
    });
  }));

  router.get('/getOnboardingInterestsCarousel', asyncHandler(async (req, res, next) => {
    res.json({
      interestSlides: interestLib.getOnboardingInterestsCarousel(req.user.interestNames,req.user.locale),
    });
  }));

  router.get('/popularInterestsForOnboardingWithNumFollowers', asyncHandler(async (req, res, next) => {
    const names = interestLib.getPopularInterestsForOnboarding(req.user.locale);

    const query = { name: { $in: names } };
    const interests = await Interest
      .find(query, '-_id name numFollowers')

    res.json({
      interests,
    });
  }));

  router.get('/autocomplete', asyncHandler(interestLib.autocompleteRouteHandler));

  router.post('/', asyncHandler(async (req, res, next) => {
    const user = req.user;

    let { name } = req.body;
    if (!name || typeof name !== 'string' || name.length > 21) {
      return next(invalidInputError());
    }

    name = interestLib.cleanInterestName(name);

    const exists = await Interest.findOne({ name });
    if (exists) {
      return next(invalidInputError(req.__('This interest already exists or is under review.')));
    }

    const approved = await interestLib.shouldInterestBeApproved(name);
    const status = approved ? undefined : 'rejected';

    const interest = new Interest({
      createdBy: user._id,
      interest: `#${name}`,
      name,
      status,
    });

    if (approved) {
      interest.numFollowers += 1;
      interest.numFollowersSortIndex += 1;
    }
    await interest.save();

    if (approved) {
      user.interestNames.push(interest.name);
      await InterestCountryCount.incrementCount(interest.name, interest.libCategory || interest.category || null, user.ipData?.country, user.locale)
      await user.save();

      sendNotification(
        user,
        null,
        '',
        translate('Your interest #%s has been approved!', user.locale, interest.name),
        null,
        null,
        'general',
        'interest-approved',
      );

    }
    else {
      sendNotification(
        user,
        null,
        '',
        translate('Your interest #%s was not approved.', user.locale, interest.name),
        null,
        null,
        'negative',
        'interest-rejected',
      );
    }

    res.json({
      approved,
    });
  }));

  router.get('/users', asyncHandler(async (req, res, next) => {
    const matchStage = {
      interestNames: req.query.interestName,
      shadowBanned: { $ne: true },
      hidden: { $ne: true },
    };

    if (req.query.beforeId) {
      const user = await User.findById(req.query.beforeId);
      if (user) {
        matchStage['metrics.lastSeen'] = { $lt: user.metrics.lastSeen };
      }
    }

    let users = [];
    while (!users.length) {
      // eslint-disable-next-line no-await-in-loop
      const unfilteredUsers = await User.aggregate([
        { $match: matchStage },
        { $sort: { 'metrics.lastSeen': -1 } },
        { $limit: constants.getPageSize() },
        getBlockLookup(req.user._id, '$_id'),
        { $project: { ...profilePreviewProjection, 'metrics.lastSeen': 1, block: 1 } },
      ]);

      if (!unfilteredUsers.length) {
        break;
      }

      const lastSeenOfLastUser = unfilteredUsers[unfilteredUsers.length - 1]?.metrics?.lastSeen;
      if (lastSeenOfLastUser) {
        matchStage['metrics.lastSeen'] = { $lt: lastSeenOfLastUser };
      }

      users = unfilteredUsers.reduce((acc, { block, metrics, ...rest }) => {
        if (!block || block.length === 0) {
          acc.push(rest);
        }
        return acc;
      }, []);
    }

    res.json({
      users,
    });
  }));

  router.get('/topRankedUsers', asyncHandler(async (req, res, next) => {

    const interest = req.query.interestName;
    const language = req.query.language;
    const entries = await InterestPoint.aggregate([
      {
        $match: {
          interest: interest,
          language: language,
          points: { $gt: 0 },
          shadowBanned: { $ne: true },
        }
      },
      {
        $sort: {
          points: -1,
        }
      },
      {
        $limit: 100
      },
      {
        $lookup: {
          from: 'users',
          let: { "user": "$user" },
          pipeline: [
            { "$match": { "$expr": { "$eq": ["$_id", "$$user"] } } },
            { "$project": { ...profilePreviewProjection, karma: 1 } },
          ],
          as: 'userObj',
        }
      },
      {
        $unwind: '$userObj'
      },
    ]);

    const topRankedUsers = [];
    for (let i = 0; i < entries.length; i++) {
      const entry = entries[i];
      const rank = entry.rank;
      const points = entry.points;
      const user = entry.userObj;
      topRankedUsers.push({
        rank,
        points,
        user,
      });
    }

    let myStats;
    {
      const found = await InterestPoint.findOne({
        user: req.uid,
        interest,
        language,
      });
      if (found) {
        myStats = {
          rank: found.rank,
          points: found.points,
        }
      }
    }

    res.json({
      myStats,
      topRankedUsers,
    });
  }));

  router.get('/followerPictures', asyncHandler(async (req, res, next) => {
    const user = req.user;

    let interestNames = req.query.name;
    if (!interestNames) {
      return next(invalidInputError());
    }
    if (typeof interestNames === 'string') {
      interestNames = [interestNames];
    }
    if (!Array.isArray(interestNames)) {
      return next(invalidInputError());
    }
    if (interestNames.length > 10) {
      return next(invalidInputError());
    }

    async function getFollowerPictures(interestName, genders, limit) {
      const query = {
        _id: { $ne: user._id },
        interestNames: interestName,
        shadowBanned: { $ne: true },
        hidden: { $ne: true },
        'pictures.0': { $exists: true },
        gender: { $in: genders },
      };
      const projection = { picture: { $first: '$pictures' } };
      const users = await User
        .find(query, projection)
        .limit(limit)
        .sort({ 'metrics.lastSeen': -1 })
        .lean()
      const followerPictures = users.filter(x => x.picture).map(x => x.picture);
      return followerPictures;
    }

    async function getInterestDetails(interestName) {
      const interest = await Interest.findOne({
        name: interestName,
        status: null,
      });
      if (!interest) {
        return;
      }
      const numPictures = 3;
      let followerPictures = [];
      let searchedGenders = [];

      if (followerPictures.length < numPictures) {
        const genders = user.preferences.dating.filter(x => !searchedGenders.includes(x));
        if (genders.length) {
          followerPictures = followerPictures.concat(await getFollowerPictures(interestName, genders, numPictures - followerPictures.length));
          searchedGenders = searchedGenders.concat(genders);
        }
      }
      if (followerPictures.length < numPictures) {
        const genders = user.preferences.friends.filter(x => !searchedGenders.includes(x));
        if (genders.length) {
          followerPictures = followerPictures.concat(await getFollowerPictures(interestName, genders, numPictures - followerPictures.length));
          searchedGenders = searchedGenders.concat(genders);
        }
      }
      if (followerPictures.length < numPictures) {
        const genders = ['male', 'female', 'non-binary'].filter(x => !searchedGenders.includes(x));
        if (genders.length) {
          followerPictures = followerPictures.concat(await getFollowerPictures(interestName, genders, numPictures - followerPictures.length));
          searchedGenders = searchedGenders.concat(genders);
        }
      }

      return {
        name: interestName,
        numFollowers: interest.numFollowers,
        followerPictures,
      }
    }

    const promises = interestNames.map(x => getInterestDetails(x));
    const results = await Promise.allSettled(promises);
    const interests = results.filter(x => x.status == 'fulfilled' && x.value).map(x => x.value);

    res.json({
      interests,
    });
  }));

  router.get('/onboardingInterests', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;

    // Initialize popular interests as empty
    let popularInterests = {};

    // APP-315 is shipped true, so fetch popular interests from the database
    /*
    popularInterests = {
      category: 'Popular',
      interestNames: interestLib.getPopularInterestsForOnboarding(user.locale),
    };
    */

    let interests = await InterestCountryCount.find({
      country: user.ipData?.country || 'United States',
      locale: user.locale,
      count: { $gt: 0 },
    }).sort({ count: -1 })
      .limit(50)
      .select({ interestName: 1, category: 1, _id: 0 });

    interests = interests.length ? interests.map(x => x.interestName) : interestLib.getPopularInterestsForOnboarding(user.locale);

    popularInterests = {
      category: 'Popular',
      interestNames: interests,
    };

    // Fetch all other onboarding interests
    const allInterests = interestLib.getAllOnboardingInterests(user);

    // Combine popular interests with other interests
    let response = [popularInterests, ...allInterests];

    if (user.partnerCampaign === 'gaming') {
      response = interestLib.reorderFirstCategory(response, 'Gaming');
    } else if (user.partnerCampaign === 'anime') {
      response = interestLib.reorderFirstCategory(response, 'Anime');
    }

    res.json(response);
  }));

  return router;
};
