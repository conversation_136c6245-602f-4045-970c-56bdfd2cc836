const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const cmp = require('semver-compare');
const mongoose = require('mongoose');
const moment = require('moment');
const cfsign = require('aws-cloudfront-sign');
const {
  notFoundError, forbiddenError, badRequestError, conflictError, applicationError, invalidInputError,
} = require('../lib/http-errors');
const { isVideoInappropriate } = require('../lib/video-moderation');
const s3 = require('../lib/s3');
const { cloudwatch } = require('../lib/cloudwatch');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const Chat = require('../models/chat');
const Gift = require('../models/gift');
const Message = require('../models/message');
const MessageLog = require('../models/message-log');
const MessageReport = require('../models/message-report');
const reportLib = require('../lib/report');
const socialLib = require('../lib/social');
const { findUser, findUserMetadata, deprecated, checkVerified, checkSupportChatOrVerified } = require('../middleware/user');
const { findChatIfExists, findChat, findApprovedChat, validateAutomatedChatState } = require('../middleware/chat');
const { getSigningParams } = require('../lib/cloudfront');
const { isValidGif } = require('../lib/gif');
const coinsConstants = require('../lib/coins-constants');
const constants = require('../lib/constants');
const chatLib = require('../lib/chat');
const { translate } = require('../lib/translate');
const { formatMessage, getMessages, sendMessageNotifications, sendMessageDeleteNotifications, sendReactionNotifications } = require('../lib/message');
const premiumLib = require('../lib/premium');
const admin = require('../config/firebase-admin');
const StickerPack = require('../models/sticker-pack');
const { findBannedMessageKeywords, findBannedEarlyMessageKeywords, findAutoHideMessageKeywords } = require('../lib/report-constants');
const { sendSocketEvent } = require('../lib/socket');
const openai = require('../lib/openai');
const { convertHls } = require('../lib/mediaconvert');
const { ImageModerationService } = require('../lib/image-moderation');
const { handleInactiveAutomatedChatMessage } = require('../lib/autoresponse/responses-helper');
const emojiRegex = require('emoji-regex');

function isSingleEmoji(input) {
  // Trim the input to remove leading/trailing spaces
  const trimmedInput = input.trim();

  // Create a regex to match a single emoji
  const regex = emojiRegex();

  // Match the entire trimmed input against the emoji regex
  const match = trimmedInput.match(regex);

  // Check if the match covers the entire string
  return match !== null && match[0] === trimmedInput;
}

async function handleNewMessage(req, res, next, message, notifText) {
  const { chat } = req;
  const user = chat.users.find((u) => u._id == req.uid);
  const otherUsers = chat.users.filter((x) => x._id != req.uid);

  // publish metrics to cloudwatch if support account
  if (req.uid == chatLib.BOO_SUPPORT_ID && chat.lastMessage) {
    if (
      chat.lastMessage.sender != chatLib.BOO_SUPPORT_ID
      || chat.lastMessage.gif == 'https://media0.giphy.com/media/j0Y6jLEG7rWas1qh3F/giphy.gif'
    ) {
      const waitTime = moment().diff(chat.lastMessage.createdAt, 'seconds');
      const params = {
        MetricData: [
          {
            MetricName: 'SupportChat',
            Value: waitTime,
            Unit: 'Seconds',
          },
        ],
        Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
      };
      await cloudwatch.putMetricData(params).promise();
    }
  }

  if (otherUsers.includes(chatLib.BOO_SUPPORT_ID) && chat.lastMessage) {
    if (
      chat.lastMessage.sender == chatLib.BOO_SUPPORT_ID
      && chat.lastMessage.gif != 'https://media0.giphy.com/media/j0Y6jLEG7rWas1qh3F/giphy.gif'
    ) {
      const params = {
        MetricData: [
          {
            MetricName: 'IncomingSupportChat',
            Value: 1,
            Unit: 'Count',
          },
        ],
        Namespace: `SupportTeamMetrics_${process.env.NODE_ENV}`,
      };
      await cloudwatch.putMetricData(params).promise();
    }
  }

  if (req.query.quotedMessageId
    && mongoose.isValidObjectId(req.query.quotedMessageId)) {
    const quotedMessage = await Message.findById(req.query.quotedMessageId);
    if (quotedMessage) {
      message.quotedMessage = quotedMessage;
    }
  }

  let savedMessage = await message.save()
    .catch((err) => {
      if (err.name == 'ValidationError') {
      // todo: finer error checking
        return next(invalidInputError('Message exceeded the character limit'));
      }
      return next(applicationError('Message could not be saved'));
    });
  const notificationId = savedMessage.notificationId;
  savedMessage = formatMessage(savedMessage);

  if (chat.numMessages == 0 && !chat.groupChat && user.currentDayMetrics.karmaFromFirstToMessage < 20) {
    await socialLib.incrementKarma(user._id, 5, otherUsers[0]._id);

    user.metrics.karmaFromFirstToMessage += 5;
    user.currentDayMetrics.karmaFromFirstToMessage += 5;
    await user.save();
  }

  /*
  if (message.hidden) {
    return res.json(savedMessage);
  }
  */

  // update chat
  const priorMessageTime = chat.lastMessageTime;
  chat.lastMessage = savedMessage._id;
  chat.lastMessageTime = Date.now();
  chat.incrementNumMessages();
  for (const otherUser of otherUsers) {
    chatLib.incrementUnreadMessages(chat, otherUser._id);
    otherUser.metrics.leftOnReadAt = undefined;
    await otherUser.save();
  }

  // increment messages sent by user
  let readReceipt = chat.readReceipts.get(user._id);
  readReceipt.numSentMessages += 1;
  chat.readReceipts.set(user._id, readReceipt);
  if (!chat.messaged) {
    chat.messaged = true;
    chat.firstMessageBy = user._id;
  }
  if (!chat.replied) {
    for (const otherUser of otherUsers) {
      let readReceipt = chat.readReceipts.get(otherUser._id);
      if (readReceipt.numSentMessages) {
        chat.replied = true;
        const diffMs = new Date() - priorMessageTime;
        const diffMinutes = Math.floor((diffMs / 1000) / 60);
        chat.numMinutesUntilFirstReply = diffMinutes;
      }
    }
  }
  // updating perUserState on new message
  const chatPerUserState = chat.perUserState ? [ ...chat.perUserState ] : []; //making copy of chat.perUserState as we reset and push the updated perState of each user in next steps
  chat.perUserState = []
  let senderLastYourTurn = false
  let receiverLastYourTurn = false
  let hasBannedUsers = false
  if(chat.bannedUsers?.length){
    hasBannedUsers = true
  }
  const chatUsers =  chat.users.map(x => x._id)
  const hasAnyBooChatIdOrGroupChat = chat.groupChat || [chatLib.BOO_SUPPORT_ID, chatLib.BOO_BOT_ID, chatLib.BOO_TRANSLATION_ID].some(id => chatUsers.includes(id));
  chatUsers.forEach((chatUserId) => {
    let userStateData = chatPerUserState.find(state => state.userId == chatUserId);
    let selfData = false
    if(chatUserId == user._id){
      selfData = true
      senderLastYourTurn = userStateData ? (['uncategorized', 'yourTurn'].includes(userStateData.yourTurnState) || false) : false
    }else{
      receiverLastYourTurn = userStateData ? (['uncategorized', 'yourTurn'].includes(userStateData.yourTurnState) || false) : false
    }
    if (!userStateData) {
      userStateData = {
        userId: chatUserId,
        yourTurnState: 'uncategorized',
        unread: true,
      };
    }
    if(selfData){
      userStateData.unread = false;
      userStateData.yourTurnState = 'messageSent';
    }else{
      userStateData.unread = true;
      userStateData.yourTurnState = 'uncategorized';
    }
    if (hasAnyBooChatIdOrGroupChat) {
      userStateData.yourTurnState = undefined
    }
    chat.perUserState.push(userStateData);
  });

  try {
    await chat.save();
  } catch (err) {
    console.log(err);
  }

  // update user metrics
  let myMetricsToIncrement = ['numMessagesSent'];
  let otherUserMetricsToIncrement = ['numMessagesReceived'];

  if (!chat.groupChat) {
    let myReadReceipt = chat.readReceipts.get(user._id);
    let otherReadReceipt = chat.readReceipts.get(otherUsers[0]._id);
    if (myReadReceipt.numSentMessages == 1 && otherReadReceipt.numSentMessages == 0) {
      myMetricsToIncrement.push('numMatchesFirstMessageSent');
      otherUserMetricsToIncrement.push('numMatchesFirstMessageReceived');
    }
    else if (myReadReceipt.numSentMessages == 1 && otherReadReceipt.numSentMessages > 0) {
      myMetricsToIncrement.push('numMatchesReplySent');
      myMetricsToIncrement.push('numMatchesBothMessaged');
      otherUserMetricsToIncrement.push('numMatchesReplyReceived');
      otherUserMetricsToIncrement.push('numMatchesBothMessaged');
    }

    // update user and other user numYourTurnChats
    if(!hasBannedUsers && !chat.deletedAt && !receiverLastYourTurn){
      otherUserMetricsToIncrement.push('numYourTurnChats')
    }
    if (!hasBannedUsers && senderLastYourTurn) {
      await User.incrementMetric(user._id, 'numYourTurnChats', -1)
      await chatLib.sendSocketUpdateForNumYourTurnChats(user._id)
    }
  }
  await User.incrementMetrics(user._id, myMetricsToIncrement);
  for (const otherUser of otherUsers) {
    const isNotSupportOrBotUser = !(user._id === chatLib.BOO_SUPPORT_ID || user._id === chatLib.BOO_BOT_ID);
    const isPushNotificationDisabled = !otherUser.fcmToken;

    if (isNotSupportOrBotUser && isPushNotificationDisabled && otherUser.versionAtLeast('1.13.69')) {
      if (!(otherUser.booMessages.notificationTrigger || otherUser.booMessages.notificationTriggerInit)) {
        // Initialize notification trigger and message sender
        otherUser.booMessages.notificationTriggerInit = Date.now();
        otherUser.booMessages.messageSender = user.firstName;
        await otherUser.save();
      }
    }

    await User.incrementMetrics(otherUser._id, otherUserMetricsToIncrement);
    if(otherUserMetricsToIncrement.includes('numYourTurnChats')){
      await chatLib.sendSocketUpdateForNumYourTurnChats(otherUser._id)
    }
    await otherUser.updateNumMinutesUntilParams('numMinutesUntilFirstMessageReceived');
  }

  sendMessageNotifications(user, chat, savedMessage, notifText);

  if (user.metrics.numMessagesSent == 10) {
    await reportLib.screenForScammer(user);
  }

  res.json(savedMessage);

  // If the message is for existing support chat, then send automated response
  const booSupportUser = otherUsers?.find((u) => u._id == chatLib.BOO_SUPPORT_ID);
  const isReplyNeeded = !chat.automatedChat || ![chatLib.BOO_BOT_ID, chatLib.BOO_SUPPORT_ID].includes(req.uid);

  if (booSupportUser && isReplyNeeded && !chat.groupChat && !chat.deletedAt) {
    await chatLib.sendAutomatedSupportReply(chat, user, booSupportUser);
  }
}

async function logMessageIfKeywordFound(message, user) {
  const lower = message.text.toLowerCase();
  const matchFn = (s) => new RegExp(`\\b${s}\\b`).test(lower);
  const keywords = [];

  if (['app'].some(matchFn)) {
    keywords.push('app');
  }
  if (['boo'].some(matchFn)) {
    keywords.push('boo');
  }
  if (['birdy'].some(matchFn)) {
    keywords.push('birdy');
  }
  if (['sosyncd', 'so syncd', 'so synced'].some(matchFn)) {
    keywords.push('sosyncd');
  }
  if (['urmytype', 'ur my type'].some(matchFn)) {
    keywords.push('umt');
    message.hidden = true;
  }
  if (lower.includes('notif')) {
    keywords.push('notif');
  }

  // check for banned keywords only if user's only language is English
  if (user.languages.length == 1 && user.languages[0] == 'en' && findAutoHideMessageKeywords(lower)) {
    message.hidden = true;
  }

  if (!keywords.length) {
    return;
  }

  const messageLog = new MessageLog({
    chat: message.chat,
    sender: message.sender,
    text: message.text,
    keywords,
  });
  await messageLog.save();
}

const isInActiveAutomatedChat = (req) => req.chat.automatedChat && req.uid !== chatLib.BOO_SUPPORT_ID && req.chat.lastMessageTime < Date.now() - 24 * 60 * 60 * 1000;

module.exports = function () {
  router.get('/', asyncHandler(findChat), asyncHandler(async (req, res, next) => {
    await chatLib.clearUnreadMessages(req.chat, req.uid);
    const changeJsonStickersToGif = !req.user.versionAtLeast('1.11.62');
    let messages = await getMessages(req.chat._id, req.query.before, changeJsonStickersToGif, false, req.user);
    res.json(messages);
  }));

  router.post('/', asyncHandler(findApprovedChat), checkSupportChatOrVerified, validateAutomatedChatState, asyncHandler(async (req, res, next) => {
    if (!req.body.text || !req.body.text?.trim()) {
      return next(badRequestError());
    }
    if (req.body.text.length > 10000) {
      return next(invalidInputError('Character limit exceeded'));
    }

    if (isInActiveAutomatedChat(req)) {
      const response = await handleInactiveAutomatedChatMessage(req.chat, req.user, null, req.body.text);
      return response ? res.json(response) : next(applicationError());
    }

    const message = new Message({
      chat: req.chat._id,
      text: req.body.text,
      sender: req.uid,
    });

    await logMessageIfKeywordFound(message, req.user);

    let notifText = message.text;
    if (req.chat.groupChat) {
      notifText = `${req.user.firstName}: ${notifText}`;
    }

    await handleNewMessage(req, res, next, message, notifText);

  }));

  router.post('/image', asyncHandler(findApprovedChat), checkSupportChatOrVerified, validateAutomatedChatState, s3.uploadChatImage.single('image'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Image was not provided'));
    }
    console.log(req.file);

    const moderationService = new ImageModerationService(['Hive']);
    const moderationResults = await moderationService.moderatePicture(req.file.key);
    if (moderationResults.hive_v2) {
      console.log(`Inappropriate picture detected from user ${req.uid}`);
      return next(invalidInputError(req.__('There’s an issue with this photo, please select another one.')));
    }

    if (isInActiveAutomatedChat(req)) {
      const response = await handleInactiveAutomatedChatMessage(req.chat, req.user, null, null, 'image', req.file.key);
      return response ? res.json(response) : next(applicationError());
    }

    const otherUser = req.chat.users.find((u) => u._id != req.uid);
    let notifText = translate(
      '%s sent you a picture!',
      otherUser?.locale,
      req.user.firstName,
    );
    if (req.chat.groupChat) {
      notifText = `${req.user.firstName} sent a picture!`;
    }

    const message = new Message({
      chat: req.chat._id,
      text: `${notifText} [update to latest version to view]`,
      image: req.file.key,
      sender: req.uid,
    });

    if (req.body.aspectRatio && req.body.aspectRatio !== null) {
      message.aspectRatio = req.body.aspectRatio;
    }

    await handleNewMessage(req, res, next, message, notifText);
  }));

  router.post('/audio', asyncHandler(findApprovedChat), checkSupportChatOrVerified, validateAutomatedChatState, s3.uploadChatAudio.single('audio'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Audio was not provided'));
    }
    console.log(req.file);

    if (isInActiveAutomatedChat(req)) {
      const response = await handleInactiveAutomatedChatMessage(req.chat, req.user, null, null, 'audio', req.file.key);
      return response ? res.json(response) : next(applicationError());
    }

    const otherUser = req.chat.users.find((u) => u._id != req.uid);
    let notifText = translate(
      '%s sent you a voice message!',
      otherUser?.locale,
      req.user.firstName,
    );
    if (req.chat.groupChat) {
      notifText = `${req.user.firstName} sent a voice message!`;
    }

    const message = new Message({
      chat: req.chat._id,
      text: `${notifText} [update to latest version to view]`,
      audio: req.file.key,
      sender: req.uid,
    });

    if (req.body.waveform && typeof req.body.waveform === 'string') {
      const waveform = JSON.parse(req.body.waveform);
      message.audioWaveform = waveform;
      message.audioDuration = req.body.duration;
    }

    await handleNewMessage(req, res, next, message, notifText);
  }));

  router.post('/video', asyncHandler(findApprovedChat), checkSupportChatOrVerified, validateAutomatedChatState, s3.uploadChatVideo.single('video'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Video was not provided'));
    }
    console.log(req.file);

    const status = await convertHls(req.file.key);
    if (status == 'COMPLETE') {
      const { hive_v2 } = await isVideoInappropriate(req.file.key, req.user);
      if (hive_v2) {
        return next(invalidInputError(req.__('There’s an issue with this video, please select another one.')));
      }
    }

    if (isInActiveAutomatedChat(req)) {
      const response = await handleInactiveAutomatedChatMessage(req.chat, req.user, null, null, 'video', req.file.key);
      return response ? res.json(response) : next(applicationError());
    }

    let notifText = `${req.user.firstName} sent you a video message!`;
    if (req.chat.groupChat) {
      notifText = `${req.user.firstName} sent a video message!`;
    }

    const message = new Message({
      chat: req.chat._id,
      text: `${req.user.firstName} sent you a video message! [update to latest version to view]`,
      video: req.file.key,
      sender: req.uid,
    });

    if (req.body.aspectRatio && req.body.aspectRatio !== null) {
      message.aspectRatio = req.body.aspectRatio;
    }

    await handleNewMessage(req, res, next, message, notifText);
  }));

  router.post('/gif', asyncHandler(findApprovedChat), checkSupportChatOrVerified, validateAutomatedChatState, asyncHandler(async (req, res, next) => {
    if (!req.body.gif) {
      return next(badRequestError());
    }
    if (!isValidGif(req.body.gif)) {
      return next(invalidInputError());
    }

    if (isInActiveAutomatedChat(req)) {
      const response = await handleInactiveAutomatedChatMessage(req.chat, req.user, null, null, 'gif', req.body.gif);
      return response ? res.json(response) : next(applicationError());
    }

    const otherUser = req.chat.users.find((u) => u._id != req.uid);
    let notifText = translate(
      '%s sent you a gif!',
      otherUser?.locale,
      req.user.firstName,
    );
    if (req.chat.groupChat) {
      notifText = `${req.user.firstName} sent a gif!`;
    }

    const message = new Message({
      chat: req.chat._id,
      text: `${notifText} [update to latest version to view]`,
      gif: req.body.gif,
      sender: req.uid,
    });

    if (req.body.aspectRatio && req.body.aspectRatio !== null) {
      message.aspectRatio = req.body.aspectRatio;
    }

    await handleNewMessage(req, res, next, message, notifText);
  }));

  router.post('/initiateCall', asyncHandler(findApprovedChat), checkSupportChatOrVerified, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { chat } = req;
    const otherUsers = chat.users.filter((u) => u._id != user._id);

    if (user.shadowBanned) {
      return res.json({});
    }

    for (const otherUser of otherUsers) {
      if (otherUser.shadowBanned) {
        continue;
      }

      const notificationData = {
        session_id: req.body.session_id,
        user_name: user.firstName,
        media: 'video',
        signal_type: 'startCall',
        caller_name: user.firstName,
        caller_id: '12345',
        call_type: '1',
        call_opponents: '{43208,20802}',
        ios_voip: '1',
      };

      admin.sendNotification(
        otherUser,
        null,
        '',
        translate('%s is calling you.', otherUser.locale, user.firstName),
        notificationData,
        chat._id,
        null,
        'love',
      );
    }

    res.json({});
  }));

  router.post('/call', asyncHandler(findApprovedChat), checkSupportChatOrVerified, asyncHandler(async (req, res, next) => {
    if (!req.body.type || !req.body.status) {
      return next(invalidInputError());
    }
    if (req.body.status == 'answered' && req.body.duration === undefined) {
      return next(invalidInputError());
    }

    const notifText = `${req.user.firstName} called you.`;

    const message = new Message({
      chat: req.chat._id,
      text: `${notifText} [update to latest version to view]`,
      call: {
        type: req.body.type,
        status: req.body.status,
        duration: req.body.duration,
      },
      sender: req.uid,
    });

    await handleNewMessage(req, res, next, message, notifText);
  }));

  router.post('/gift', deprecated, (req, res, next) => next(notFoundError()));

  router.post('/proposal', deprecated, (req, res, next) => next(notFoundError()));

  router.delete('/proposal', deprecated, (req, res, next) => next(notFoundError()));

  router.put('/proposal/accept', deprecated, (req, res, next) => next(notFoundError()));

  router.put('/proposal/decline', deprecated, (req, res, next) => next(notFoundError()));

  router.put('/seen', asyncHandler(findChatIfExists), asyncHandler(async (req, res, next) => {
    await chatLib.clearUnreadMessages(req.chat, req.uid);

    const { chat } = req;
    if (chat && !chat.groupChat && !(premiumLib.isPremiumV1OrGodMode(req.user) && req.user.hideReadReceipts)) {
      const partnerId = chat.users.find((x) => x != req.user._id);
      const partner = await User.findById(partnerId);
      partner.metrics.leftOnReadAt = Date.now();
      await partner.save();
      if (premiumLib.isPremiumV1OrGodMode(partner)) {
        const readReceipt = {
          _id: chat._id,
          partnerNumUnreadMessages: 0,
        };
        sendSocketEvent(partnerId, 'chat read receipt', readReceipt);
      }
    }
    if (chat) {
      await Chat.updateOne({ _id: chat._id, 'perUserState.userId': req.uid }, { $set: { 'perUserState.$.unread': false } });
    }
    return res.json({});
  }));

  router.post('/sticker', asyncHandler(findApprovedChat), checkSupportChatOrVerified, asyncHandler(async (req, res, next) => {
    if (!req.body.sticker) {
      return next(badRequestError());
    }
    if (typeof req.body.sticker !== 'string') {
      return next(invalidInputError());
    }

    {
      const stickerPack = await StickerPack.findOne(
        {
          'stickers.id': req.body.sticker,
        },
        {
          _id: 0,
          premium: 1,
          productId: 1,
        },
      );
      if (!stickerPack) {
        return next(notFoundError());
      }

      if (stickerPack.premium) {
        if (!req.user.stickerPackPurchases
            || !req.user.stickerPackPurchases.includes(stickerPack.productId)) {
          return next(forbiddenError());
        }
      }
    }

    if (isInActiveAutomatedChat(req)) {
      const response = await handleInactiveAutomatedChatMessage(req.chat, req.user, null, null, 'sticker', req.body.sticker);
      return response ? res.json(response) : next(applicationError());
    }

    const otherUser = req.chat.users.find((u) => u._id != req.uid);
    let notifText = translate(
      '%s sent you a sticker!',
      otherUser?.locale,
      req.user.firstName,
    );
    if (req.chat.groupChat) {
      notifText = `${req.user.firstName} sent a sticker!`;
    }

    const message = new Message({
      chat: req.chat._id,
      text: `${notifText} [update to latest version to view]`,
      sticker: req.body.sticker,
      sender: req.uid,
    });

    await handleNewMessage(req, res, next, message, notifText);
  }));

  router.patch(
    '/unsend',
    asyncHandler(async (req, res, next) => {
      let { user } = req;
      const messageId = req.query.messageId;
      if (!messageId || !mongoose.isValidObjectId(messageId)) {
        return next(badRequestError());
      }

      let message = await Message.findById(messageId);
      if (!message) {
        return next(notFoundError('The message you are trying to unsend could not be found'));
      }

      if (message.sender !== user._id) {
        return next(badRequestError('User is not permitted to unsend this message.'));
      }

      if (message.unsent) {
        return next(badRequestError('The message has already been unsent'));
      }

      let chat = await Chat.findById(message.chat).populate('users');
      if (!chat) {
        return next(notFoundError('Chat not found'));
      }

      // Sender is unsending the message for everyone
      message.unsent = true;
      await message.save();

      const chatUsers =  chat.users.map(x => x._id)
      const hasAnyBooChatIdOrGroupChat = chat.groupChat || [chatLib.BOO_SUPPORT_ID, chatLib.BOO_BOT_ID, chatLib.BOO_TRANSLATION_ID].some(id => chatUsers.includes(id));

      if ((message._id.toString() == chat.lastMessage.toString()) && !hasAnyBooChatIdOrGroupChat && chat.perUserState?.length) {
        const previousMessage = await Message.findOne({
          chat: chat._id,
          unsent: null,
          deletedAt: null,
          createdAt: { $lt: new Date(message.createdAt) },
        })
        .sort('-createdAt');

        for (let i = 0; i < chat.perUserState.length; i++) {
          const perState = chat.perUserState[i];
          const previousState = perState.yourTurnState;
          if (previousMessage && previousMessage._id) {
            chat.perUserState[i].yourTurnState = perState.userId == previousMessage.sender ? 'messageSent' : 'uncategorized';
          } else {
            chat.perUserState[i].yourTurnState = undefined;
          }
          if ((previousState == 'uncategorized' || previousState == 'yourTurn') && chat.perUserState[i].yourTurnState != 'uncategorized') {
            await User.incrementMetric(chat.perUserState[i].userId, 'numYourTurnChats', -1);
            await chatLib.sendSocketUpdateForNumYourTurnChats(chat.perUserState[i].userId)
          } else if ((previousState != 'uncategorized' && previousState != 'yourTurn') && chat.perUserState[i].yourTurnState == 'uncategorized') {
            await User.incrementMetric(chat.perUserState[i].userId, 'numYourTurnChats', 1);
            await chatLib.sendSocketUpdateForNumYourTurnChats(chat.perUserState[i].userId)
          }
        }
        await chat.save()
      }

      // Sending socket event to participants only
      for (const chatUser of chat.users || []) {
        if (chatUser._id !== user._id) {
          sendSocketEvent(chatUser._id, 'unsent message', { messageId: message._id, chatId: chat._id });
        }
      }
      /* Disabled for now
      // If message has a notificationId only then sending silent notification
      if (message.notificationId) {
        sendMessageDeleteNotifications(user, chat, message);
      }
      */
      return res.json({});
    }),
  );

  router.patch('/edit', asyncHandler(async (req, res, next) => {
    const { text, messageId } = req.body;

    // Validate request body and messageId format
    if (!text || !messageId || !mongoose.isValidObjectId(messageId) || text.length > 10000) {
      return next(invalidInputError());
    }

    // Fetch the message from the database
    const message = await Message.findById(messageId);

    // Check if message exists
    if (!message) {
      return next(notFoundError());
    }

    // Ensure the message contains only text
    const isNonTextContent = message.hasOwnProperty('gift') || message.hasOwnProperty('call') || message.image || message.gif || message.sticker || message.audio || message.video;
    if (message.sender !== req.uid || message.unsent || isNonTextContent) {
      return next(forbiddenError());
    }

    let chat = await Chat.findById(message.chat).populate('users');
    if (!chat) {
      return next(notFoundError());
    }

    // Update the message
    if (!message.editLogs) {
      message.editLogs = [];
    }
    message.editLogs.push({ text: message.text, editedAt: new Date() });
    message.text = text;
    message.edited = true;
    await message.save();

    // Sending socket event to participants only
    for (const chatUser of chat.users || []) {
      if (chatUser._id !== req.uid) {
        sendSocketEvent(chatUser._id, 'message edited', { messageId: message._id, chatId: chat._id, updatedMessage: text });
      }
    }

    res.json({});

    // Log and report actions
    await logMessageIfKeywordFound(message, req.user);
  }));

  router.put('/reaction', checkVerified, asyncHandler(async (req, res, next) => {
    const messageId = req.body.messageId;
    const reaction = req.body.reaction.trim()

    if(!isSingleEmoji(reaction)){
      return next(invalidInputError('invalid emoji'));
    }

    if (!messageId || !mongoose.isValidObjectId(messageId)) {
      return next(invalidInputError('invalid messageId'));
    }
    const message = await Message.findById(messageId);

    if(message.sender === chatLib.BOO_BOT_ID){
      return next(forbiddenError());
    }

    const isDuplicate = message.reactions.find(
      r => r.sender === req.user._id.toString() && r.reaction === reaction
    );
    if(isDuplicate) {
      return next(invalidInputError('duplicate reaction'));
    }

    try {
      await Message.findOneAndUpdate(
        { _id: messageId },
        {
          $push: { reactions: { sender: req.user._id, reaction } }
        },
      );

      const chat = await Chat.findById(message.chat).populate('users')
      const otherUsers = chat.users.filter((x) => x._id != req.user._id);
      for (const otherUser of otherUsers) {
        if(otherUser.versionAtLeast('1.13.77')){
          chatLib.incrementUnreadMessages(chat, otherUser._id);
        }
      }

      //set chat.lastMessageReaction
      chat.lastMessageReaction = {sender: req.user._id, firstName: req.user.firstName, reaction}
      chat.lastMessageTime = Date.now();

      //check if sender stil on chat
      const isSenderInChat = chat.users.find((x) => x._id === message.sender)

      //send notification if other user reacted on my messages
      if(message.sender !== req.user._id && isSenderInChat){
        //get sender user data
        const msgSender = await User.findById(message.sender)
        if(msgSender){
          sendReactionNotifications(req.user, msgSender, chat, message, reaction);
        }
      }

      await chat.save()

      res.json({});

    } catch (error) {
      console.log(`failed to add reaction '${reaction}' on messageId ${messageId}, error: ${error} `)
      return next(applicationError());
    }
  }));

  router.delete('/reaction', asyncHandler(async (req, res, next) => {
    const { reaction,messageId } = req.body;

    if (!messageId || !mongoose.isValidObjectId(messageId)) {
      return next(invalidInputError('invalid messageId'));
    }

    try {
      const message = await Message.findById(messageId);
      const reactionIndex = message.reactions.findIndex(r => r.sender === req.user._id.toString() && r.reaction === reaction);

      if (reactionIndex !== -1) {
        const removedReaction = message.reactions.splice(reactionIndex, 1)[0];
        const oldReaction = removedReaction.reaction;
        const chat = await Chat.findById(message.chat).populate('users')
        const otherUsers = chat.users.filter((x) => x._id != req.user._id);
        for (const otherUser of otherUsers) {
          if(otherUser.versionAtLeast('1.13.77')){
            chatLib.decrementUnreadMessages(chat, otherUser._id);
          }

        }

        if(chat.lastMessageReaction?.sender?.toString() === removedReaction?.sender?.toString() && chat.lastMessageReaction?.reaction === removedReaction?.reaction) {
          //set chat.lastMessageReaction empty
          chat.lastMessageReaction = undefined

        }

        await chat.save()

        // Save the message
        await message.save();

        // Sending socket event to participants only
        for (const chatUser of chat.users || []) {
          if (chatUser._id !== req.user._id) {
            sendSocketEvent(chatUser._id, 'reaction removed', { messageId: message._id, chatId: chat._id, removedReaction });
          }
        }

        res.json({});
      } else {
        return next(invalidInputError('reaction not found'));
      }


    } catch (error) {
      console.log(`failed to remove reaction messageId ${messageId}, error: ${error} `)
      return next(applicationError());
    }
  }));



  return router;
};
