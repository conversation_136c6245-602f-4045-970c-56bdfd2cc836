const { initializeOpenTelemetry } = require('./tracing');

// Initialize OpenTelemetry before setting up the rest of the application
initializeOpenTelemetry().then(() => {
  // Import and set up the main application components after OpenTelemetry initialization
  const app = require('express')();

  const mongoose = require('mongoose');
  const blocked = require('blocked');
  const { createServer, configureRoutes } = require('./lib/server');

  const server = createServer(app);
  configureRoutes(app);

  const port = process.env.PORT || 3000;
  const geocoder = require('./lib/geocoder');
  const interestLib = require('./lib/interest');
  const metricsLib = require('./lib/metrics');
  const { updateDailyExchangeRate} = require('./lib/currency-exchange');
  const { loadCategoriesFromDatabase } = require('./lib/database');
  const { loadAndCacheTranslatedCities } = require('./lib/translated-locations');

  // Database configuration
  // =============================================================================
  const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

  mongoose.connection.on('connecting', () => {
    console.log('dbevent: connecting, connection state: ', mongoose.connection.readyState);
  });
  mongoose.connection.on('connected', () => {
    console.log('dbevent: connected, connection state: ', mongoose.connection.readyState);
  });
  mongoose.connection.on('disconnecting', () => {
    console.log('dbevent: disconnecting, connection state: ', mongoose.connection.readyState);
  });
  mongoose.connection.on('disconnected', () => {
    console.log('dbevent: disconnected, connection state: ', mongoose.connection.readyState);
  });
  mongoose.connection.on('reconnected', () => {
    console.log('dbevent: reconnected, connection state: ', mongoose.connection.readyState);
  });
  mongoose.connection.on('error', (err) => {
    console.log(`dbevent: error: ${err}, connection state: `, mongoose.connection.readyState);
  });
  mongoose.connection.on('fullsetup', () => {
    console.log('dbevent: fullsetup, connection state: ', mongoose.connection.readyState);
  });
  mongoose.connection.on('all', () => {
    console.log('dbevent: all, connection state: ', mongoose.connection.readyState);
  });
  mongoose.connection.on('reconnectFailed', () => {
    console.log('dbevent: reconnectFailed, connection state: ', mongoose.connection.readyState);
  });

  mongoose.connection.once('open', async () => {
    console.log('dbevent: open, connection state: ', mongoose.connection.readyState);

    console.log('Loading categories from database');
    await loadCategoriesFromDatabase();
    console.log('Finished loading categories from database');

    console.log('Loading interests from database');
    await interestLib.loadInterestsFromDatabase();
    console.log('Finished loading interests from database');

    console.log('Loading currency exchange data');
    await updateDailyExchangeRate();
    console.log('Finished loading currency exchange data');

    await metricsLib.loadMostRecentMetrics();

    console.log('Initializing geocoder');
    geocoder.init({
      load: {
        admin1: true, admin2: false, admin3And4: false, alternateNames: false,
      },
    }, () => {
      console.log('Finished initializing geocoder');

    loadAndCacheTranslatedCities().then(() => {
      console.log('Finished caching translated locations');
    }).catch((err) => {
      console.log('Failed to cache translated locations', err);
    });

      server.listen(port, () => {
        console.log('Express started. Listening on %s', port);
        blocked(function(lag) {
          console.log(`Event loop lag: ${lag} ms`);
        });
      });
    });
  });

  mongoose.connect(
    MONGODB_URI,
    {
      autoIndex: true,
      compressors: ['snappy'],
    },
  ).catch((err) => {
    console.log(`dbevent: initial connection error. Shutting down server. ${err}`);
    process.exit(-1);
  });
});
