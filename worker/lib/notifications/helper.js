/* eslint-disable indent */
const fs = require('fs');
// eslint-disable-next-line import/no-unresolved
const { parse } = require('csv-parse/sync');
const promptsLib = require('../../../lib/prompts');
const User = require('../../../models/user');
const Message = require('../../../models/message');
const AiNotification = require('../../../models/ai-notifications');
const { mapLanguageCodeToName } = require('../../../lib/languages');
const openaiClient = require('../../../lib/openai-client');
const { OpenAI } = require('../../../lib/prompt');

let NOTES = {};
let THEMES = {};
const LABEL_BASE = 'daily-push-ai-v2-';

const client = openaiClient.getOpenaiClient();
const openAi = new OpenAI(client, 'gpt-4o-mini');

const loadThemes = () => {
  try {
    const fileData = parse(fs.readFileSync(`${__dirname}/daywise-theme.csv`, 'utf8'), {
      delimiter: ',',
      columns: true,
      skip_empty_lines: true,
    });

    fileData.forEach((item) => {
      THEMES[item.Date] = {
        primaryTopic: item['Primary Prompt Topic']?.trim(),
        holidayDetails: item['Holiday Details']?.trim(),
        alternativeTopic: item['Alternative Prompt Topic']?.trim(),
        tone: item['MBTI tone notes']?.trim(),
      };
    });
  } catch (error) {
    console.log(`Error reading daywise-theme.csv: ${error.message}`);
  }
};

const loadNotes = () => {
  try {
    const fileData = parse(fs.readFileSync(`${__dirname}/mbti-notes.csv`, 'utf8'), {
      delimiter: ',',
      columns: true,
      skip_empty_lines: true,
    });

    fileData.forEach((item) => {
      if (!NOTES[item.MBTI]) {
        NOTES[item.MBTI] = {};
      }

      const notesData = {
        notes: item['MBTI plus tone notes']?.trim(),
        warnings: item.Warnings || '',
      };

      for (let i = 1; i <= 13; i++) {
        notesData[i] = item[i.toString()];
      }

      NOTES[item.MBTI] = { ...NOTES[item.MBTI], ...notesData };
    });
  } catch (error) {
    console.log(`Error reading mbti-notes.csv: ${error.message}`);
  }
};

const getMbtiNote = (mbti) => {
  try {
    if (Object.keys(NOTES).length === 0) {
      loadNotes();
    }
    return NOTES[mbti] || null;
  } catch (error) {
    console.log(`Error fetching MBTI note for ${mbti}: ${error.message}`);
    return null;
  }
};

const getTodaysTheme = (key) => {
  try {
    if (Object.keys(THEMES).length === 0) {
      loadThemes();
    }
    return THEMES[key] || null;
  } catch (error) {
    console.log(`Error fetching theme for key ${key}: ${error.message}`);
    return null;
  }
};

const getAnalyticsLabel = (theme) => {
  if (!theme || !theme.primaryTopic) return null;
  const formatLabel = (str) => str?.toLowerCase().replace(/[\s/]+/g, '-');
  return LABEL_BASE + formatLabel(theme.primaryTopic);
};

const getUserBioOrPrompt = (user) => {
  const bioParts = [];
  if (user.description) bioParts.push(user.description);
  if (user.audioDescriptionTranscription) bioParts.push(`AI Transcript: ${user.audioDescriptionTranscription}`);
  const bio = bioParts.length ? `Bio: ${bioParts.join('\n')}` : null;

  let singlePrompt = null;
  if (Array.isArray(user.prompts) && user.prompts.length) {
    const validPrompts = user.prompts
      .map((prompt) => {
        const promptText = promptsLib.getPromptText(prompt);
        return promptText ? `${promptText}: ${prompt.answer}` : null;
      })
      .filter(Boolean);

    if (validPrompts.length) {
      singlePrompt = `Bio Prompt: ${validPrompts[Math.floor(Math.random() * validPrompts.length)]}`;
    }
  }

  const options = [bio, singlePrompt].filter(Boolean);
  return options.length ? options[Math.floor(Math.random() * options.length)] : null;
};

const getRandomElements = (arr, n) => {
  if (!Array.isArray(arr) || arr.length === 0) return [];
  return arr.length <= n ? arr : [...arr].sort(() => 0.5 - Math.random()).slice(0, n);
};

const getUserWisePromptTopic = async (user, key, theme) => {
  switch (key) {
    case 'Holiday':
      return {
        topic: `Holiday: ${theme.holidayDetails}`,
        label: 'holiday',
      };

    case '1 interest':
    case '2 interests':
    case '3 interests': {
      const n = parseInt(key[0], 10);
      const interests = getRandomElements(user.interestNames, n);
      return {
        topic: interests.length ? `Interests: ${interests.join(', ')}` : '',
        label: `${n}-interest${n > 1 ? 's' : ''}`,
      };
    }

    default:
      return { topic: null, label: 'unknown' };
  }
};

const prepareProfile = (user, theme) => {
  const profileParts = [];
  if (user.age) profileParts.push(`Age: ${user.age}`);
  if (user.gender) profileParts.push(`Gender: ${user.gender}`);

  const preferences = [];
  const formatPreference = (type, values, subPreferences) => {
    if (!values?.length) return null;

    const isOnlyNonBinary = values.length === 1 && values[0] === 'non-binary';
    const mainText = subPreferences?.length ? `${type}: ${subPreferences}` : type;

    return isOnlyNonBinary ? mainText : `${mainText} with ${values.join(', ')}`;
  };

  const datingPreference = formatPreference('Dating', user.preferences?.dating, user.datingSubPreferences);
  const friendsPreference = formatPreference('Friends', user.preferences?.friends);

  if (datingPreference) preferences.push(datingPreference);
  if (friendsPreference) preferences.push(friendsPreference);
  if (preferences.length) profileParts.push(`Looking For: ${preferences.join(' / ')}`);

  const mbtiNote = getMbtiNote(user.personality?.mbti);
  if (mbtiNote) {
    const tovNote = mbtiNote[theme.tone];
    profileParts.push(
      `MBTI Personality Type: ${user.personality.mbti} ${mbtiNote.notes} ${tovNote}.${mbtiNote.warnings.length ? ` ${mbtiNote.warnings}` : ''}`,
    );
  }

  return profileParts.join('\n');
};

const preparePrompt = async (user, theme) => {
  const userLocale = mapLanguageCodeToName(user.locale || 'en');
  const { topic, label } = await getUserWisePromptTopic(user, theme.primaryTopic, theme);

  const allExamples = [
    `{ output: ["Compatible Looks Like", "Someone who is everything you're not, yet somehow just the same. What are you waiting for? Say hi to your compatible match 👋"] }`,
    `{ output: ["When You Find Your Rhythm", "It’s with someone who shares your drive and your playlist. Connect with a friend who vibes like you do. 🎧" }`,
    `{ output: ["When Sparks Fly Fast", "You feel it in the moment—chemistry, energy, connection. Someone bold and beautiful is waiting to match your vibe 🔥" }`,
    `{ output: ["You Know You've Met Your Match When", "He’s already planning your next food crawl and hyping your playlists like a personal DJ. Time to connect. 🎶" }`,
    `{ output: ["You Know It's A Spark When", "She’s up for an impulsive road trip and can’t stop talking about her dog. She’s waiting for your message. 🐾" }`,
  ];

  const shuffledExamples = getRandomElements(allExamples, 3).join('\n\n');

  const promptText = `I need a casual push notification for Boo, a dating/friendship app. The notification should be uniquely tailored to the specific user based on their MBTI personality type, the Prompt Topic indicated below, and whether they're looking for dating or friendship and with which gender e.g. find a beautiful girl, hot guy, anime chick. Do not imply the user has new messages or matches that they didn’t know about.

Write the title as the opening phrase or clause of a sentence (do not end it with a period). Write the message to complete that sentence naturally, but starting with an uppercase letter. Together, the title and message should form one smooth, complete sentence when read in sequence. The output should be formatted as a json object in the following format: { output: ["title", "message"] }. Here are some examples:

${shuffledExamples}

Check that you have adhered to this cohesive style requirement in your output. Also note the selective use of one emoji where appropriate.

The overall message must feel natural, engaging and uniquely tailored to this user and what they are looking for from using our app. Prioritize authenticity and do not overuse exclamation points. Use the user’s data and the prompt topic to create a message that feels personal and relevant, not generic. Bring the user’s lifestyle and vibe to life through imagery or metaphor that connects to the feature. Write as if you know the user’s personality and preferences intimately, so the notification feels like a friend’s thoughtful suggestion.

Here are the user's details, and your output must be in ${userLocale}:

${prepareProfile(user, theme)}${topic?.length ? `\n\nPrompt Topic:\n\n${topic}` : ''}`;

  return { promptText, primaryTopic: theme.primaryTopic, label };
};

const generateAiNotification = async (user, theme) => {
  const result = {};
  try {
    let isError = false;
    let errorMessage = null;
    let cost = 0;

    const { promptText, primaryTopic, label } = await preparePrompt(user, theme);

    const analyticsLabel = LABEL_BASE + label;
    const response = await openAi.executePrompt({
      prompt: promptText,
      response_format: { type: 'json_object' },
    });

    if (response) {
      isError = response.errorMessage !== undefined;
      errorMessage = response.errorMessage;
      cost = response.cost || 0;
      if (response.output) {
        try {
          const parsed = JSON.parse(response.output.replace('```json', '').replace('```', ''));
          result.output = parsed?.output;
        } catch (err) {
          isError = true;
          errorMessage = `json parsing error: ${err.message}`;
        }
      }
    }
    result.analyticsLabel = analyticsLabel;
    await new AiNotification({
      user: user._id,
      primaryTopic,
      analyticsLabel,
      prompt: promptText,
      output: response.output,
      isError,
      errorMessage,
      cost,
      response: JSON.stringify(response),
      dailyPushV2: true,
    }).save();
  } catch (error) {
    console.log(`Error generating AI notification: ${error.message} for user ${user._id}`);
  }
  return result;
};

module.exports = { getTodaysTheme, getAnalyticsLabel, generateAiNotification };
