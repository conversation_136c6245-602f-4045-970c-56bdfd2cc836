const {
  app, loadInterests, validGif, validResponseGif, validTenorGif, validTenorResponseGif, validImagePath, validAudioPath, initSocket, destroySocket, getSocketPromise, createUser, getProfilePreview
} = require('./common');
const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const temp = require('temp').track();
const fs = require('fs');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const Question = require('../models/question');
const Comment = require('../models/comment');
const Interest = require('../models/interest');
const Notification = require('../models/notification');
const SocialQueryCache = require('../models/social-query-cache');
const interestLib = require('../lib/interest');
const userLib = require('../lib/user');
const constants = require('../lib/constants');
const { pageSize, REPORTS_UNTIL_DELETION } = require('../lib/constants');
const { createQuestion, backfillAllUserKarma, backfillLinkedKeywords, backfillInterestNumQuestions, backfillQuestionHashtags, backfillInterestNumQuestionsPerLanguage, backfillLinkedPillarKeywords, backfillLinkedExploreKeywords } = require('../lib/social');
const { notifs, reset, waitFor } = require('./stub');
const coinsConstants = require('../lib/coins-constants');
const { likeComment } = require('./helper/api');
const basic = require('../lib/basic');
const InterestCountryCount = require('../models/interest-country-count');

describe('APP-701', () => {
  it('should hide blocked/hidden users from interest feed', async () => {
    for (let i = 0; i < 5; i++) {
      await createUser(i);
    }

    for (let i = 0; i < 5; i++) {
      const res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', i)
        .send({
          interestNames: ['kpop'],
        });
      expect(res.status).to.equal(200);
    }

    let res = await request(app)
      .get('/v1/interest/users')
      .set('authorization', 0)
      .query({ interestName: 'kpop' });
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(5);
    expect(res.body.users[0]).to.eql({
      _id: '4',
      firstName: '4',
      handle: 'handle4',
      personality: { mbti: 'ESTJ' },
      enneagram: '1w9',
      gender: 'female',
      picture: 'picture0',
      horoscope: 'Capricorn',
      age: 31
    });

    // // Now user 0 blocks user 2
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    // Verify user 2 is not returned when user 0 queries
    res = await request(app)
      .get('/v1/interest/users')
      .set('authorization', 0)
      .query({ interestName: 'kpop' });
    expect(res.status).to.equal(200);
    console.log('Response ', res.body);

    // Check that user 2 is not in the results
    const user2InResults = res.body.users.some(user => user._id === '2');
    expect(user2InResults).to.equal(false);

    // Now user 3 blocks user 0
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 3)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    // Verify user 3 is not returned when user 0 queries (bidirectional blocking)
    res = await request(app)
      .get('/v1/interest/users')
      .set('authorization', 0)
      .query({ interestName: 'kpop' });
    expect(res.status).to.equal(200);

    // Check that user 3 is not in the results
    const user3InResults = res.body.users.some(user => user._id === '3');
    expect(user3InResults).to.equal(false);

    const user4 = await User.findById('4');
    user4.email = '<EMAIL>';
    await user4.save();

    res = await request(app)
      .patch('/v1/user/hideContacts')
      .set('authorization', 0)
      .send({
        emails: ['<EMAIL>'],
        numbers: [],
      });
    expect(res.status).to.equal(200);

    // User 4 is hidden, verify user 4 is not returned when user 0 queries
    res = await request(app)
      .get('/v1/interest/users')
      .set('authorization', 0)
      .query({ interestName: 'kpop' });
    expect(res.status).to.equal(200);

    // Check that user 4 is not in the results
    const user4InResults = res.body.users.some(user => user._id === '4');
    expect(user4InResults).to.equal(false);
  });
});

describe('APP 490: Add Anime and Move the Gaming Interest Category', () => {
  beforeEach(async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    const user = await User.findById('0');
    user.ipData.country = 'United States';
    user.locale = 'en';
    await user.save();
  });

  it('should return popular interests from onboarding if no interest for country found', async () => {
    const user = await User.findById('0');
    expect(user.ipData.country).to.eql('United States');
    expect(user.locale).to.eql('en');

    const interests = await InterestCountryCount.find({ country: user.ipData.country, locale: user.locale, count: { $gt: 0 } });
    expect(interests.length).to.eql(0);

    let res = await request(app)
      .get('/v1/interest/onboardingInterests')
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    expect(res.body[0]).to.have.property('category', 'Popular');
    expect(res.body[0].interestNames).to.not.deep.equal([]);
    expect(res.body[0].interestNames).to.deep.equal(interestLib.getPopularInterestsForOnboarding(user.locale));

    // test translations
    user.locale = 'bn';
    await user.save();

    res = await request(app)
      .get('/v1/interest/onboardingInterests')
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    const popularInterests = interestLib.getPopularInterestsForOnboarding(user.locale);
    expect(res.body[0]).to.have.property('category', 'Popular');
    expect(res.body[0].interestNames).to.deep.equal(popularInterests);
    expect(res.body[0].interestNames[0]).to.equal('সঙ্গীত');
    expect(res.body[0].interestNames[popularInterests.length - 1]).to.equal('পদার্থবিদ্যা');
  });

  it('should return popular interests and all onboarding interests', async () => {
    let res = await request(app)
      .get('/v1/interest/onboardingInterests')
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    expect(res.body).to.be.an('array');
    expect(res.body[0]).to.have.property('category', 'Popular');
    expect(res.body[0].interestNames).to.deep.equal(interestLib.getPopularInterestsForOnboarding('en'));
    expect(res.body.length).to.equal(15);
    const onboardingCategories = ['Popular', 'Topics', 'Creativity', 'Film & Literature', 'Music', 'Activities', 'Gaming', 'Food & Drink', 'Pets', 'Causes', 'Sports', 'MBTI', 'Enneagram', 'Astrology', 'Anime'];
    for (let i = 1; i < res.body.length; i++) {
      expect(res.body[i]).to.have.property('category');
      expect(res.body[i].interestNames).to.be.an('array');
      expect(res.body[i].category).to.equal(onboardingCategories[i]);
    }

    await Interest.insertMany([
      { interest: '#dragonage', name: 'dragonage', libCategory: 'gaming', numFollowers: 3 },
      { interest: '#watchdogs', name: 'watchdogs', libCategory: 'gaming', numFollowers: 1 },
      { interest: '#dota2', name: 'dota2', libCategory: 'gaming', numFollowers: 2 },
      { interest: '#yourturntodie', name: 'yourturntodie', libCategory: 'gaming', numFollowers: 5 },
      { interest: '#gaslands', name: 'gaslands', libCategory: 'gaming', numFollowers: 5 },
      { interest: '#fifa23', name: 'fifa23', libCategory: 'gaming', numFollowers: 5 },
      { interest: '#esport', name: 'esport', libCategory: 'gaming', numFollowers: 5 },
      { interest: '#efootball', name: 'efootball', libCategory: 'gaming', numFollowers: 5 },
      { interest: '#physics', name: 'physics', libCategory: 'science', numFollowers: 5 },
      { interest: '#biology', name: 'biology', libCategory: 'science', numFollowers: 5 },
      { interest: '#check0', name: 'check0', numFollowers: 5 },
      { interest: '#check1', name: 'check1', numFollowers: 5 },
      { interest: '#check2', name: 'check2', numFollowers: 5 },
      { interest: '#check3', name: 'check3', numFollowers: 5 },
      { interest: '#check4', name: 'check4', numFollowers: 5 },
      { interest: '#check5', name: 'check5', numFollowers: 5 },
      { interest: '#check6', name: 'check6', numFollowers: 5 },
      { interest: '#check7', name: 'check7', numFollowers: 5 },
    ]);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({ interestNames: ['dragonage', 'watchdogs', 'dota2', 'biology'] });
    expect(res.status).to.equal(200);

    for (let i = 1; i < 4; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);

      const user = await User.findById(i);
      user.ipData.country = 'United States';
      user.locale = 'en';
      await user.save();

      let interestNames = ['dragonage', 'watchdogs', 'biology'];
      if (i % 2 === 0) {
        interestNames = ['dragonage', 'watchdogs', 'dota2', 'gaslands', 'fifa23', 'esport', 'efootball', 'check0', 'physics']
      }

      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', i)
        .send({ interestNames });
      expect(res.status).to.equal(200);
    }

    const expected = ['dragonage', 'watchdogs', 'biology', 'dota2', 'gaslands', 'fifa23', 'esport', 'efootball', 'check0', 'physics'];
    res = await request(app)
      .get('/v1/interest/onboardingInterests')
      .set('authorization', '0')
      .query({ app_315: 'true' });
    expect(res.status).to.equal(200);
    expect(res.body[0]).to.have.property('category', 'Popular');
    expect(res.body[0].interestNames).to.eql(expected);
  });

  it('should reorder Gaming category to be first if partner campaign is gaming', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '1')
      .send({ locale: 'en', appVersion: '1.13.65', kochava: { network: 'fb', partner_campaign_name: 'C. US L. English || Interest Game || App' } });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/interest/onboardingInterests')
      .set('authorization', '1');
    expect(res.status).to.equal(200);

    expect(res.body[0]).to.have.property('category', 'Gaming');
    expect(res.body[0].interestNames[0]).to.equal('gaming');
  });

  it('should reorder Anime category to be first if app_483 is true', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '1')
      .send({ locale: 'en', appVersion: '1.13.65', kochava: { network: 'fb', partner_campaign_name: 'C. US L. English || Interest Anime || App' } });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/interest/onboardingInterests')
      .set('authorization', '1');
    expect(res.status).to.equal(200);

    expect(res.body[0]).to.have.property('category', 'Anime');
    expect(res.body[0].interestNames[0]).to.equal('onepiece');
  });

  it('should translate interestName based on user locale', async () => {
    const user = await User.findById('0');
    user.locale = 'bn';
    await user.save();

    let res = await request(app)
      .get('/v1/interest/onboardingInterests')
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    expect(res.body[0]).to.have.property('category', 'Popular');
    expect(res.body[0].interestNames).to.deep.equal(interestLib.getPopularInterestsForOnboarding('bn'));

    expect(res.body[1]).to.have.property('category', 'Topics');
    expect(res.body[1].interestNames[0]).to.equal('মিমস');

    expect(res.body[2]).to.have.property('category', 'Creativity');
    expect(res.body[2].interestNames[0]).to.equal('শিল্প');

    // should be translated for gaming campaign too
    user.partnerCampaign = 'gaming';
    await user.save();

    res = await request(app)
      .get('/v1/interest/onboardingInterests')
      .set('authorization', '0');
    expect(res.status).to.equal(200);

    expect(res.body[0]).to.have.property('category', 'Gaming');
    expect(res.body[0].interestNames[0]).to.equal('গেমিং');
    expect(res.body[0].interestNames[1]).to.equal('বোর্ডগেম');
  });
});

let user;

it('interest names', async () => {
  const numUsers = 2 + REPORTS_UNTIL_DELETION;
  const profiles = {};
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);

    profiles[uid.toString()] = {
      _id: uid.toString(),
      firstName: `name${uid.toString()}`,
      pictures: [],
      personality: null,
      gender: null,
      age: null,
      description: '',
      education: '',
      prompts: [],
      interests: [],
      interestNames: [],
      crown: false,
      hideQuestions: false,
      hideComments: false,
      handle: res.body.user.handle,
      teleport: false,
      preferences: { purpose: [] },
      horoscope: null,
      location: null,
      karma: 0,
      numFollowers: 0,
      verified: false,
      verificationStatus: 'unverified',
      relationshipStatus: 'Single',
      datingSubPreferences: 'Short term fun',
      relationshipType: 'Polyamorous',
    };

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', uid)
      .send({ fcmToken: `token${uid.toString()}` });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', uid)
      .send({ firstName: `name${uid.toString()}` });
    expect(res.status).to.equal(200);
    res = await request(app)
    .put('/v1/user/relationshipStatus')
    .set('authorization', uid)
    .send({ relationshipStatus: 'Single' });
    expect(res.status).to.equal(200);
    res = await request(app)
    .put('/v1/user/relationshipType')
    .set('authorization', uid)
    .send({ relationshipType: 'Polyamorous' });
    expect(res.status).to.equal(200);
    res = await request(app)
    .put('/v1/user/datingSubPreferences')
    .set('authorization', uid)
    .send({ datingSubPreferences: 'Short term fun' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.interestNames).to.eql([]);
  const { interests } = res.body;
  interestNames = ['kpop', 'latin', 'chess'];

  // invalid interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['#chess'],
    });
  expect(res.status).to.equal(422);

  // valid interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop', 'latin'],
    });
  expect(res.status).to.equal(200);
  profiles['0'].interests = [interests[0], interests[1]];
  profiles['0'].interestNames = ['kpop', 'latin'];

  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 1)
    .send({
      interestNames: ['chess', 'latin'],
    });
  expect(res.status).to.equal(200);
  profiles['1'].interests = [interests[2], interests[1]];
  profiles['1'].interestNames = ['chess', 'latin'];

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.interests).to.eql(profiles['0'].interests);
  expect(res.body.user.interestNames).to.eql(profiles['0'].interestNames);

  // no questions yet
  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  for (const interestName of interestNames) {
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([]);
  }

  // post question 1
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;
  const q1 = {
    _id: q1Id,
    webId: res.body.webId,
    url: res.body.url,
    createdAt: res.body.createdAt,
    createdBy: profiles['0'],
    interest: interests[0],
    interestName: 'kpop',
    title: 'title1',
    text: 'text1',
    numComments: 0,
    numLikes: 0,
    isDeleted: false,
    isEdited: false,
    hasUserLiked: false,
    hasUserSaved: false,
    language: 'en',
    linkedKeywords: [],
    linkedExploreKeywords: [],
    linkedPillarKeywords: [],
    linkedCategories: [],
    linkedSubcategories: [],
    linkedProfiles: [],
    hashtags: ['kpop'],
    images:[]
  };
  expect(res.body).to.eql(q1);
  expect(q1.url).to.equal(`https://boo.world/u/kpop/${q1.webId}/title1-text1`);

  // user can see question in feed
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'kpop' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'chess' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'explore' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'following' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  // user without kpop interest
  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'explore' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'following' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  // shadow banned user can still see own post, others cannot
  u0 = await User.findById('0');
  u0.shadowBanned = true;
  await u0.save();

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'kpop' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'kpop' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  u0.shadowBanned = false;
  await u0.save();

  // post non-existant interest group
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(400);
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: '404',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(422);

  // make post a qod
  await Question.updateOne({ _id: q1._id }, { $set: { interestName: 'questions' } });

  // edit qod post

  res = await request(app)
    .patch('/v1/question/edit')
    .set('authorization', 0)
    .send({ questionId: q1._id, title: 'title2', text: 'text1' });
  expect(res.status).to.equal(403);

  // restore post
  await Question.updateOne({ _id: q1._id }, { $set: { interestName: q1.interestName } });

  // edit own post
  res = await request(app)
    .patch('/v1/question/edit')
    .set('authorization', 0)
    .send({ questionId: q1._id, title: 'title2', text: 'text1' });
  expect(res.status).to.equal(200);

  q1.title = 'title2';
  q1.isEdited = true;
  q1.url = q1.url.replace('title1', 'title2');

  // edit other's post fails
  res = await request(app)
    .patch('/v1/question/edit')
    .set('authorization', 1)
    .send({ questionId: q1._id, title: 'title4', text: 'text4' });
  expect(res.status).to.equal(404);

  // load question of day
  newQuestion = await createQuestion({
    createdAt: new Date(),
    text: 'qod 1',
    interestName: 'questions',
  });
  await newQuestion.save();
  await Question.updateSearchFields(newQuestion._id);

  // question of day should appear in feed
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  const qod1Id = res.body.questions[0]._id;
  const qod1 = {
    _id: qod1Id,
    webId: res.body.questions[0].webId,
    url: res.body.questions[0].url,
    createdAt: res.body.questions[0].createdAt,
    createdBy: null,
    interest: null,
    text: 'qod 1',
    numComments: 0,
    numLikes: 0,
    isDeleted: false,
    isEdited: false,
    hasUserLiked: false,
    hasUserSaved: false,
    language: 'en',
    interestName: 'questions',
    linkedKeywords: [],
    linkedExploreKeywords: [],
    linkedPillarKeywords: [],
    linkedCategories: [],
    linkedSubcategories: [],
    linkedProfiles: [],
    hashtags: ['questions'],
    images: [],
  };
  expect(res.body.questions).to.eql([qod1]);
  expect(qod1.url).to.equal(`https://boo.world/u/questions/${qod1.webId}/qod-1`);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'kpop' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'chess' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([qod1, q1]);

  // edit qod fails
  res = await request(app)
    .patch('/v1/question/edit')
    .set('authorization', 1)
    .send({ questionId: qod1Id, title: 'title4', text: 'text4' });
  expect(res.status).to.equal(404);

  // test pagination
  for (let i = 0; i < pageSize * 2; i++) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: i.toString(),
        text: i.toString(),
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'kpop' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  // get first page
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'chess' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize * 2 - 1 - i).toString());
  }

  // get second page
  before = res.body.questions[pageSize - 1].createdAt;
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'chess', before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
  }

  // try get third page - no more
  before = res.body.questions[pageSize - 1].createdAt;
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'chess', before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  // create an older and future qod
  newQuestion = await createQuestion({
    createdAt: new Date(2020, 5, 20, 4, 5, 0, 0),
    text: 'qod 2',
    interestName: 'questions',
  });
  await newQuestion.save();

  newQuestion = await createQuestion({
    createdAt: new Date(9999, 5, 20, 4, 5, 0, 0),
    text: 'qod 3',
    interestName: 'questions',
  });
  await newQuestion.save();

  // feed - get first page
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize + 1);
  expect(res.body.questions[0]).to.eql(qod1);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[1 + i].text).to.equal((pageSize * 2 - 1 - i).toString());
    expect(res.body.questions[1 + i].interestName).to.equal('chess');
  }

  // get second page
  before = res.body.questions[pageSize].createdAt;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
    expect(res.body.questions[1].interestName).to.equal('chess');
  }

  // third page
  before = res.body.questions[pageSize - 1]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ beforeId: before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0].interestName).to.equal('kpop');

  // afterId - 2nd page
  afterId = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0)
    .query({ afterId });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
    expect(res.body.questions[1].interestName).to.equal('chess');
  }

  // afterId - 1st page (does not include qod)
  afterId = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0)
    .query({ afterId });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize * 2 - 1 - i).toString());
    expect(res.body.questions[i].interestName).to.equal('chess');
  }

  // afterId - no more
  afterId = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0)
    .query({ afterId });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  // afterId for qod - no more
  res = await request(app)
    .get('/v1/question/allQuestions')
    .set('authorization', 0)
    .query({ afterId: qod1Id, sort: 'recent' });
  expect(res.status).to.equal(200);
  console.log(res.body.questions);
  expect(res.body.questions.length).to.equal(0);

  // following should only return kpop question
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'following' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  // view likes
  reset();
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', '1')
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);
  q1.hasUserLiked = true;
  q1.numLikes += 1;

  resetTime = Date.now();
  await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
  expect(notifs.recent.token).to.equal('token0');
  expect(notifs.recent.notification.title).to.equal('name1 loved your post');
  expect(notifs.recent.notification.body).to.equal('You: title2');
  expect(notifs.recent.apns.payload.aps['thread-id']).to.equal(q1Id);
  expect(JSON.parse(notifs.recent.data.question)).to.eql({
    _id: q1Id,
    interest: interests[0],
    interestName: 'kpop',
  });
  expect(notifs.numSent).to.equal(1);
  reset();

  res = await request(app)
    .get('/v1/question/likes')
    .query({ questionId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.totalPages).to.equal(1);
  expect(res.body.usersThatLiked).to.eql([profiles['1']]);

  res = await request(app)
    .get('/v1/question/likes')
    .query({ questionId: q1Id })
    .set('authorization', 1);
  expect(res.body.totalPages).to.equal(0);
  expect(res.body.usersThatLiked).to.eql([]);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications.length).to.equal(1);
  expect(res.body.notifications[0].postType).to.equal('question');
  expect(res.body.notifications[0].notificationType).to.equal('like');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile).to.eql(profiles['1']);

  // comment on a question
  reset();
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;
  q1.numComments += 1;

  // get updated karma
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  profiles['0'].karma = res.body.user.karma;

  resetTime = Date.now();
  await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
  expect(notifs.recent.token).to.equal('token0');
  expect(notifs.recent.notification.title).to.equal('title2');
  expect(notifs.recent.notification.body).to.equal('name1: @name0 comment1');
  expect(notifs.recent.apns.payload.aps['thread-id']).to.equal(q1Id);
  expect(JSON.parse(notifs.recent.data.comment)).to.eql({
    _id: c1Id,
    question: q1Id,
    parent: q1Id,
    postRepliedTo: q1Id,
    interest: interests[0],
    interestName: 'kpop',
  });
  expect(notifs.numSent).to.equal(1);
  reset();

  // not able to ban qod
  for (let i = 0; i < REPORTS_UNTIL_DELETION; i++) {
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 2 + i)
      .send({ questionId: qod1Id });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/question/allQuestions')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0]).to.eql(qod1);
  expect(res.body.questions[1].text).to.equal('qod 2');

  // ban question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'kpop',
      title: 'title',
      text: 'text',
    });
  expect(res.status).to.equal(200);
  const q2Id = res.body._id;

  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  res = await request(app)
    .put('/v1/admin/banQuestion')
    .set('authorization', 1)
    .send({
      questionId: q2Id,
    });
  expect(res.status).to.equal(403);
  res = await request(app)
    .put('/v1/admin/banQuestion')
    .set('authorization', 0)
    .send({
      questionId: q2Id,
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/admin/banQuestion')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'kpop' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'kpop' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q2Id);

  // duplicate question - auto ban
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'latin',
      title: 'title',
      text: 'text',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'latin' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestName: 'latin' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);
});

it('interests', async () => {
  const numUsers = 2 + REPORTS_UNTIL_DELETION;
  const profiles = {};
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);

    profiles[uid.toString()] = {
      _id: uid.toString(),
      firstName: `name${uid.toString()}`,
      pictures: [],
      personality: null,
      gender: null,
      age: null,
      description: '',
      education: '',
      prompts: [],
      interests: [],
      interestNames: [],
      crown: false,
      hideQuestions: false,
      hideComments: false,
      handle: res.body.user.handle,
      teleport: false,
      preferences: { purpose: [] },
      horoscope: null,
      location: null,
      karma: 0,
      numFollowers: 0,
      verified: false,
      verificationStatus: 'unverified',
    };

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', uid)
      .send({ fcmToken: `token${uid.toString()}` });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', uid)
      .send({ firstName: `name${uid.toString()}` });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.interests).to.eql([]);
  expect(res.body.interests.length).to.equal(3);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;
  chessId = res.body.interests[2]._id;
  expect(res.body.interests).to.eql([
    {
      _id: kpopId,
      interest: '#kpop',
      allowImages: true,
      category: 'Music',
      name: 'kpop',
      numFollowers: 0,
      numQuestions: 0,
      numQuestionsPerLanguage: {},
      similar: [],
    },
    {
      _id: latinId,
      interest: '#latin',
      allowImages: true,
      category: 'Music',
      name: 'latin',
      numFollowers: 0,
      numQuestions: 0,
      numQuestionsPerLanguage: {},
      similar: [],
    },
    {
      _id: chessId,
      interest: '#chess',
      allowImages: true,
      category: 'Games',
      name: 'chess',
      numFollowers: 0,
      numQuestions: 0,
      numQuestionsPerLanguage: {},
      similar: [],
    },
  ]);
  const { interests } = res.body;
  interestIds = [kpopId, latinId, chessId];

  // invalid interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestIds: ['#chess'],
    });
  expect(res.status).to.equal(404);

  // valid interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestIds: [kpopId, latinId],
    });
  expect(res.status).to.equal(200);
  profiles['0'].interests = [interests[0], interests[1]];
  profiles['0'].interestNames = ['kpop', 'latin'];

  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 1)
    .send({
      interestIds: [chessId, latinId],
    });
  expect(res.status).to.equal(200);
  profiles['1'].interests = [interests[2], interests[1]];
  profiles['1'].interestNames = ['chess', 'latin'];

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.interests).to.eql(profiles['0'].interests);

  // no questions yet
  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  for (const interestId of interestIds) {
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([]);
  }

  // post question 1
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;
  const q1 = {
    _id: q1Id,
    webId: res.body.webId,
    url: res.body.url,
    createdAt: res.body.createdAt,
    createdBy: profiles['0'],
    interest: interests[0],
    interestName: 'kpop',
    title: 'title1',
    text: 'text1',
    numComments: 0,
    numLikes: 0,
    isDeleted: false,
    isEdited: false,
    hasUserLiked: false,
    hasUserSaved: false,
    language: 'en',
    linkedKeywords: [],
    linkedExploreKeywords: [],
    linkedPillarKeywords: [],
    linkedCategories: [],
    linkedSubcategories: [],
    linkedProfiles: [],
    hashtags: ['kpop'],
    images:[]
  };
  expect(res.body).to.eql(q1);
  expect(q1.url).to.equal(`https://boo.world/u/kpop/${q1.webId}/title1-text1`);

  // user can see question in feed
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: kpopId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: chessId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'explore' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'following' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  // user without kpop interest
  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'explore' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'following' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  // shadow banned user can still see own post, others cannot
  u0 = await User.findById('0');
  u0.shadowBanned = true;
  await u0.save();

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: kpopId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: kpopId })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  u0.shadowBanned = false;
  await u0.save();

  // post non-existant interest group
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(400);
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: '404',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(404);

  // edit own post
  res = await request(app)
    .patch('/v1/question/edit')
    .set('authorization', 0)
    .send({ questionId: q1._id, title: 'title2', text: 'text1' });
  expect(res.status).to.equal(200);

  q1.title = 'title2';
  q1.isEdited = true;
  q1.url = q1.url.replace('title1', 'title2');

  // edit other's post fails
  res = await request(app)
    .patch('/v1/question/edit')
    .set('authorization', 1)
    .send({ questionId: q1._id, title: 'title4', text: 'text4' });
  expect(res.status).to.equal(404);

  // load question of day
  newQuestion = await createQuestion({
    createdAt: new Date(),
    text: 'qod 1',
    interestName: 'questions',
  });
  await newQuestion.save();
  await Question.updateSearchFields(newQuestion._id);

  // question of day should appear in feed
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  const qod1Id = res.body.questions[0]._id;
  const qod1 = {
    _id: qod1Id,
    webId: res.body.questions[0].webId,
    url: res.body.questions[0].url,
    createdAt: res.body.questions[0].createdAt,
    createdBy: null,
    interest: null,
    text: 'qod 1',
    numComments: 0,
    numLikes: 0,
    isDeleted: false,
    isEdited: false,
    hasUserLiked: false,
    hasUserSaved: false,
    language: 'en',
    interestName: 'questions',
    linkedKeywords: [],
    linkedExploreKeywords: [],
    linkedPillarKeywords: [],
    linkedCategories: [],
    linkedSubcategories: [],
    linkedProfiles: [],
    hashtags: ['questions'],
    images:[]
  };
  expect(res.body.questions).to.eql([qod1]);
  expect(qod1.url).to.equal(`https://boo.world/u/questions/${qod1.webId}/qod-1`);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: kpopId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: chessId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([qod1, q1]);

  // edit qod fails
  res = await request(app)
    .patch('/v1/question/edit')
    .set('authorization', 1)
    .send({ questionId: qod1Id, title: 'title4', text: 'text4' });
  expect(res.status).to.equal(404);

  // test pagination
  for (let i = 0; i < pageSize * 2; i++) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestId: chessId,
        title: i.toString(),
        text: i.toString(),
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: kpopId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  // get first page
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: chessId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize * 2 - 1 - i).toString());
  }

  // get second page
  before = res.body.questions[pageSize - 1].createdAt;
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: chessId, before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
  }

  // try get third page - no more
  before = res.body.questions[pageSize - 1].createdAt;
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: chessId, before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  // create an older and future qod
  newQuestion = await createQuestion({
    createdAt: new Date(2020, 5, 20, 4, 5, 0, 0),
    text: 'qod 2',
    interestName: 'questions',
  });
  await newQuestion.save();

  newQuestion = await createQuestion({
    createdAt: new Date(9999, 5, 20, 4, 5, 0, 0),
    text: 'qod 3',
    interestName: 'questions',
  });
  await newQuestion.save();

  // feed - get first page
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize + 1);
  expect(res.body.questions[0]).to.eql(qod1);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[1 + i].text).to.equal((pageSize * 2 - 1 - i).toString());
    expect(res.body.questions[1 + i].interest._id).to.equal(chessId);
  }

  // get second page
  before = res.body.questions[pageSize].createdAt;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
    expect(res.body.questions[1].interest._id).to.equal(chessId);
  }

  // third page
  before = res.body.questions[pageSize - 1]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ beforeId: before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0].interest._id).to.equal(kpopId);

  // afterId - 2nd page
  afterId = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0)
    .query({ afterId });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
    expect(res.body.questions[1].interest._id).to.equal(chessId);
  }

  // afterId - 1st page (does not include qod)
  afterId = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0)
    .query({ afterId });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(pageSize);
  for (let i = 0; i < pageSize; i++) {
    expect(res.body.questions[i].text).to.equal((pageSize * 2 - 1 - i).toString());
    expect(res.body.questions[i].interest._id).to.equal(chessId);
  }

  // afterId - no more
  afterId = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0)
    .query({ afterId });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  // afterId for qod - no more
  res = await request(app)
    .get('/v1/question/allQuestions')
    .set('authorization', 0)
    .query({ afterId: qod1Id, sort: 'recent' });
  expect(res.status).to.equal(200);
  console.log(res.body.questions);
  expect(res.body.questions.length).to.equal(0);

  // following should only return kpop question
  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'following' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([q1]);

  // view likes
  reset();
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', '1')
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);
  q1.hasUserLiked = true;
  q1.numLikes += 1;

  resetTime = Date.now();
  await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
  expect(notifs.recent.token).to.equal('token0');
  expect(notifs.recent.notification.title).to.equal('name1 loved your post');
  expect(notifs.recent.notification.body).to.equal('You: title2');
  expect(JSON.parse(notifs.recent.data.question)).to.eql({
    _id: q1Id,
    interest: interests[0],
    interestName: 'kpop',
  });
  expect(notifs.numSent).to.equal(1);
  reset();

  res = await request(app)
    .get('/v1/question/likes')
    .query({ questionId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.totalPages).to.equal(1);
  expect(res.body.usersThatLiked).to.eql([profiles['1']]);

  res = await request(app)
    .get('/v1/question/likes')
    .query({ questionId: q1Id })
    .set('authorization', 1);
  expect(res.body.totalPages).to.equal(0);
  expect(res.body.usersThatLiked).to.eql([]);

  // comment on a question
  reset();
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;
  q1.numComments += 1;

  // get updated karma
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  profiles['0'].karma = res.body.user.karma;

  resetTime = Date.now();
  await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
  expect(notifs.recent.token).to.equal('token0');
  expect(notifs.recent.notification.title).to.equal('title2');
  expect(notifs.recent.notification.body).to.equal('name1: @name0 comment1');
  expect(JSON.parse(notifs.recent.data.comment)).to.eql({
    _id: c1Id,
    question: q1Id,
    parent: q1Id,
    postRepliedTo: q1Id,
    interest: interests[0],
    interestName: 'kpop',
  });
  expect(notifs.numSent).to.equal(1);
  reset();

  // not able to ban qod
  for (let i = 0; i < REPORTS_UNTIL_DELETION; i++) {
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 2 + i)
      .send({ questionId: qod1Id });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/question/allQuestions')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0]).to.eql(qod1);
  expect(res.body.questions[1].text).to.equal('qod 2');

  // ban question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestId: kpopId,
      title: 'title',
      text: 'text',
    });
  expect(res.status).to.equal(200);
  const q2Id = res.body._id;

  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  res = await request(app)
    .put('/v1/admin/banQuestion')
    .set('authorization', 1)
    .send({
      questionId: q2Id,
    });
  expect(res.status).to.equal(403);
  res = await request(app)
    .put('/v1/admin/banQuestion')
    .set('authorization', 0)
    .send({
      questionId: q2Id,
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/admin/banQuestion')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: kpopId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: kpopId })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q2Id);

  // duplicate question - auto ban
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestId: latinId,
      title: 'title',
      text: 'text',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: latinId })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ interestId: latinId })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);
});

it('sort by popular - score decay', async () => {
  try {
    clock = sinon.useFakeTimers();

    const numUsers = 7;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;
    latinId = res.body.interests[1]._id;

    // post questions
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    // like question
    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .patch('/v1/question/like')
        .set('authorization', i)
        .send({ questionId: q1Id });
      expect(res.status).to.equal(200);
    }

    console.log(await Question.find());

    // the liked question should appear first
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[1]._id).to.equal(q2Id);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: kpopId, sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[1]._id).to.equal(q2Id);

    // 1 half-life passes
    clock.tick(25 * 60 * 60 * 1000);

    res = await request(app)
      .post('/v1/worker/updateQuestionScores')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // new question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title3',
      });
    expect(res.status).to.equal(200);
    const q3Id = res.body._id;

    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .patch('/v1/question/like')
        .set('authorization', i)
        .send({ questionId: q3Id });
      expect(res.status).to.equal(200);
    }

    console.log(await Question.find());

    // check order
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q3Id);
    expect(res.body.questions[1]._id).to.equal(q1Id);
    expect(res.body.questions[2]._id).to.equal(q2Id);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: kpopId, sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q3Id);
    expect(res.body.questions[1]._id).to.equal(q1Id);
    expect(res.body.questions[2]._id).to.equal(q2Id);

    // add comments from a single user to q2
    for (let i = 0; i < 7; i++) {
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', 1)
        .send({ questionId: q2Id, parentId: q2Id, text: `comment${i}` });
      expect(res.status).to.equal(200);
    }

    console.log(await Question.find());

    // q2 score does not increase
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q3Id);
    expect(res.body.questions[1]._id).to.equal(q1Id);
    expect(res.body.questions[2]._id).to.equal(q2Id);

    // add comments from many users to q2
    for (let i = 0; i < 7; i++) {
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', i)
        .send({ questionId: q2Id, parentId: q2Id, text: `comment${i}` });
      expect(res.status).to.equal(200);
    }

    console.log(await Question.find());

    // q2 score increases
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q2Id);
    expect(res.body.questions[1]._id).to.equal(q3Id);
    expect(res.body.questions[2]._id).to.equal(q1Id);

    // interest with 1 banned post
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestId: latinId,
        title: 'add me on instagram',
        text: 'normal text',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: latinId, sort: 'recent' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: latinId, sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // many half-life passes
    for (let i = 0; i < 50; i++) {
      clock.tick(1 * 60 * 60 * 1000);

      res = await request(app)
        .post('/v1/worker/updateQuestionScores')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }

    // scores should stop decaying before they reach 0
    questions = await Question.find();
    expect(questions[0].score).to.be.greaterThan(0);

    // sort order should now match pre-decay scores
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q2Id);
    expect(res.body.questions[1]._id).to.equal(q1Id);
    expect(res.body.questions[2]._id).to.equal(q3Id);
  } finally {
    clock.restore();
  }
});

it('sort by popular with images', async () => {
  const numUsers = 4;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // q1 - text post
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // q2 - image post
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(200);
  const q2Id = res.body._id;
  res = await request(app)
    .post('/v1/question/image')
    .set('authorization', 0)
    .query({ questionId: q2Id })
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  // like posts
  for (let i = 0; i < numUsers / 2 + 1; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', i)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);
  }
  for (let i = 0; i < numUsers; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', i)
      .send({ questionId: q2Id });
    expect(res.status).to.equal(200);
  }

  console.log(await Question.find());

  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions[0]._id).to.equal(q2Id);
  expect(res.body.questions[1]._id).to.equal(q1Id);
});

it('region boost', async () => {
  sinon.stub(constants, 'getPageSize').returns(1);

  const numUsers = 4;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  // user 0 - west
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q0Id = res.body._id;

  // user 1 - southeastAsia
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 28.61,
      longitude: 77.21,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'kpop',
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // like posts
  for (let i = 0; i < numUsers / 2 + 1; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', i)
      .send({ questionId: q0Id });
    expect(res.status).to.equal(200);
  }
  for (let i = 0; i < numUsers; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', i)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);
  }

  console.log(await Question.find());

  // user 0 - q0 should appear first
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore', beforeId: before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  // user 1 - q1 should appear first
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore', beforeId: before })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  // user 2 - q1 should appear first
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', beforeId: before })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  // web germany - q0 should appear first
  res = await request(app)
    .get('/web/question/feed')
    .query({ sort: 'popular', filter: 'explore' })
    .set('X-Forwarded-For', '*************')
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/web/question/feed')
    .query({ sort: 'popular', filter: 'explore', beforeId: before })
    .set('X-Forwarded-For', '*************')
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  // web philippines - q1 should appear first
  res = await request(app)
    .get('/web/question/feed')
    .query({ sort: 'popular', filter: 'explore' })
    .set('X-Forwarded-For', '********')
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/web/question/feed')
    .query({ sort: 'popular', filter: 'explore', beforeId: before })
    .set('X-Forwarded-For', '********')
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  // user 0 switches location to southeastAsia
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 28.61,
      longitude: 77.21,
    });
  expect(res.status).to.equal(200);

  // web germany - q1 should appear first
  res = await request(app)
    .get('/web/question/feed')
    .query({ sort: 'popular', filter: 'explore' })
    .set('X-Forwarded-For', '*************')
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/web/question/feed')
    .query({ sort: 'popular', filter: 'explore', beforeId: before })
    .set('X-Forwarded-For', '*************')
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  // ban q0
  q = await Question.findById(q0Id);
  q.banned = true;
  await q.save();

  // user 2 - west
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);
});

it('throttle score updates', async () => {
  clock = sinon.useFakeTimers(Date.now());

  constants.throttleScoreUpdates.restore();
  sinon.stub(constants, 'throttleScoreUpdates').returns(true);

  const numUsers = 3;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 1)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  q1 = await Question.findById(q1Id);
  expect(Math.floor(q1.score)).to.equal(0);

  // score should update after 1 hour
  const msPerHour = 60 * 60 * 1000;
  clock.tick(1.1 * msPerHour);

  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 2)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  q1 = await Question.findById(q1Id);
  expect(Math.floor(q1.score)).to.equal(2);
});

it('country boost', async () => {
  sinon.stub(constants, 'getPageSize').returns(1);

  const numUsers = 4;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  // user 0 - US
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q0Id = res.body._id;

  // user 1 - UK
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 51.5,
      longitude: 0,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'kpop',
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // user 2 - US
  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 2)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  // like posts
  for (let i = 0; i < numUsers / 2 + 1; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', i)
      .send({ questionId: q0Id });
    expect(res.status).to.equal(200);
  }
  for (let i = 0; i < numUsers; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', i)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);
  }

  console.log(await Question.find());

  // user 0 - q0 should appear first
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore', beforeId: before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  // user 1 - q1 should appear first
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore', beforeId: before })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  // user 2 - q0 should appear first
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  before = res.body.questions[0]._id;
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', beforeId: before })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);
});

describe('image in question', async () => {
  let q1Id; let img1Id; let kpopId; let
    latinId;

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.interests[0].allowImages).to.equal(true);
    expect(res.body.interests[1].allowImages).to.equal(true);
    kpopId = res.body.interests[0]._id;
    latinId = res.body.interests[1]._id;

    // post question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;
    expect(res.body.image).to.equal();
    expect(res.body.isEdited).to.equal(false);

    // cannot add image to qod
    const question = await Question.findById(q1Id);
    const oldInterest = question.interestName;
    // change to qod
    question.interestName = 'questions';
    await question.save();
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(403);
    // restore question
    question.interestName = oldInterest;
    await question.save();

    // add image to question
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    assert(res.body.image.includes(`questions/${q1Id}/`));
    img1Id = res.body.image;

    // check post
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].image).to.equal(img1Id);
    expect(res.body.questions[0].isEdited).to.equal(false);
  });

  it('edit image', async () => {
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    assert(res.body.image.includes(`questions/${q1Id}/`));
    const img2Id = res.body.image;
    expect(img2Id).to.not.equal(img1Id);

    // check post
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].image).to.equal(img2Id);
    expect(res.body.questions[0].isEdited).to.equal(true);
  });

  it('replace image with gif', async () => {
    // invalid gif
    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        title: 'title1',
        text: 'text1',
        gif: 'yahoo.com',
      });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        title: 'title1',
        text: 'text1',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].gif).to.equal(validResponseGif);
    expect(res.body.questions[0].image).to.equal();
    expect(res.body.questions[0].isEdited).to.equal(true);

    // now replace gif with image
    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    assert(res.body.image.includes(`questions/${q1Id}/`));
    const img2Id = res.body.image;

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].gif).to.equal();
    expect(res.body.questions[0].image).to.equal(img2Id);
    expect(res.body.questions[0].isEdited).to.equal(true);
  });

  it('other user cannot edit or delete image', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question/image')
      .set('authorization', 1)
      .query({ questionId: q1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(404);

    res = await request(app)
      .delete('/v1/question/image')
      .set('authorization', 1)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(404);
  });

  it('delete image', async () => {
    // cannot delete qod image
    const question = await Question.findById(q1Id);
    const oldInterest = question.interestName;
    // change to qod
    question.interestName = 'questions';
    await question.save();
    res = await request(app)
      .delete('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(403);
    // restore question
    question.interestName = oldInterest;
    await question.save();
    res = await request(app)
      .delete('/v1/question/image')
      .set('authorization', 0)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].image).to.equal();
    expect(res.body.questions[0].isEdited).to.equal(true);
  });

  it('post question with gif', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title1',
        text: 'text1',
        gif: validGif,
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;
    expect(res.body.text).to.equal('text1');
    expect(res.body.gif).to.equal(validResponseGif);
    expect(res.body.isEdited).to.equal(false);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'recent' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title1');
    expect(res.body.questions[0].text).to.equal('text1');
    expect(res.body.questions[0].gif).to.equal(validResponseGif);
    expect(res.body.questions[0].image).to.equal();
    expect(res.body.questions[0].isEdited).to.equal(false);

    // edit and remove the text
    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        title: 'title1',
        text: '',
        gif: validTenorGif,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'recent' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title1');
    expect(res.body.questions[0].text).to.equal('');
    expect(res.body.questions[0].gif).to.equal(validTenorResponseGif);
    expect(res.body.questions[0].image).to.equal();
    expect(res.body.questions[0].isEdited).to.equal(true);

    // edit and remove the gif
    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        title: 'title1',
        text: '',
        gif: '',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'recent' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title1');
    expect(res.body.questions[0].text).to.equal('');
    expect(res.body.questions[0].gif).to.equal('');
    expect(res.body.questions[0].image).to.equal();
    expect(res.body.questions[0].isEdited).to.equal(true);
  });
});

describe('audio in question', async () => {
  let q1Id; let audio1Id; let kpopId; let
    latinId;

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;
    latinId = res.body.interests[1]._id;

    // post question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;
    expect(res.body.audio).to.equal();
    expect(res.body.isEdited).to.equal(false);

    // cannot add audio to qod
    const question = await Question.findById(q1Id);
    const oldInterest = question.interestName;
    // change to qod
    question.interestName = 'questions';
    await question.save();
    res = await request(app)
      .post('/v1/question/audio')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('audio', validAudioPath)
      .field({ waveform: JSON.stringify([1.3, 1.5]), duration: 1.5 });
    expect(res.status).to.equal(403);
    // restore question
    question.interestName = oldInterest;
    await question.save();

    // add audio to question
    res = await request(app)
      .post('/v1/question/audio')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('audio', validAudioPath)
      .field({ waveform: JSON.stringify([1.3, 1.5]), duration: 1.5 });
    expect(res.status).to.equal(200);
    assert(res.body.audio.includes(`questions/${q1Id}/`));
    audio1Id = res.body.audio;

    // check post
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].audio).to.equal(audio1Id);
    expect(res.body.questions[0].audioWaveform).to.eql([1.3, 1.5]);
    expect(res.body.questions[0].audioDuration).to.equal(1.5);
    expect(res.body.questions[0].isEdited).to.equal(false);
  });

  it('edit audio', async () => {
    res = await request(app)
      .post('/v1/question/audio')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('audio', validAudioPath);
    expect(res.status).to.equal(200);
    assert(res.body.audio.includes(`questions/${q1Id}/`));
    const audio2Id = res.body.audio;
    expect(audio2Id).to.not.equal(audio1Id);

    // check post
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].audio).to.equal(audio2Id);
    expect(res.body.questions[0].isEdited).to.equal(true);
  });

  it('add gif', async () => {
    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        title: 'title1',
        text: 'text1',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title1');
    expect(res.body.questions[0].text).to.equal('text1');
    expect(res.body.questions[0].gif).to.equal(validResponseGif);
    expect(res.body.questions[0].audio).to.equal(audio1Id);
    expect(res.body.questions[0].isEdited).to.equal(true);

    // now edit audio
    res = await request(app)
      .post('/v1/question/audio')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('audio', validAudioPath);
    expect(res.status).to.equal(200);
    assert(res.body.audio.includes(`questions/${q1Id}/`));
    const audio2Id = res.body.audio;
    expect(audio2Id).to.not.equal(audio1Id);

    // check post
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].title).to.equal('title1');
    expect(res.body.questions[0].text).to.equal('text1');
    expect(res.body.questions[0].gif).to.equal(validResponseGif);
    expect(res.body.questions[0].audio).to.equal(audio2Id);
    expect(res.body.questions[0].isEdited).to.equal(true);
  });

  it('other user cannot edit or delete audio', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question/audio')
      .set('authorization', 1)
      .query({ questionId: q1Id })
      .attach('audio', validAudioPath);
    expect(res.status).to.equal(404);

    res = await request(app)
      .delete('/v1/question/audio')
      .set('authorization', 1)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(404);
  });

  it('delete audio', async () => {
    // cannot delete audio  qod
    const question = await Question.findById(q1Id);
    const oldInterest = question.interestName;
    // change to qod
    question.interestName = 'questions';
    await question.save();
    res = await request(app)
      .delete('/v1/question/audio')
      .set('authorization', 0)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(403);
    // restore question
    question.interestName = oldInterest;
    await question.save();

    res = await request(app)
      .delete('/v1/question/audio')
      .set('authorization', 0)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.questions[0]._id).to.equal(q1Id);
    expect(res.body.questions[0].audio).to.equal();
    expect(res.body.questions[0].isEdited).to.equal(true);
  });

  it('duplicate detection', async () => {
    let banned = await Question.find({ banned: true });
    expect(banned.length).to.equal(0);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    res = await request(app)
      .post('/v1/question/audio')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('audio', validAudioPath);
    expect(res.status).to.equal(200);

    banned = await Question.find({ banned: true });
    expect(banned.length).to.equal(1);
  });
});

it('notifications and delete', async () => {
  const numUsers = 3;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // user 0 posts question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;
  const q1Url = res.body.url;

  // user 1 likes
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 1)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  // user 0 gets notifications
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(1);
  expect(res.body.notifications[0].postType).to.equal('question');
  expect(res.body.notifications[0].notificationType).to.equal('like');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile._id).to.equal('1');
  expect(res.body.notifications[0].numProfiles).to.equal(1);
  expect(res.body.notifications[0].url).to.equal(q1Url);
  data = JSON.parse(res.body.notifications[0].data.question);
  expect(data._id).to.equal(q1Id);

  // user 1 views question - user 0's notification not cleared
  res = await request(app)
    .get('/v1/question')
    .query({ questionId: q1Id })
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications[0].seen).to.equal(false);

  // user 0 views question - notification cleared
  res = await request(app)
    .get('/v1/question')
    .query({ questionId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications[0].seen).to.equal(true);

  // user 1 comments
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;

  // user 0 gets notifications
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(2);
  expect(res.body.notifications[0].postType).to.equal('question');
  expect(res.body.notifications[0].notificationType).to.equal('reply');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile._id).to.equal('1');
  expect(res.body.notifications[0].numProfiles).to.equal(1);
  expect(res.body.notifications[1].postType).to.equal('question');
  expect(res.body.notifications[1].notificationType).to.equal('like');
  expect(res.body.notifications[1].seen).to.equal(true);
  expect(res.body.notifications[1].profile._id).to.equal('1');
  expect(res.body.notifications[1].numProfiles).to.equal(1);

  // user 2 likes
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 2)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  // user 0 gets notifications
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(2);
  expect(res.body.notifications[0].postType).to.equal('question');
  expect(res.body.notifications[0].notificationType).to.equal('like');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile._id).to.equal('2');
  expect(res.body.notifications[0].numProfiles).to.equal(2);
  expect(res.body.notifications[1].postType).to.equal('question');
  expect(res.body.notifications[1].notificationType).to.equal('reply');
  expect(res.body.notifications[1].seen).to.equal(false);
  expect(res.body.notifications[1].profile._id).to.equal('1');
  expect(res.body.notifications[1].numProfiles).to.equal(1);
  data = JSON.parse(res.body.notifications[1].data.comment);
  expect(data._id).to.equal(c1Id);
  expect(data.question).to.equal(q1Id);
  expect(data.parent).to.equal(q1Id);
  expect(data.postRepliedTo).to.equal(q1Id);

  // user 1 views comment - user 0's notification not seen
  res = await request(app)
    .get('/v1/comment/context')
    .query({
      commentId: c1Id, parentId: q1Id, questionId: q1Id, postRepliedTo: q1Id,
    })
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[1].seen).to.equal(false);

  // user 0 views comment - notification seen
  res = await request(app)
    .get('/v1/comment/context')
    .query({
      commentId: c1Id, parentId: q1Id, questionId: q1Id, postRepliedTo: q1Id,
    })
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[1].seen).to.equal(true);

  // user 0 replies to c1
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      text: 'comment2',
      parentId: c1Id,
    });
  expect(res.status).to.equal(200);
  const c2Id = res.body._id;

  // user 0 replies to c2
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      text: 'comment3',
      parentId: c2Id,
    });
  expect(res.status).to.equal(200);
  const c3Id = res.body._id;

  // user 1 replies to c2
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment4',
      parentId: c2Id,
    });
  expect(res.status).to.equal(200);
  const c4Id = res.body._id;

  // user 2 likes c3
  res = await request(app)
    .patch('/v1/comment/like')
    .set('authorization', 2)
    .send({ commentId: c3Id });
  expect(res.status).to.equal(200);

  // user 0 gets notifications
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(4);
  expect(res.body.notifications[0].postType).to.equal('comment');
  expect(res.body.notifications[0].notificationType).to.equal('like');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile._id).to.equal('2');
  expect(res.body.notifications[0].numProfiles).to.equal(1);
  expect(res.body.notifications[0].url).to.equal(q1Url);
  data = JSON.parse(res.body.notifications[0].data.comment);
  expect(data._id).to.equal(c3Id);
  expect(data.question).to.equal(q1Id);
  expect(data.parent).to.equal(c1Id);
  expect(data.postRepliedTo).to.equal(c2Id);
  expect(res.body.notifications[1].postType).to.equal('comment');
  expect(res.body.notifications[1].notificationType).to.equal('reply');
  expect(res.body.notifications[1].seen).to.equal(false);
  expect(res.body.notifications[1].profile._id).to.equal('1');
  expect(res.body.notifications[1].numProfiles).to.equal(1);
  data = JSON.parse(res.body.notifications[1].data.comment);
  expect(data._id).to.equal(c4Id);
  expect(data.question).to.equal(q1Id);
  expect(data.parent).to.equal(c1Id);
  expect(data.postRepliedTo).to.equal(c2Id);

  // user 1 views c3 - user 0's notification not seen
  res = await request(app)
    .get('/v1/comment/context')
    .query({ commentId: c3Id, parentId: c1Id, questionId: q1Id })
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[1].seen).to.equal(false);

  // user 0 views c3 - notification seen
  res = await request(app)
    .get('/v1/comment/context')
    .query({ commentId: c3Id, parentId: c1Id, questionId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications[0].seen).to.equal(true);
  expect(res.body.notifications[1].seen).to.equal(false);

  // user 1 views c4 - user 0's notification not seen
  res = await request(app)
    .get('/v1/comment/context')
    .query({
      commentId: c4Id, parentId: c1Id, questionId: q1Id, postRepliedTo: c2Id,
    })
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications[1].seen).to.equal(false);

  // user 0 views c4 - notification seen
  res = await request(app)
    .get('/v1/comment/context')
    .query({
      commentId: c4Id, parentId: c1Id, questionId: q1Id, postRepliedTo: c2Id,
    })
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications[1].seen).to.equal(true);

  // user 1 cannot delete c2
  res = await request(app)
    .delete('/v1/comment')
    .set('authorization', 1)
    .query({ commentId: c2Id });
  expect(res.status).to.equal(404);

  // user 0 can delete c2
  res = await request(app)
    .delete('/v1/comment')
    .set('authorization', 0)
    .query({ commentId: c2Id });
  expect(res.status).to.equal(200);

  // c3 and c4 replies also deleted
  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c1Id);
  expect(res.body.comments[0].comments.length).to.equal(0);

  // c2 notification deleted
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(2);
  expect(res.body.notifications[0].postType).to.equal('question');
  expect(res.body.notifications[0].notificationType).to.equal('like');
  expect(res.body.notifications[1].postType).to.equal('question');
  expect(res.body.notifications[1].notificationType).to.equal('reply');
  data = JSON.parse(res.body.notifications[1].data.comment);
  expect(data._id).to.equal(c1Id);
  expect(data.question).to.equal(q1Id);
  expect(data.parent).to.equal(q1Id);
  expect(data.postRepliedTo).to.equal(q1Id);

  // c1 notification still references c2 (not ideal)
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(1);
  expect(res.body.notifications[0].postType).to.equal('comment');
  expect(res.body.notifications[0].notificationType).to.equal('reply');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile._id).to.equal('0');
  expect(res.body.notifications[0].numProfiles).to.equal(1);
  data = JSON.parse(res.body.notifications[0].data.comment);
  expect(data._id).to.equal(c2Id);
  expect(data.question).to.equal(q1Id);
  expect(data.parent).to.equal(c1Id);
  expect(data.postRepliedTo).to.equal(c1Id);

  // getting context for c2 should still work (but c2 won't be in the response)
  res = await request(app)
    .get('/v1/comment/context')
    .query({
      commentId: c2Id, parentId: c1Id, questionId: q1Id, postRepliedTo: c1Id,
    })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c1Id);
  expect(res.body.comments[0].comments.length).to.equal(0);

  // user 0 replies to c1 again
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      text: 'comment5',
      parentId: c1Id,
    });
  expect(res.status).to.equal(200);
  const c5Id = res.body._id;

  // user 1 deletes c1
  res = await request(app)
    .delete('/v1/comment')
    .set('authorization', 1)
    .query({ commentId: c1Id });
  expect(res.status).to.equal(200);

  // c5 reply also deleted
  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  // c1 notification deleted
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(0);

  // getting context for c1 should still work (but c1 won't be in the response)
  res = await request(app)
    .get('/v1/comment/context')
    .query({
      commentId: c1Id, parentId: q1Id, questionId: q1Id, postRepliedTo: q1Id,
    })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.comments).to.eql([]);

  // user 1 replies to q1 again
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment6',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c6Id = res.body._id;

  // user 1 cannot delete q1
  res = await request(app)
    .delete('/v1/question')
    .set('authorization', 1)
    .query({ questionId: q1Id });
  expect(res.status).to.equal(404);

  // user 0 can delete q1
  res = await request(app)
    .delete('/v1/question')
    .set('authorization', 0)
    .query({ questionId: q1Id });
  expect(res.status).to.equal(200);

  // feed empty now
  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions).to.eql([]);

  // q1 notifications deleted
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.notifications.length).to.equal(0);

  // viewing question should return error
  res = await request(app)
    .get('/v1/question')
    .query({ questionId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(404);

  // getting context for c1 should still work (but c1 won't be in the response)
  res = await request(app)
    .get('/v1/comment/context')
    .query({
      commentId: c1Id, parentId: q1Id, questionId: q1Id, postRepliedTo: q1Id,
    })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.comments).to.eql([]);
});

it('notifications involving banned/deleted users', async () => {
  const numUsers = 3;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // user 0 posts question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // user likes
  res = await request(app)
    .get('/v1/user')
    .set('authorization', constants.IMMEDIATE_DELETION_ID);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', constants.IMMEDIATE_DELETION_ID)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  // user gets banned
  u = await User.findById(constants.IMMEDIATE_DELETION_ID);
  u.shadowBanned = true;
  await u.save();

  // user 0 gets notifications
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(1);
  expect(res.body.notifications[0].postType).to.equal('question');
  expect(res.body.notifications[0].notificationType).to.equal('like');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile).to.equal(null);
  expect(res.body.notifications[0].numProfiles).to.equal(1);
  data = JSON.parse(res.body.notifications[0].data.question);
  expect(data._id).to.equal(q1Id);

  // user deletes account
  res = await request(app)
    .post('/v1/user/accountDeletion')
    .set('authorization', constants.IMMEDIATE_DELETION_ID)
    .send({
      reason: [1, 4],
      feedback: 'feedback',
    });
  expect(res.status).to.equal(200);

  // user 0 gets notifications
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(1);
  expect(res.body.notifications[0].postType).to.equal('question');
  expect(res.body.notifications[0].notificationType).to.equal('like');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile).to.equal(null);
  expect(res.body.notifications[0].numProfiles).to.equal(1);
  data = JSON.parse(res.body.notifications[0].data.question);
  expect(data._id).to.equal(q1Id);
});

it('notifications involving blocked users', async () => {
  const numUsers = 2;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  // user 0 posts question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // user 1 likes
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 1)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  // user 0 blocks user 1
  res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  // user 0 gets notifications
  res = await request(app)
    .get('/v1/notification')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  console.log(res.body.notifications);
  expect(res.body.notifications.length).to.equal(1);
  expect(res.body.notifications[0].postType).to.equal('question');
  expect(res.body.notifications[0].notificationType).to.equal('like');
  expect(res.body.notifications[0].seen).to.equal(false);
  expect(res.body.notifications[0].profile).to.equal(null);
  expect(res.body.notifications[0].numProfiles).to.equal(1);
  data = JSON.parse(res.body.notifications[0].data.question);
  expect(data._id).to.equal(q1Id);
});

describe('comment notifications', async () => {
  let interestIds; let q1Id; let
    c1Id;

  beforeEach(async () => {
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }
    interestIds = res.body.interests.map((x) => x._id);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({
        fcmToken: '1',
      });
    expect(res.status).to.equal(200);

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // user 1 comments
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;

    // user 0 likes
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 0)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(1);

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(1);
    expect(res.body.notifications[0].postType).to.equal('comment');
    expect(res.body.notifications[0].notificationType).to.equal('like');
  });

  it('post is banned', async () => {
    // post gets banned
    q = await Question.findById(q1Id);
    q.banned = true;
    await q.save();

    // comment notification hidden
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(0);

    // no further push notifications
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 2)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(0);
  });

  it('comment author blocks post author', async () => {
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // comment notification hidden
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(0);

    // no further push notifications
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 2)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(0);
  });

  it('post author blocks comment author', async () => {
    // user 0 blocks user 1
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // comment notification hidden
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(0);

    // no further push notifications
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 2)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(0);
  });
});

describe('nested comment notifications', async () => {
  let interestIds; let q1Id; let c1Id; let
    c2Id;

  beforeEach(async () => {
    for (let uid = 0; uid < 3; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }
    interestIds = res.body.interests.map((x) => x._id);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({
        fcmToken: '1',
      });
    expect(res.status).to.equal(200);

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    // user 2 comments
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;

    // user 1 posts nested comment
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'comment2',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);
    c2Id = res.body._id;

    // user 0 likes
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 0)
      .send({ commentId: c2Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(1);

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(1);
    expect(res.body.notifications[0].postType).to.equal('comment');
    expect(res.body.notifications[0].notificationType).to.equal('like');
  });

  it('post is banned', async () => {
    // post gets banned
    q = await Question.findById(q1Id);
    q.banned = true;
    await q.save();

    // comment notification hidden
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(0);

    // no further push notifications
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 2)
      .send({ commentId: c2Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(0);
  });

  it('post author blocks comment author', async () => {
    // user 0 blocks user 1
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // comment notification hidden
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(0);

    // no further push notifications
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 2)
      .send({ commentId: c2Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(0);
  });

  it('comment is banned', async () => {
    q = await Comment.findById(c1Id);
    q.banned = true;
    await q.save();

    // comment notification hidden
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(0);

    // no further push notifications
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 2)
      .send({ commentId: c2Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(0);
  });

  it('comment author blocks reply author', async () => {
    // user 2 blocks user 1
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // comment notification hidden
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(0);

    // no further push notifications
    reset();
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 2)
      .send({ commentId: c2Id });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 100));
    expect(notifs.numSent).to.equal(0);
  });
});

it('delete qod user - forbidden', async () => {
  const numUsers = 1;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // user 0 posts question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // make q1 a qod
  await Question.updateOne({ _id: q1Id }, { $set: { interestName: 'questions' } });

  res = await request(app)
    .delete('/v1/question')
    .set('authorization', 0)
    .query({ questionId: q1Id });
  expect(res.status).to.equal(403);
});

it('delete question should delete all comments', async () => {
  const numUsers = 1;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // user 0 posts question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  for (let i = 0; i < 2; i++) {
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const cId = res.body._id;
    for (let j = 0; j < 2; j++) {
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', 0)
        .send({
          questionId: q1Id,
          text: 'comment',
          parentId: cId,
        });
      expect(res.status).to.equal(200);
    }
  }

  comments = await Comment.find();
  expect(comments.length).to.equal(6);

  res = await request(app)
    .delete('/v1/question')
    .set('authorization', 0)
    .query({ questionId: q1Id });
  expect(res.status).to.equal(200);

  comments = await Comment.find();
  expect(comments.length).to.equal(0);
});

it('delete account should delete questions and comments', async () => {
  const numUsers = 2;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // users 0 and 1 each post a question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title0',
      text: 'text0',
    });
  expect(res.status).to.equal(200);
  const q0Id = res.body._id;

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // users 0 and 1 each comment on the other's question
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      text: 'comment',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c0Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q0Id,
      text: 'comment',
      parentId: q0Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;

  // user 1 replies to user 0's comment
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment',
      parentId: c0Id,
    });
  expect(res.status).to.equal(200);
  const c2Id = res.body._id;

  // delete user 0's account, then recreate
  user = await User.findById('0');
  await userLib.deleteAccount(user);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.10.21' });
  expect(res.status).to.equal(200);

  // user 0's question and comment should be deleted
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  questions = await Question.find();
  expect(questions.length).to.equal(1);
  comments = await Comment.find();
  expect(comments.length).to.equal(0);
});

it('delete account should not delete qod and the comments on qod', async () => {
  const numUsers = 2;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // users 0 and 1 each post a question
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title0',
      text: 'text0',
    });
  expect(res.status).to.equal(200);
  const q0Id = res.body._id;

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // set q0 as qod

  await Question.updateOne({ _id: q0Id }, { interestName: 'questions' });
  // delete user 0's account, then recreate
  user = await User.findById('0');
  await userLib.deleteAccount(user);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.10.21' });
  expect(res.status).to.equal(200);

  // qod should not be deleted but createdBy becomes null
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0]._id).to.equal(q0Id);
  expect(res.body.questions[0].createdBy).to.equal(null);
  expect(res.body.questions[1]._id).to.equal(q1Id);
});

it('view user comments on question of day', async () => {
  const numUsers = 1;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  // load question of day
  newQuestion = await createQuestion({
    createdAt: new Date(),
    text: 'qod 1',
    interestName: 'questions',
  });
  await newQuestion.save();
  q1Id = newQuestion._id;

  // post comment
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;

  // view comment
  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c1Id);
});

it('user activity', async () => {
  const numUsers = 3;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  // user 0 and user 1 each post question and comment
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;
  const q1Url = res.body.url;

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestId: latinId,
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(200);
  const q2Id = res.body._id;
  const q2Url = res.body.url;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q2Id,
      text: 'comment1',
      parentId: q2Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment2',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c2Id = res.body._id;

  // breathing test
  for (let i = 0; i < numUsers; i++) {
    res = await request(app)
      .get('/v1/user/questions')
      .set('authorization', i)
      .query({ createdBy: 0 });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    res = await request(app)
      .get('/v1/user/questions')
      .set('authorization', i)
      .query({ createdBy: 1 });
    expect(res.status).to.equal(200);
    expect(res.body.questions[0]._id).to.equal(q2Id);

    res = await request(app)
      .get('/v1/user/comments')
      .set('authorization', i)
      .query({ createdBy: 0 });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c1Id);
    expect(res.body.comments[0].questionUrl).to.equal(q2Url);

    res = await request(app)
      .get('/v1/user/comments')
      .set('authorization', i)
      .query({ createdBy: 1 });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[0].questionUrl).to.equal(q1Url);
  }

  // user 0 hides posts
  res = await request(app)
    .put('/v1/user/hideQuestions')
    .set('authorization', 0)
    .send({
      hideQuestions: true,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.hideQuestions).to.equal(true);
  expect(res.body.user.hideComments).to.equal(false);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.chats[0].user.hideQuestions).to.equal(true);
  expect(res.body.chats[0].user.hideComments).to.equal(false);

  // user 0 can still see own activity
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c1Id);

  // user 1 cannot see user 0's posts
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 1)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 1)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c1Id);

  // user 0 hides comments, unhides posts
  res = await request(app)
    .put('/v1/user/hideQuestions')
    .set('authorization', 0)
    .send({
      hideQuestions: false,
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/hideComments')
    .set('authorization', 0)
    .send({
      hideComments: true,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.hideQuestions).to.equal(false);
  expect(res.body.user.hideComments).to.equal(true);

  res = await request(app)
    .get('/v1/chat/pending')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.chats[0].user.hideQuestions).to.equal(false);
  expect(res.body.chats[0].user.hideComments).to.equal(true);

  // user 0 can still see own activity
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c1Id);

  // user 1 cannot see user 0's comments
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 1)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 1)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  // user 0 unhides comments, unhides posts
  res = await request(app)
    .put('/v1/user/hideQuestions')
    .set('authorization', 0)
    .send({
      hideQuestions: false,
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/hideComments')
    .set('authorization', 0)
    .send({
      hideComments: false,
    });
  expect(res.status).to.equal(200);

  // q2 gets banned
  q2 = await Question.findById(q2Id);
  q2.banned = true;
  await q2.save();

  // q2 and c1 should not be visible by user 0
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 0)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 0)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c2Id);

  // q2 and c1 should be visible by user 1
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 1)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 1)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q2Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 1)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c1Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 1)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c2Id);

  // unban q2
  q2 = await Question.findById(q2Id);
  q2.banned = false;
  await q2.save();

  // user 0 gets banned
  u0 = await User.findById('0');
  u0.shadowBanned = true;
  await u0.save();

  // everything should be visible by user 0
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 0)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q2Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 0)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c1Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 0)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c2Id);

  // q1, c1, and c2 should not be visible by user 1
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 1)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 1)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q2Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 1)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 1)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  // unban user 0
  u0 = await User.findById('0');
  u0.shadowBanned = false;
  await u0.save();

  // user 2 blocks user 0
  res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 2)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  // q1, c1, and c2 should not be visible by user 2
  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 2)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/v1/user/questions')
    .set('authorization', 2)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q2Id);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 2)
    .query({ createdBy: 0 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  res = await request(app)
    .get('/v1/user/comments')
    .set('authorization', 2)
    .query({ createdBy: 1 });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);
});

describe('user activity when blocked', async () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 2; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;

    res = await request(app)
      .get('/v1/user/questions')
      .set('authorization', 1)
      .query({ createdBy: 0 });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/user/comments')
      .set('authorization', 1)
      .query({ createdBy: 0 });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
  });

  it('user 0 blocks user 1', async () => {
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/questions')
      .set('authorization', 1)
      .query({ createdBy: 0 });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/user/comments')
      .set('authorization', 1)
      .query({ createdBy: 0 });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);
  });

  it('user 1 blocks user 0', async () => {
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user/questions')
      .set('authorization', 1)
      .query({ createdBy: 0 });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/user/comments')
      .set('authorization', 1)
      .query({ createdBy: 0 });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);
  });
});

it('karma', async () => {
  const numUsers = 7;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.36' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/fcmToken')
    .set('authorization', 0)
    .send({ fcmToken: 'token0' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/notificationSettings')
    .set('authorization', 0)
    .send({
      pushNotificationSettings: { commentLikes: false },
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;

  // user 0 posts
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(0);
  let karma = 0;
  let { coins } = res.body.user;

  // user 0 likes own post - no change in karma
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 0)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  // other users like post - karma increases
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 1)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  karma = 1;
  coins += 1;

  resetTime = Date.now();
  await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
  expect(notifs.recent.token).to.equal('token0');
  expect(notifs.recent.notification.title).to.equal('Congrats!');
  expect(notifs.recent.notification.body).to.equal('You\'ve reached a new level!');
  expect(JSON.parse(notifs.recent.data.karma)).to.eql({
    karma,
    coins,
  });
  expect(notifs.numSent).to.equal(1);
  reset();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 2)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  karma = 2;

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 3)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  karma = Math.round(1 + Math.sqrt(2));

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  // user 0 comments on own post - no change in karma
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  // other users comment - karma increases
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 4)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);

  karma += 1;
  coins += 3;

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 5)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);

  karma += 1;

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 6)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);

  karma = Math.round(2 + 2 * Math.sqrt(2));

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  // test backfill karma
  user = await User.findById(0);
  user.karma = 0;
  await user.save();

  await backfillAllUserKarma();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(0);
});

it('tiers - backwards compatibility', async () => {
  const numUsers = 8;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.35' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  kpopId = res.body.interests[0]._id;
  latinId = res.body.interests[1]._id;
  let karma = 0;
  let { coins } = res.body.user;

  // user 0 posts
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // karma increases, but no coin reward
  for (let uid = 1; uid < 6; uid++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', uid)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);
  }

  karma = Math.round(1 + Math.sqrt(4));

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  // upgrade
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.10.36' });
  expect(res.status).to.equal(200);

  // tier increase
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 6)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  karma = Math.round(1 + Math.sqrt(5));
  coins += 1;

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);

  // tier increase
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 7)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);

  karma = Math.round(1 + Math.sqrt(6));
  coins += 3;

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.karma).to.equal(karma);
  expect(res.body.user.coins).to.equal(coins);
});

/*
describe('feed speed', function() {

  beforeEach(async function() {
    const numUsers = 2;
    const numQuestionsPerInterest = pageSize;

    // create an older and future qod
    newQuestion = await createQuestion({
      createdAt: new Date(2020, 5, 20, 4, 5, 0, 0),
      text: 'qod 2',
      interestName: 'questions',
    });
    await newQuestion.save();

    newQuestion = await createQuestion({
      createdAt: new Date(9999, 5, 20, 4, 5, 0, 0),
      text: 'qod 3',
      interestName: 'questions',
    });
    await newQuestion.save();

    for (let i = 0; i < numUsers; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }
    interestIds = res.body.interests.map(x => x._id);

    for (let j = 0; j < numQuestionsPerInterest; j++) {
      for (let interestId of interestIds) {
        for (let language of ['de', 'zh', 'en']) {
          for (let i = 0; i < numUsers; i++) {
            res = await request(app)
              .post('/v1/question')
              .set('authorization', i)
              .send({
                interestId: interestId,
                title: interestId + j.toString(),
                language: language,
              })
            expect(res.status).to.equal(200);
          }
        }
      }
    }

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestIds: [interestIds[0]],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/follow/sendFollowRequest')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.approved).to.equal(true);
  });

  it('explore recent', async function() {
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'explore', sort: 'recent' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);

    let afterId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'explore', sort: 'recent', afterId: afterId })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('explore popular', async function() {
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'explore', sort: 'popular' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('explore one language', async function() {
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'explore', sort: 'recent', language: 'de' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('explore two language', async function() {
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({languages: ['de', 'zh']})
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'explore', sort: 'recent' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('following recent', async function() {
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'following', sort: 'recent' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('following popular', async function() {
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'following', sort: 'popular' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('following one language', async function() {
    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'following', sort: 'recent', language: 'de' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('following two language', async function() {
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({languages: ['de', 'zh']})
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ filter: 'following', sort: 'recent' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('interest recent', async function() {
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[0], sort: 'recent' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);

    let afterId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[0], sort: 'recent', afterId: afterId })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('interest popular', async function() {
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[0], sort: 'popular' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('interest one language', async function() {
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[0], sort: 'recent', language: 'de' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('interest two language', async function() {
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({languages: ['de', 'zh']})
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[0], sort: 'recent' })
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('user recent', async function() {
    res = await request(app)
      .get('/v1/user/questions')
      .set('authorization', 0)
      .query({ createdBy: 1, sort: 'recent' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

  it('user popular', async function() {
    res = await request(app)
      .get('/v1/user/questions')
      .set('authorization', 0)
      .query({ createdBy: 1, sort: 'popular' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.be.greaterThan(0);
  });

});
*/

it('inactive - hide activity', async () => {
  const numUsers = 2;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.35' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/chat')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body[0].user._id).to.equal('0');
  expect(res.body[0].user.hideQuestions).to.equal(false);
  expect(res.body[0].user.hideComments).to.equal(false);

  // set user 0 inactive
  user = await User.findById('0');
  user.updatedAt = new Date(2000, 1, 1);
  await user.save();

  // activity hidden
  res = await request(app)
    .get('/v1/chat')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body[0].user._id).to.equal('0');
  expect(res.body[0].user.hideQuestions).to.equal(true);
  expect(res.body[0].user.hideComments).to.equal(true);

  // set user 0 active
  user = await User.findById('0');
  user.updatedAt = new Date();
  await user.save();

  // activity not hidden
  res = await request(app)
    .get('/v1/chat')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body[0].user._id).to.equal('0');
  expect(res.body[0].user.hideQuestions).to.equal(false);
  expect(res.body[0].user.hideComments).to.equal(false);
});

describe('save question', async () => {
  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.36' });
    expect(res.status).to.equal(200);
  });

  it('basic functionality', async () => {
    // create second user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.36' });
    expect(res.status).to.equal(200);

    // create question
    newQuestion = await createQuestion({
      createdAt: new Date(),
      text: 'qod',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const q1 = res.body.questions[0];

    // nothing saved yet
    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([]);

    // user 0 saves q1
    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: q1._id });
    expect(res.status).to.equal(200);
    q1.hasUserSaved = true;

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([q1]);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([q1]);

    // user 1 cannot see saved post
    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 1)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([]);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0].hasUserSaved).to.equal(false);

    // unsave
    res = await request(app)
      .patch('/v1/question/unsave')
      .set('authorization', 0)
      .send({ questionId: q1._id });
    expect(res.status).to.equal(200);
    q1.hasUserSaved = false;

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([]);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([q1]);
  });

  it('order', async () => {
    const numQuestions = 2;
    const q = [];
    for (let i = 0; i < numQuestions; i++) {
      newQuestion = await createQuestion({
        createdAt: new Date(1000 + i, 5, 20, 4, 5, 0, 0),
        text: i.toString(),
        interestName: 'questions',
      });
      await newQuestion.save();
      q.push(newQuestion);
    }

    // save q0, then q1
    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: q[0]._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: q[1]._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);
    expect(res.body.questions[0]._id).to.equal(q[1]._id.toString());
    expect(res.body.questions[1]._id).to.equal(q[0]._id.toString());

    // move q0 to front of saved list
    res = await request(app)
      .patch('/v1/question/unsave')
      .set('authorization', 0)
      .send({ questionId: q[0]._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: q[0]._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);
    expect(res.body.questions[0]._id).to.equal(q[0]._id.toString());
    expect(res.body.questions[1]._id).to.equal(q[1]._id.toString());
  });

  it('pagination', async () => {
    // create second user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.36' });
    expect(res.status).to.equal(200);

    const numQuestions = pageSize * 2;
    const q = [];
    for (let i = 0; i < numQuestions; i++) {
      newQuestion = await createQuestion({
        createdAt: new Date(1000 + i, 5, 20, 4, 5, 0, 0),
        text: i.toString(),
        interestName: 'questions',
      });
      await newQuestion.save();
      q.push(newQuestion);

      res = await request(app)
        .patch('/v1/question/save')
        .set('authorization', 0)
        .send({ questionId: newQuestion._id });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.questions[i].text).to.equal((pageSize * 2 - 1 - i).toString());
    }
    beforeId = res.body.questions[pageSize - 1]._id;

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
    }
    beforeId = res.body.questions[pageSize - 1]._id;

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // ban the entire first page
    for (let i = 0; i < pageSize; i++) {
      const question = q[pageSize * 2 - 1 - i];
      question.banned = true;
      question.interestName = 'kpop';
      await question.save();
    }

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
    }
    beforeId = res.body.questions[pageSize - 1]._id;

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({ beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('saved question is deleted', async () => {
    q1 = await createQuestion({
      createdAt: new Date(2000, 5, 20, 4, 5, 0, 0),
      text: 'qod',
      interestName: 'questions',
    });
    await q1.save();

    q2 = await createQuestion({
      createdAt: new Date(2000, 5, 20, 4, 5, 0, 0),
      text: 'qod',
      interestName: 'questions',
    });
    await q2.save();

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: q1._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: q2._id });
    expect(res.status).to.equal(200);

    await q1.deleteOne();

    // q2 is still saved
    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2._id.toString());
  });

  it('user is deleted', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.10.36' });
    expect(res.status).to.equal(200);

    newQuestion = await createQuestion({
      createdAt: new Date(2000, 5, 20, 4, 5, 0, 0),
      text: 'qod',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: newQuestion._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 1)
      .send({ questionId: newQuestion._id });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    await userLib.deleteAccount(user);

    // re-create user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.36' });
    expect(res.status).to.equal(200);

    // nothing saved
    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions).to.eql([]);

    // user 1's saved not affected
    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 1)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(newQuestion._id.toString());
  });

  it('double save', async () => {
    newQuestion = await createQuestion({
      createdAt: new Date(2000, 5, 20, 4, 5, 0, 0),
      text: 'qod',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: newQuestion._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: newQuestion._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(newQuestion._id.toString());

    res = await request(app)
      .patch('/v1/question/unsave')
      .set('authorization', 0)
      .send({ questionId: newQuestion._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/unsave')
      .set('authorization', 0)
      .send({ questionId: newQuestion._id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({});
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('error handling', async () => {
    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0);
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: '0' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/question/save')
      .set('authorization', 0)
      .send({ questionId: '551137c2f9e1fac808a5f572' });
    expect(res.status).to.equal(404);

    res = await request(app)
      .patch('/v1/question/unsave')
      .set('authorization', 0);
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/question/unsave')
      .set('authorization', 0)
      .send({ questionId: '0' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/question/unsave')
      .set('authorization', 0)
      .send({ questionId: '551137c2f9e1fac808a5f572' });
    expect(res.status).to.equal(404);

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({ beforeId: '0' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .get('/v1/question/saved')
      .set('authorization', 0)
      .query({ beforeId: '551137c2f9e1fac808a5f572' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });
});

describe('language dimensions', async () => {
  let interestIds;

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.10.36' });
    expect(res.status).to.equal(200);

    interestIds = res.body.interests.map((x) => x._id);
  });

  it('post daily limit', async () => {
    // January 1, 2017 01:00:00 AM UTC
    initDateMs = 1483232400000;
    clock = sinon.useFakeTimers({
      now: initDateMs,
    });
    msPerHour = 60 * 60 * 1000;

    constants.getDailyPostLimit.restore();
    sinon.stub(constants, 'getDailyPostLimit').returns(2);

    for (let i = 0; i < 2; i++) {
      await createQuestion({
        createdBy: '0',
        parent: interestIds[0],
        title: '0',
        text: '0',
      });
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
      });
    expect(res.status).to.equal(403);
    expect(res.error.text).to.equal('You may share again in 23 hours.');

    clock.tick(21.5 * msPerHour);
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
      });
    expect(res.status).to.equal(403);
    expect(res.error.text).to.equal('You may share again in 2 hours.');

    clock.tick(1 * msPerHour);
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
      });
    expect(res.status).to.equal(403);
    expect(res.error.text).to.equal('You may share again in 1 hour.');

    clock.tick(1 * msPerHour);
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
      });
    expect(res.status).to.equal(403);
    expect(res.error.text).to.equal('You may share again in 24 hours.');
  });

  it('post daily limit with mediaUploadPending', async () => {
    constants.getDailyPostLimit.restore();
    sinon.stub(constants, 'getDailyPostLimit').returns(1);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);

    // can still post without hitting limit
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
      });
    expect(res.status).to.equal(200);

    // now hit limit
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'second post',
      });
    expect(res.status).to.equal(403);
  });

  it('post daily limit with postLimitResetAt', async () => {
    constants.getDailyPostLimit.restore();
    sinon.stub(constants, 'getDailyPostLimit').returns(1);
    clock = sinon.useFakeTimers(100000);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.16' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'second post',
      });
    expect(res.status).to.equal(403);
    console.log(res.body, res.error);
    expect(res.body.message).to.equal('You may share again in 24 hours.');
    expect(res.body.postLimitResetAt).to.equal('1970-01-02T00:00:00.000Z');
    expect(res.error.text).to.equal('{"message":"You may share again in 24 hours.","postLimitResetAt":"1970-01-02T00:00:00.000Z"}');

    clock.restore();
  });

  it('basic functionality', async () => {
    // post question in german
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '0',
        text: '0',
        language: 'de',
      });
    expect(res.status).to.equal(200);
    expect(res.body.language).to.equal('de');
    expect(res.body.url).to.equal(`https://boo.world/u/kpop/${res.body.webId}/0-0`);

    // no language set yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
    expect((await User.findById('0')).socialFeedLanguage).to.equal();

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect((await User.findById('0')).socialFeedLanguage).to.equal('de');

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'zh' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
    expect((await User.findById('0')).socialFeedLanguage).to.equal('zh');

    // set user language to german
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['de'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.languages).to.eql(['de']);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'zh' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // set user language to chinese
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['zh'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.languages).to.eql(['zh']);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'de' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'zh' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('filter interest and language', async () => {
    // post question in german to interest 0
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
        language: 'de',
      });
    expect(res.status).to.equal(200);

    // post question in chinese to interest 1
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[1],
        title: '1',
        text: '1',
        language: 'zh',
      });
    expect(res.status).to.equal(200);

    // set user to german, interest 1
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['de'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({ interestIds: [interestIds[1]] });
    expect(res.status).to.equal(200);

    // explore shows german question
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('de');

    // following empty
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'following' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // filter on interest and/or language
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[0] })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('de');

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[0], language: 'zh' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[1] })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: interestIds[1], language: 'zh' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('zh');
  });

  it('empty language field should default to english', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
        language: '',
      });
    expect(res.status).to.equal(200);
    expect(res.body.language).to.equal('en');

    // no language set yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('en');
  });

  it('backwards compatibility', async () => {
    // question without language assumed to be english
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
      });
    expect(res.status).to.equal(200);
    expect(res.body.language).to.equal('en');

    // no language set yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('en');

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'zh' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // set user language to english
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['en'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // set user language to chinese
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['zh'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('error handling', async () => {
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0);
    expect(res.status).to.equal(422);

    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: [] });
    expect(res.status).to.equal(422);

    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: 'zh' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['zh', 'zh'] });
    expect(res.status).to.equal(422);

    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['zhongwen'] });
    expect(res.status).to.equal(422);

    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: [null] });
    expect(res.status).to.equal(422);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
        language: 'german',
      });
    expect(res.status).to.equal(422);
  });

  it('english question of day', async () => {
    // load question of day
    newQuestion = await createQuestion({
      createdAt: new Date(),
      text: 'qod 1',
      interestName: 'questions',
    });
    await newQuestion.save();

    // no language set yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('en');

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'zh' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // set user language to english
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['en'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // set user language to chinese
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['zh'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('multiple question of day', async () => {
    // load question of day
    now = Date.now();

    newQuestion = await createQuestion({
      createdAt: now - 24 * 3600 * 1000,
      text: '0',
      interestName: 'questions',
    });
    await newQuestion.save();
    newQuestion = await createQuestion({
      createdAt: now - 24 * 3600 * 1000,
      text: '0',
      language: 'zh',
      interestName: 'questions',
    });
    await newQuestion.save();
    newQuestion = await createQuestion({
      createdAt: now,
      text: '1',
      interestName: 'questions',
    });
    await newQuestion.save();
    newQuestion = await createQuestion({
      createdAt: now,
      text: '1',
      language: 'zh',
      interestName: 'questions',
    });
    await newQuestion.save();

    // no language set yet
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('en');
    expect(res.body.questions[0].text).to.equal('1');

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'en' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('en');
    expect(res.body.questions[0].text).to.equal('1');

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ language: 'zh' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].language).to.equal('zh');
    expect(res.body.questions[0].text).to.equal('1');

    // set user language
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['en', 'zh'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);
    expect(res.body.questions[0].text).to.equal('1');
    expect(res.body.questions[1].text).to.equal('1');
  });

  it('post in two dimensions coin reward', async () => {
    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.postInTwoDimensions.reward).to.equal(100);
    expect(res.body.rewards.postInTwoDimensions.received).to.equal(false);
    let { coins } = res.body;

    // post question in german
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
        language: 'de',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.postInTwoDimensions.received).to.equal(false);
    expect(res.body.coins).to.equal(coins);

    // post question in english
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
        language: 'en',
      });
    expect(res.status).to.equal(200);

    coins += 100;

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.postInTwoDimensions.received).to.equal(true);
    expect(res.body.coins).to.equal(coins);

    // post question in spanish
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: interestIds[0],
        title: '0',
        text: '0',
        language: 'es',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.postInTwoDimensions.received).to.equal(true);
    expect(res.body.coins).to.equal(coins);
  });
});

describe('awards', async () => {
  let regularAward; let premiumAward; let kpopId; let q1; let
    c1;

  beforeEach(async () => {
    for (let i = 0; i < 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.11.0' });
      expect(res.status).to.equal(200);
      expect(res.body.coinProducts.awards).to.eql(coinsConstants.awardsBatch1);

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.11.11' });
      expect(res.status).to.equal(200);
      expect(res.body.coinProducts.awards).to.eql(coinsConstants.awardsBatch1.concat(coinsConstants.awardsBatch2));

      awards = res.body.coinProducts.awards;
      regularAward = awards.find((x) => x.type == 'regular');
      premiumAward = awards.find((x) => x.type == 'premium');
      kpopId = res.body.interests[0]._id;

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', i)
        .send({ fcmToken: `token${i.toString()}` });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', i)
        .send({ firstName: `name${i.toString()}` });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', i)
        .send({
          mbti: 'ESTJ',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/gender')
        .set('authorization', i)
        .send({ gender: 'female' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/preferences')
        .set('authorization', i)
        .send({
          purpose: ['friends'],
          gender: ['female'],
          personality: ['ESTJ'],
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/birthday')
        .set('authorization', i)
        .send({
          year: new Date().getFullYear() - 31,
          month: 1,
          day: 1,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', i)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
      // mock upload two pictures
      const user = await User.findOne({ _id: i });
      user.pictures.push('picture0');
      user.pictures.push('picture1');
      user.scores.numPictures = 1;
      res = await user.save();

      metadata = await UserMetadata.findOne({ user: i.toString() });
      metadata.coins = 10000;
      await metadata.save();
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;
  });

  it('basic functionality - regular award to question', async () => {
    reset();

    // user 1 gives regular award with message to user 0's question
    res = await request(app)
      .post('/v1/question/award')
      .set('authorization', 1)
      .send({
        postId: q1Id,
        awardId: regularAward.id,
        price: regularAward.price,
        message: 'message',
      });
    expect(res.status).to.equal(200);

    // coins deducted from user 1
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(10000 - regularAward.price);

    // user 0 gains coins
    // user 0 sees award on profile
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(10000 + regularAward.reward);
    expect(res.body.user.awards).to.eql({ [regularAward.id]: 1 });
    handle0 = res.body.user.handle;

    // user 0 sees award on post and profile
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].awards).to.eql({ [regularAward.id]: 1 });
    expect(res.body.questions[0].createdBy.awards).to.eql({ [regularAward.id]: 1 });

    // user 1 sees award on user 0's profile when searching by handle
    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 1)
      .query({ handle: handle0 });
    expect(res.status).to.equal(200);
    expect(res.body.user.awards).to.eql({ [regularAward.id]: 1 });

    // user 1 sees award on user 0's profile when getting daily profiles
    res = await request(app)
      .get('/v1/user/dailyProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.profiles.length).to.equal(1);
    expect(res.body.profiles[0].awards).to.eql({ [regularAward.id]: 1 });

    // user 0 got notification
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('You got an award!');
    expect(notifs.recent.notification.body).to.equal('');
    expect(notifs.recent.data).to.eql({
      question: JSON.stringify({ _id: q1Id }),
      award: JSON.stringify({ id: regularAward.id }),
    });
    reset();

    // send another award
    res = await request(app)
      .post('/v1/question/award')
      .set('authorization', 1)
      .send({
        postId: q1Id,
        awardId: regularAward.id,
        price: regularAward.price,
        message: 'message2',
      });
    expect(res.status).to.equal(200);

    // award count increases
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].awards).to.eql({ [regularAward.id]: 2 });
    expect(res.body.questions[0].createdBy.awards).to.eql({ [regularAward.id]: 2 });
  });

  it('notifications for batch3 awards', async () => {
    reset();

    // old version - no notification
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
      .send({ appVersion: '1.13.30' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question/award')
      .set('authorization', 1)
      .send({
        postId: q1Id,
        awardId: 'peek_a_boo',
        price: 100,
        message: 'message',
      });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);
    reset();

    // 1.13.34 - notification
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '0')
      .send({ appVersion: '1.13.34' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question/award')
      .set('authorization', 1)
      .send({
        postId: q1Id,
        awardId: 'peek_a_boo',
        price: 100,
        message: 'message',
      });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('You got an award!');
    expect(notifs.recent.notification.body).to.equal('');
    expect(notifs.recent.data).to.eql({
      question: JSON.stringify({ _id: q1Id }),
      award: JSON.stringify({ id: 'peek_a_boo' }),
    });
    reset();
  });

  it('basic functionality - premium award to comment', async () => {
    reset();

    // user 1 gives premium award without message to user 0's comment
    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 1)
      .send({
        postId: c1Id,
        awardId: premiumAward.id,
        price: premiumAward.price,
      });
    expect(res.status).to.equal(200);

    // coins deducted from user 1
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(10000 - premiumAward.price);

    // user 0 gains coins
    // user 0 sees award on profile
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(10000 + premiumAward.reward);
    expect(res.body.user.awards).to.eql({ [premiumAward.id]: 1 });

    // user 0 sees award on comment and profile
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].awards).to.eql({ [premiumAward.id]: 1 });
    expect(res.body.comments[0].createdBy.awards).to.eql({ [premiumAward.id]: 1 });

    // user 0 got notification
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('You got an award!');
    expect(notifs.recent.notification.body).to.equal('');
    expect(notifs.recent.data).to.eql({
      comment: JSON.stringify({
        _id: c1Id,
        question: q1Id,
        parent: q1Id,
        postRepliedTo: q1Id,
      }),
      award: JSON.stringify({ id: premiumAward.id }),
    });
    reset();
  });

  it('ignite award to comment', async () => {
    let c1;
    premiumAward = awards.find((x) => x.id == 'ignite');


    //user 1 likes comment
    await likeComment(1,{commentId:c1Id});

    // initial score of 0
    c1 = await Comment.findById(c1Id);
    expect(Math.floor(c1.score)).to.equal(1);

    // ignite should give a score boost to user
    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 1)
      .send({
        postId: c1Id,
        awardId: premiumAward.id,
        price: premiumAward.price,
      });
    expect(res.status).to.equal(200);

    c1 = await Comment.findById(c1Id);
    let score=Math.floor(c1.score/2);
    expect(score).to.eq(1);


    // give another ignite award - no boost
    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 1)
      .send({
        postId: c1Id,
        awardId: premiumAward.id,
        price: premiumAward.price,
      });
    expect(res.status).to.equal(200);

    c1 = await Comment.findById(c1Id);
    score=Math.floor(c1.score/2);
    expect(score).to.eq(1);
  });

  it('error handling', async () => {
    // cannot give award to own post
    res = await request(app)
      .post('/v1/question/award')
      .set('authorization', 0)
      .send({
        postId: q1Id,
        awardId: regularAward.id,
        price: regularAward.price,
        message: '',
      });
    expect(res.status).to.equal(404);

    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 0)
      .send({
        postId: c1Id,
        awardId: regularAward.id,
        price: regularAward.price,
        message: '',
      });
    expect(res.status).to.equal(404);

    // invalid award id
    res = await request(app)
      .post('/v1/question/award')
      .set('authorization', 1)
      .send({
        postId: q1Id,
        awardId: '...',
        price: 100,
        message: '',
      });
    expect(res.status).to.equal(404);

    // wrong award price
    res = await request(app)
      .post('/v1/question/award')
      .set('authorization', 1)
      .send({
        postId: q1Id,
        awardId: regularAward.id,
        price: regularAward.price + 100,
        message: '',
      });
    expect(res.status).to.equal(409);

    // not enough coins
    metadata = await UserMetadata.findOne({ user: '1' });
    metadata.coins = regularAward.price / 2;
    await metadata.save();

    res = await request(app)
      .post('/v1/question/award')
      .set('authorization', 1)
      .send({
        postId: q1Id,
        awardId: regularAward.id,
        price: regularAward.price,
        message: '',
      });
    expect(res.status).to.equal(403);
  });

  it('view award sender', async () => {
    // user 1 gives award non-anonymously
    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 1)
      .send({
        postId: c1Id,
        awardId: premiumAward.id,
        price: premiumAward.price,
        anonymous: false,
      });
    expect(res.status).to.equal(200);

    // user 0 can see award sender
    res = await request(app)
      .get('/v1/comment/awardSenders')
      .set('authorization', 0)
      .query({
        postId: c1Id,
        awardId: premiumAward.id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.senders.length).to.equal(1);
    expect(res.body.senders[0].profile._id).to.equal('1');
    expect(res.body.senders[0].awardId).to.equal(premiumAward.id);

    // user 0's notification should include senderId
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('You got an award!');
    expect(notifs.recent.notification.body).to.equal('');
    expect(notifs.recent.data).to.eql({
      comment: JSON.stringify({
        _id: c1Id,
        question: q1Id,
        parent: q1Id,
        postRepliedTo: q1Id,
      }),
      award: JSON.stringify({ id: premiumAward.id }),
      senderId: '1',
    });
    reset();

    // user 1 cannot see award sender
    res = await request(app)
      .get('/v1/comment/awardSenders')
      .set('authorization', 1)
      .query({
        postId: c1Id,
        awardId: premiumAward.id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.senders.length).to.equal(0);

    // user 0 tries to view sender for award that was not received
    res = await request(app)
      .get('/v1/comment/awardSenders')
      .set('authorization', 1)
      .query({
        postId: c1Id,
        awardId: regularAward.id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.senders.length).to.equal(0);
  });

  it('view award sender - anonymous', async () => {
    // user 1 gives award anonymously
    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 1)
      .send({
        postId: c1Id,
        awardId: premiumAward.id,
        price: premiumAward.price,
        anonymous: true,
      });
    expect(res.status).to.equal(200);

    // not able to see award sender
    res = await request(app)
      .get('/v1/comment/awardSenders')
      .set('authorization', 0)
      .query({
        postId: c1Id,
        awardId: premiumAward.id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.senders.length).to.equal(0);

    // user 0's notification should not include senderId
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('You got an award!');
    expect(notifs.recent.notification.body).to.equal('');
    expect(notifs.recent.data).to.eql({
      comment: JSON.stringify({
        _id: c1Id,
        question: q1Id,
        parent: q1Id,
        postRepliedTo: q1Id,
      }),
      award: JSON.stringify({ id: premiumAward.id }),
    });
    reset();
  });

  it('view all award senders', async () => {
    // user 1 gives premium award non-anonymously
    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 1)
      .send({
        postId: c1Id,
        awardId: premiumAward.id,
        price: premiumAward.price,
        anonymous: false,
      });
    expect(res.status).to.equal(200);

    // user 2 gives regular award non-anonymously
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 2)
      .send({
        postId: c1Id,
        awardId: regularAward.id,
        price: regularAward.price,
        anonymous: false,
      });
    expect(res.status).to.equal(200);

    // user 3 gives regular award anonymously
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/comment/award')
      .set('authorization', 3)
      .send({
        postId: c1Id,
        awardId: regularAward.id,
        price: regularAward.price,
        anonymous: true,
      });
    expect(res.status).to.equal(200);

    // user 0 gets all award senders
    res = await request(app)
      .get('/v1/comment/awardSenders')
      .set('authorization', 0)
      .query({
        postId: c1Id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.senders.length).to.equal(2);
    expect(res.body.senders[0].profile._id).to.equal('2');
    expect(res.body.senders[0].awardId).to.equal(regularAward.id);
    expect(res.body.senders[1].profile._id).to.equal('1');
    expect(res.body.senders[1].awardId).to.equal(premiumAward.id);
  });
});

describe('nearby', async () => {
  let kpopId; let q1; let
    c1;

  beforeEach(async () => {
    for (let i = 0; i < 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.11.0' });
      expect(res.status).to.equal(200);
      kpopId = res.body.interests[0]._id;

      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', i)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;
  });

  it('nearby', async () => {
    // user viewing own post
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].createdBy.nearby).to.equal();

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].createdBy.nearby).to.equal();

    // other user viewing post
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].createdBy.nearby).to.equal(true);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].createdBy.nearby).to.equal(true);
  });

  it('not nearby', async () => {
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 40,
        longitude: -74,
      });
    expect(res.status).to.equal(200);

    // user viewing own post
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].createdBy.nearby).to.equal();

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].createdBy.nearby).to.equal();

    // other user viewing post
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].createdBy.nearby).to.equal();

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].createdBy.nearby).to.equal();
  });
});

describe('nearby sort', async () => {
  let kpopId; let
    q1Id;

  beforeEach(async () => {
    const newQuestion = await createQuestion({
      createdAt: new Date(),
      text: 'qod',
      interestName: 'questions',
    });
    q1Id = newQuestion._id.toString();
  });

  it('only qod visible', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].text).to.equal('qod');

    beforeId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('sort by nearby', async () => {
    for (let i = 0; i < 2 * pageSize; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.11.0' });
      expect(res.status).to.equal(200);
      kpopId = res.body.interests[0]._id;

      res = await request(app)
        .post('/v1/question')
        .set('authorization', i)
        .send({
          interestId: kpopId,
          title: i.toString(),
          text: i.toString(),
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/comment')
        .set('authorization', i)
        .send({
          questionId: q1Id,
          parentId: q1Id,
          text: i.toString(),
        });
      expect(res.status).to.equal(200);
    }

    // user 0 no location yet

    // feed sorted by recent
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1 + pageSize);
    console.log('user 0 before set location res.body.questions:', res.body.questions)
    expect(res.body.questions[0].text).to.equal('qod');
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.questions[1 + i].text).to.equal((pageSize * 2 - 1 - i).toString());
    }

    beforeId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
    }

    beforeId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // comments sorted by recent
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i].text).to.equal((pageSize * 2 - 1 - i).toString());
    }

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i].text).to.equal((pageSize - 1 - i).toString());
    }

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // user 0 has location, but no one nearby
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    // feed empty except for qod and own post
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby' });
    expect(res.status).to.equal(200);
    console.log('user 0 after set location res.body.questions:', res.body.questions)

    expect(res.body.questions.length).to.equal(1); // app_617 user own post excluded when sort = nearby
    expect(res.body.questions[0].text).to.equal('qod');

    beforeId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // own comment first, then rest sorted by recent
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    expect(res.body.comments[0].text).to.equal('0');
    for (let i = 0; i < pageSize - 1; i++) {
      expect(res.body.comments[1 + i].text).to.equal((pageSize * 2 - 1 - i).toString());
    }

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i].text).to.equal((pageSize - i).toString());
    }

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    console.log(res.body.comments);
    expect(res.body.comments.length).to.equal(0);

    // assign locations to half of users
    for (let i = 0; i < pageSize; i++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', i)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
    }

    // feed should show nearby posts sorted by recent
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(pageSize);
    expect(res.body.questions[0].text).to.equal('qod');
    for (let i = 1; i < pageSize; i++) {

      expect(res.body.questions[i].text).to.equal((pageSize - i).toString());
    }

    beforeId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // comments show nearby sorted by recent
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i].text).to.equal((pageSize - 1 - i).toString());
    }

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i].text).to.equal((pageSize * 2 - 1 - i).toString());
    }

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    console.log(res.body.comments);
    expect(res.body.comments.length).to.equal(0);

    // assign locations to rest of users
    for (let i = pageSize; i < 2 * pageSize; i++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', i)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
    }

    // feed sorted by recent
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1 + pageSize);
    expect(res.body.questions[0].text).to.equal('qod');
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.questions[1 + i].text).to.equal((pageSize * 2 - 1 - i).toString());
    }

    beforeId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(pageSize - 1 );
    for (let i = 0; i < pageSize - 1; i++) {

      expect(res.body.questions[i].text).to.equal((pageSize - 1 - i).toString());
    }

    beforeId = res.body.questions[res.body.questions.length - 1]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // comments sorted by recent
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i].text).to.equal((pageSize * 2 - 1 - i).toString());
    }

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i].text).to.equal((pageSize - 1 - i).toString());
    }

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby', beforeId });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);
  });

  it('sort by nearby with social preference filters', async () => {
    for (let i = 0; i < 5; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.11.0' });
      expect(res.status).to.equal(200);
      kpopId = res.body.interests[0]._id;

      res = await request(app)
        .post('/v1/question')
        .set('authorization', i)
        .send({
          interestId: kpopId,
          title: i.toString(),
          text: i.toString(),
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/comment')
        .set('authorization', i)
        .send({
          questionId: q1Id,
          parentId: q1Id,
          text: i.toString(),
        });
      expect(res.status).to.equal(200);
    }

    // activate filter for personality
    res = await request(app)
      .put('/v1/user/socialPreferencesActivated')
      .set('authorization', 0)
      .send({ socialPreferencesActivated: true });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          personality: ['INTJ'],
        },
      });
    expect(res.status).to.equal(200);

    // assign locations to user 0, 1, 2
    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .put('/v1/user/location')
        .set('authorization', i)
        .send({
          latitude: 21.30,
          longitude: -157.85,
        });
      expect(res.status).to.equal(200);
    }

    // assign personality to user 1, 3
    for (let i of [1, 3]) {
      res = await request(app)
        .put('/v1/user/personality')
        .set('authorization', i)
        .send({
          mbti: 'INTJ',
        });
      expect(res.status).to.equal(200);
    }

    // feed should show posts from user 1
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);
    expect(res.body.questions[0].text).to.equal('qod');
    expect(res.body.questions[1].text).to.equal('1');

    // comments show nearby sorted by recent
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q1Id, sort: 'nearby' });
    expect(res.status).to.equal(200);
    expect(res.body.comments[0].text).to.equal('2');
    expect(res.body.comments[1].text).to.equal('1');
  });
});

describe('social to dm', async () => {
  let kpopId; let q1Id; let
    c1Id;

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.11.0' });
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'q1',
        text: 'q1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        parentId: q1Id,
        text: 'c1',
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;
  });

  it('reply to question', async () => {
    socket0 = await initSocket(0);
    socketPromise0 = getSocketPromise(socket0, 'pending chat');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.11.0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 1)
      .send({
        user: '0',
        message: 'Hi',
        price: 50,
        quotedQuestion: q1Id,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('1');
    expect(user.metrics.numDMSentFromSocial).to.equal(1);

    res = await socketPromise0.catch((err) => { console.error(err); });
    expect(res.lastMessage.quotedQuestion).to.equal(q1Id);
    expect(res.lastMessage.quotedComment).to.equal();

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].lastMessage.quotedQuestion).to.equal(q1Id);
    expect(res.body.chats[0].lastMessage.quotedComment).to.equal();

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].quotedQuestion).to.equal(q1Id);
    expect(res.body[0].quotedComment).to.equal();

    await destroySocket(socket0);
  });

  it('reply to comment', async () => {
    socket0 = await initSocket(0);
    socketPromise0 = getSocketPromise(socket0, 'pending chat');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.11.0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendDirectMessage')
      .set('authorization', 1)
      .send({
        user: '0',
        message: 'Hi',
        price: 50,
        quotedComment: c1Id,
      });
    expect(res.status).to.equal(200);

    res = await socketPromise0.catch((err) => { console.error(err); });
    expect(res.lastMessage.quotedQuestion).to.equal();
    expect(res.lastMessage.quotedComment).to.equal(c1Id);

    res = await request(app)
      .get('/v1/chat/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].lastMessage.quotedQuestion).to.equal();
    expect(res.body.chats[0].lastMessage.quotedComment).to.equal(c1Id);

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].quotedQuestion).to.equal();
    expect(res.body[0].quotedComment).to.equal(c1Id);

    await destroySocket(socket0);
  });
});

it('add names to users', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  const kpopId = res.body.interests[0]._id;

  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestIds: [kpopId],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/preferences/interests')
    .set('authorization', 0)
    .send({
      interests: [kpopId],
    });
  expect(res.status).to.equal(200);

  let user;
  user = await User.findById(0);
  expect(user.interestNames).to.eql(['kpop']);
  expect(user.preferences.interestNames).to.eql(['kpop']);
  user.interestNames = undefined;
  user.preferences.interestNames = undefined;
  await user.save();

  user = await User.findById(0);
  expect(user.interestNames).to.eql([]);
  expect(user.preferences.interestNames).to.eql();

  await interestLib.migrateUsersInterestName();

  user = await User.findById(0);
  expect(user.interestNames).to.eql(['kpop']);
  expect(user.preferences.interestNames).to.eql(['kpop']);
});

it('add names to questions', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  const kpopId = res.body.interests[0]._id;

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestId: kpopId,
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);

  let question;
  question = await Question.findOne();
  expect(question.interestName).to.equal('kpop');
  question.interestName = undefined;
  await question.save();

  question = await Question.findOne();
  expect(question.interestName).to.equal();

  await interestLib.migrateQuestionsInterestName();

  question = await Question.findOne();
  expect(question.interestName).to.equal('kpop');
});

it('num followers', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({ interestNames: ['kpop', 'latin'] });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(1);

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'latin' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(1);

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'chess' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(0);

  res = await request(app)
    .post('/v1/worker/recordInterestMetrics')
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/web/numFollowersPerInterest')
  expect(res.status).to.equal(200);
  expect(res.body.numFollowersPerInterest).to.eql({
    kpop: 1,
    latin: 1,
    chess: 0,
  });

  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({ interestNames: ['kpop', 'chess'] });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(1);

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'latin' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(0);

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'chess' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(1);

  res = await request(app)
    .post('/v1/worker/recordInterestMetrics')
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/web/numFollowersPerInterest')
  expect(res.status).to.equal(200);
  expect(res.body.numFollowersPerInterest).to.eql({
    kpop: 1,
    latin: 0,
    chess: 1,
  });

  // test migration
  interest = await Interest.findOne({ name: 'kpop' });
  interest.numFollowers = 0;
  await interest.save();

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(0);

  await interestLib.addNumFollowersToInterests();

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(1);

  // check web route
  res = await request(app)
    .get('/web/interest')
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(1);

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'latin' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(0);

  // special handling for 'questions'
  res = await request(app)
    .get('/web/interest')
    .query({ name: 'questions' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numFollowers).to.equal(null);
});

it('similar interests', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.similar).to.eql([]);

  res = await request(app)
    .get('/v1/interest/similar')
    .set('authorization', 0)
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interests).to.eql([]);

  res = await request(app)
    .get('/web/cached/interest/similar')
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interests).to.eql([]);

  // add similar
  const doc = await Interest.findOne({ name: 'kpop' });
  doc.similar = ['latin'];
  await doc.save();

  res = await request(app)
    .get('/v1/interest')
    .set('authorization', 0)
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.similar).to.eql(['latin']);

  res = await request(app)
    .get('/v1/interest/similar')
    .set('authorization', 0)
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('latin');
  expect(res.body.interests[0].numFollowers).to.equal(0);

  res = await request(app)
    .get('/web/cached/interest/similar')
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('latin');
  expect(res.body.interests[0].numFollowers).to.equal(0);
});

it('autocomplete query', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { all: true };
  res = await user.save();

  // prefix found
  res = await request(app)
    .get('/v1/interest/autocomplete')
    .query({ query: 'kp' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('kpop');

  // check web route
  res = await request(app)
    .get('/web/interest/autocomplete')
    .query({ query: 'kp' })
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('kpop');

  // not found
  res = await request(app)
    .get('/v1/interest/autocomplete')
    .query({ query: 'aaa' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(0);

  // handle embedded regex
  res = await request(app)
    .get('/v1/interest/autocomplete')
    .query({ query: '.*' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(0);

  // should not find rejected interest
  sinon.stub(interestLib, 'shouldInterestBeApproved')
    .callsFake((params) => {
      const impl = function (resolve, reject) {
        resolve(false);
      };
      return new Promise(impl);
    });

  res = await request(app)
    .post('/v1/interest')
    .set('authorization', 0)
    .send({ name: 'newinterest' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/interest/autocomplete')
    .query({ query: 'new' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(0);

  // unicode should not error
  res = await request(app)
    .get('/v1/interest/autocomplete')
    .query({ query: '%D8%AC%D9%86%D8%AF%D9%87' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(0);
});

it('non-english interests', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { all: true };
  res = await user.save();

  // create non-english interest
  sinon.stub(interestLib, 'shouldInterestBeApproved')
    .callsFake((params) => {
      const impl = function (resolve, reject) {
        resolve(true);
      };
      return new Promise(impl);
    });

  res = await request(app)
    .post('/v1/interest')
    .set('authorization', 0)
    .send({ name: '图书馆' });
  expect(res.status).to.equal(200);

  // search for it
  res = await request(app)
    .get('/v1/interest/autocomplete')
    .query({ query: '图书' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('图书馆');

  // unicode should be escaped in url
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: '图书馆',
      title: '我喜欢看书',
      text: '你呢？',
    });
  expect(res.status).to.equal(200);
  const q = res.body;
  expect(q.url).to.equal(`https://boo.world/u/%E5%9B%BE%E4%B9%A6%E9%A6%86/${q.webId}/%E6%88%91%E5%96%9C%E6%AC%A2%E7%9C%8B%E4%B9%A6-%E4%BD%A0%E5%91%A2`);
});

it('non-english interests: unicode with marks', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { all: true };
  res = await user.save();

  // create non-english interest
  sinon.stub(interestLib, 'shouldInterestBeApproved')
    .callsFake((params) => {
      const impl = function (resolve, reject) {
        resolve(true);
      };
      return new Promise(impl);
    });

  res = await request(app)
    .post('/v1/interest')
    .set('authorization', 0)
    .send({ name: 'ไก่' });
  expect(res.status).to.equal(200);

  // search for it
  res = await request(app)
    .get('/v1/interest/autocomplete')
    .query({ query: 'ไ' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('ไก่');

  // unicode should be escaped in url
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'ไก่',
      title: '我喜欢看书',
      text: '你呢？',
    });
  expect(res.status).to.equal(200);
  const q = res.body;
  expect(q.url).to.equal(`https://boo.world/u/%E0%B9%84%E0%B8%81%E0%B9%88/${q.webId}/%E6%88%91%E5%96%9C%E6%AC%A2%E7%9C%8B%E4%B9%A6-%E4%BD%A0%E5%91%A2`);
});

it('paginated popular interests', async () => {
  await Interest.deleteMany();

  const interests = [];
  interests.push({
    name: 'a',
    interest: 'a',
    numFollowers: 10,
    numFollowersSortIndex: 10.9,
  });
  interests.push({
    name: 'y',
    interest: 'y',
    numFollowers: 10,
    numFollowersSortIndex: 10.8,
  });
  interests.push({
    name: 'z',
    interest: 'z',
    numFollowers: 10,
    numFollowersSortIndex: 10.7,
  });
  interests.push({
    name: 'b',
    interest: 'b',
    numFollowers: 1,
    numFollowersSortIndex: 1.7,
  });
  await Interest.insertMany(interests);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/interest/popular')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(2);
  expect(res.body.interests[0].name).to.equal('a');
  expect(res.body.interests[0].interest).to.equal('a');
  expect(res.body.interests[0].numFollowers).to.equal(10);
  expect(res.body.interests[1].name).to.equal('y');
  expect(res.body.interests[1].interest).to.equal('y');
  expect(res.body.interests[1].numFollowers).to.equal(10);

  const savedRes = res.body;
  res = await request(app)
    .get('/web/popularInterests');
  expect(res.status).to.equal(200);
  expect(res.body).to.eql(savedRes);

  before = res.body.interests[res.body.interests.length - 1].name;
  res = await request(app)
    .get('/v1/interest/popular')
    .query({ before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(2);
  expect(res.body.interests[0].name).to.equal('z');
  expect(res.body.interests[0].interest).to.equal('z');
  expect(res.body.interests[0].numFollowers).to.equal(10);
  expect(res.body.interests[1].name).to.equal('b');
  expect(res.body.interests[1].interest).to.equal('b');
  expect(res.body.interests[1].numFollowers).to.equal(1);

  before = res.body.interests[res.body.interests.length - 1].name;
  res = await request(app)
    .get('/v1/interest/popular')
    .query({ before })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(0);
});

it('interests for onboarding', async () => {
  // english
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.11.55' });
  expect(res.status).to.equal(200);
  expect(res.body.interests).to.eql([
    { category: 'Music', name: 'kpop' },
    { category: 'Music', name: 'latin' },
    { category: 'Games', name: 'chess' },
  ]);

  await Interest.insertMany([
    { interest: '#music', name: 'music', numFollowers: 3 },
    { interest: '#food', name: 'food', numFollowers: 1 },
    { interest: '#音楽', name: '音楽', numFollowers: 2 },
    { interest: '#映画', name: '映画', numFollowers: 5 },
  ]);

  res = await request(app)
    .get('/v1/interest/popularInterestsForOnboarding')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interestNames.length).to.equal(50);
  expect(res.body.interestNames[0]).to.equal('music');

  res = await request(app)
    .get('/v1/interest/popularInterestsForOnboardingWithNumFollowers')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(2);
  expect(res.body.interests.find(x => x.name == 'food')).to.eql({ name: 'food', numFollowers: 1 });
  expect(res.body.interests.find(x => x.name == 'music')).to.eql({ name: 'music', numFollowers: 3 });

  // japanese
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.11.55', locale: 'ja' });
  expect(res.status).to.equal(200);
  expect(res.body.interests).to.eql([
    { category: 'Music', name: 'kpop' },
    { category: 'Music', name: 'ラテン音楽' },
    { category: 'Games', name: 'チェス' },
  ]);

  res = await request(app)
    .get('/v1/interest/popularInterestsForOnboarding')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.interestNames.length).to.equal(50);
  expect(res.body.interestNames[0]).to.equal('音楽');

  res = await request(app)
    .get('/v1/interest/popularInterestsForOnboardingWithNumFollowers')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(2);
  expect(res.body.interests.find(x => x.name == '音楽')).to.eql({ name: '音楽', numFollowers: 2 });
  expect(res.body.interests.find(x => x.name == '映画')).to.eql({ name: '映画', numFollowers: 5 });
});

it('error updating interests', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 0 });
  user.interestNames = null;
  await user.save();

  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({ interestNames: ['kpop', 'latin'] });
  expect(res.status).to.equal(200);
});

it('hidden interests', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.hiddenInterests).to.eql([]);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'q1',
      text: 'q1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'latin',
      title: 'q2',
      text: 'q2',
    });
  expect(res.status).to.equal(200);
  const q2Id = res.body._id;

  // see all topics on explore
  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0]._id).to.equal(q2Id);
  expect(res.body.questions[1]._id).to.equal(q1Id);

  // hide kpop
  res = await request(app)
    .put('/v1/user/hiddenInterests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.hiddenInterests).to.eql(['kpop']);

  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q2Id);

  // hide latin
  res = await request(app)
    .put('/v1/user/hiddenInterests')
    .set('authorization', 0)
    .send({
      interestNames: ['latin'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);

  // hide kpop and latin
  res = await request(app)
    .put('/v1/user/hiddenInterests')
    .set('authorization', 0)
    .send({
      interestNames: ['latin', 'kpop'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  // un-hide
  res = await request(app)
    .put('/v1/user/hiddenInterests')
    .set('authorization', 0)
    .send({
      interestNames: [],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.hiddenInterests).to.eql([]);

  res = await request(app)
    .get('/v1/question/feed')
    .query({})
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0]._id).to.equal(q2Id);
  expect(res.body.questions[1]._id).to.equal(q1Id);

  // follow and hide an interest
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop', 'latin'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/hiddenInterests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/feed')
    .query({ filter: 'following' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q2Id);

  // invalid input
  res = await request(app)
    .put('/v1/user/hiddenInterests')
    .set('authorization', 0)
    .send({
      interestNames: ['...'],
    });
  expect(res.status).to.equal(422);

  // duplicates should be handled
  res = await request(app)
    .put('/v1/user/hiddenInterests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop', 'kpop'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.hiddenInterests).to.eql(['kpop']);
});

it('question poster can ban comment', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'q1',
      text: 'q1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: 'c1',
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;

  // user 2 cannot ban c1
  res = await request(app)
    .put('/v1/comment/ban')
    .set('authorization', 2)
    .send({
      commentId: c1Id,
    });
  expect(res.status).to.equal(403);

  // invalid input by user 0
  res = await request(app)
    .put('/v1/comment/ban')
    .set('authorization', 0)
    .send({
      commentId: 'c1',
    });
  expect(res.status).to.equal(422);

  res = await request(app)
    .put('/v1/comment/ban')
    .set('authorization', 0)
    .send({
      commentId: q1Id,
    });
  expect(res.status).to.equal(404);

  // user 0 bans the comment
  res = await request(app)
    .put('/v1/comment/ban')
    .set('authorization', 0)
    .send({
      commentId: c1Id,
    });
  expect(res.status).to.equal(200);

  // user 2 does not see the comment
  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  // user 1 can still see their own comment
  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
});

it('sort by top', async () => {
  try {
    clock = sinon.useFakeTimers();

    // question of day will not appear when sorting by top
    await createQuestion({
      createdAt: new Date(),
      text: 'qod 1',
      interestName: 'questions',
    });

    const numUsers = 7;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;
    latinId = res.body.interests[1]._id;

    // post questions
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    // like question
    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .patch('/v1/question/like')
        .set('authorization', i)
        .send({ questionId: q1Id });
      expect(res.status).to.equal(200);
    }

    console.log(await Question.find());

    // the liked question should appear first
    for (const sort of ['topAllTime', 'topYear', 'topMonth', 'topWeek']) {
      res = await request(app)
        .get('/v1/question/feed')
        .query({ sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(2);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[1]._id).to.equal(q2Id);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .query({ interestId: kpopId, sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(2);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[1]._id).to.equal(q2Id);
    }

    // 1+ weeks pass
    for (let i = 0; i < 4 * 10; i++) {
      clock.tick(6 * 60 * 60 * 1000);

      res = await request(app)
        .post('/v1/worker/updateQuestionScores')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }

    // new question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: 'title3',
      });
    expect(res.status).to.equal(200);
    const q3Id = res.body._id;

    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .patch('/v1/question/like')
        .set('authorization', i)
        .send({ questionId: q3Id });
      expect(res.status).to.equal(200);
    }

    console.log(await Question.find());

    // topWeek should include the older questions after the recent one
    for (const sort of ['topWeek']) {
      res = await request(app)
        .get('/v1/question/feed')
        .query({ sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q3Id);
      expect(res.body.questions[1]._id).to.equal(q1Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .query({ interestId: kpopId, sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q3Id);
      expect(res.body.questions[1]._id).to.equal(q1Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);
    }

    // other sort options should include all
    for (const sort of ['topAllTime', 'topYear', 'topMonth']) {
      res = await request(app)
        .get('/v1/question/feed')
        .query({ sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[1]._id).to.equal(q3Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .query({ interestId: kpopId, sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[1]._id).to.equal(q3Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);
    }

    // 1+ months pass
    for (let i = 0; i < 35; i++) {
      clock.tick(24 * 60 * 60 * 1000);

      res = await request(app)
        .post('/v1/worker/updateQuestionScores')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .post('/v1/worker/updateQuestionScores')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    console.log(await Question.find());

    for (const sort of ['topMonth', 'topWeek']) {
      res = await request(app)
        .get('/v1/question/feed')
        .query({ sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q3Id);
      expect(res.body.questions[1]._id).to.equal(q1Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .query({ interestId: kpopId, sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q3Id);
      expect(res.body.questions[1]._id).to.equal(q1Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);
    }

    for (const sort of ['topAllTime', 'topYear']) {
      res = await request(app)
        .get('/v1/question/feed')
        .query({ sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[1]._id).to.equal(q3Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .query({ interestId: kpopId, sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[1]._id).to.equal(q3Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);
    }

    // 1+ years pass
    clock.tick(300 * 24 * 60 * 60 * 1000);
    for (let i = 0; i < 70; i++) {
      clock.tick(24 * 60 * 60 * 1000);

      res = await request(app)
        .post('/v1/worker/updateQuestionScores')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .post('/v1/worker/updateQuestionScores')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    console.log(await Question.find());

    for (const sort of ['topYear']) {
      res = await request(app)
        .get('/v1/question/feed')
        .query({ sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q3Id);
      expect(res.body.questions[1]._id).to.equal(q1Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .query({ interestId: kpopId, sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q3Id);
      expect(res.body.questions[1]._id).to.equal(q1Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);
    }

    for (const sort of ['topAllTime', 'topMonth', 'topWeek']) {
      res = await request(app)
        .get('/v1/question/feed')
        .query({ sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[1]._id).to.equal(q3Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .query({ interestId: kpopId, sort })
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[0]._id).to.equal(q1Id);
      expect(res.body.questions[1]._id).to.equal(q3Id);
      expect(res.body.questions[2]._id).to.equal(q2Id);
    }
  } finally {
    clock.restore();
  }
});

it('get likes on post with no likes', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  res = await request(app)
    .get('/v1/question/likes')
    .query({ questionId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.totalPages).to.equal(0);
  expect(res.body.usersThatLiked).to.eql([]);
});

it('sort by top - infinite loop bug with before and banned post', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // post questions
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title2',
      text: 'text2',
    });
  expect(res.status).to.equal(200);
  const q2Id = res.body._id;

  // like q2
  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 1)
    .send({ questionId: q2Id });
  expect(res.status).to.equal(200);

  // ban q1
  const q = await Question.findById(q1Id);
  q.banned = true;
  await q.save();

  // getting top with before should not cause infinite loop
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'topAllTime', beforeId: q2Id })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'topYear', beforeId: q2Id })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'topMonth', beforeId: q2Id })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);

  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'topWeek', beforeId: q2Id })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);
});

describe('social filters', async () => {
  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'q1',
        text: 'q1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/socialPreferencesActivated')
      .set('authorization', 0)
      .send({ socialPreferencesActivated: true });
    expect(res.status).to.equal(200);
  });

  it('init app', async () => {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minAge: 40,
          countries: ['US'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.socialPreferencesActivated).to.equal(true);
    expect(res.body.user.socialPreferences).to.eql({
      minAge: 40,
      countries: ['US'],
    });

    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          maxAge: 40,
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.socialPreferences).to.eql({
      maxAge: 40,
    });

    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({});
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.socialPreferences).to.eql();
  });

  it('age', async () => {
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        minAge: 25,
        maxAge: 35,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minAge: 40,
          maxAge: 50,
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minAge: 20,
          maxAge: 25,
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minAge: 20,
          maxAge: 40,
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // other user's age filter doesn't match
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        minAge: 33,
        maxAge: 35,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // reset social preference age filter
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minAge: 18,
          maxAge: 200,
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('age - edge case user has null birthday', async () => {
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        minAge: 25,
        maxAge: 35,
      });
    expect(res.status).to.equal(200);

    // should not error
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minAge: 20,
          maxAge: 40,
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('show verified only', async () => {
    // user not verified, cannot use filter
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          showVerifiedOnly: true,
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.socialPreferences).to.eql({
      showVerifiedOnly: false,
    });

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // verify user 0
    user = await User.findOne({ _id: 0 });
    user.verification.status = 'verified';
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.socialPreferences).to.eql({
      showVerifiedOnly: true,
    });

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // verify user 1
    user = await User.findOne({ _id: 1 });
    user.verification.status = 'verified';
    await user.save();

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // unverify user 1
    user = await User.findOne({ _id: 1 });
    user.verification.status = 'rejected';
    await user.save();

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

  });

  it('personality', async () => {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          personality: ['INTJ'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 1)
      .send({
        mbti: 'INTJ',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
    .put('/v1/user/personality')
    .set('authorization', 1)
    .send({
      mbti: 'INFJ',
    });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
    });

  it('country', async () => {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          countries: ['US'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 23.30,
        longitude: 23.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('enneagram', async () => {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          enneagrams: ['1w2'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/enneagram')
      .set('authorization', 1)
      .send({ enneagram: '1w2' });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    res = await request(app)
      .put('/v1/user/enneagram')
      .set('authorization', 1)
      .send({ enneagram: '2w1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('horoscope', async () => {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          horoscopes: ['Capricorn'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    {
      const user = await User.findById('1');
      user.horoscope = 'Aquarius';
      await user.save();
    }
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('dating/friends', async () => {
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          dating: [],
          friends: ['female'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        dating: ['female'],
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        dating: [],
        friends: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    await User.updateOne({ _id: '0' }, { $set: { gender: 'female' } });//user gender changed

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['female'],
        friends: [],
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('disable social preferences', async () => {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          enneagrams: ['1w2'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/socialPreferencesActivated')
      .set('authorization', 0)
      .send({ socialPreferencesActivated: false });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.socialPreferencesActivated).to.equal(false);
    expect(res.body.user.socialPreferences).to.eql({
      enneagrams: ['1w2'],
    });

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('ignore social preferences when searching', async () => {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          enneagrams: ['1w2'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // should be visible when searching for 'kpop'
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0)
      .query({ interestName: 'kpop' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // should be visible when searching by keyword
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ search: 'q1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('view specific interest group', async () => {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          personality: ['INTJ'],
        },
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0)
      .query({ interestName: 'kpop' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 1)
      .send({
        mbti: 'INTJ',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0)
      .query({ interestName: 'kpop' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    await SocialQueryCache.updateMany({}, { $set: { updatedAt: Date.now() - 24 * 3600 * 1000 } });//expire query cache

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0)
      .query({ interestName: 'kpop' });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  /*
  it('should NOT return feeds from crystal level below preference', async function() {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minKarma: 100,
        },
      })
    expect(res.status).to.equal(200);

    // user 0 fetch feeds
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0)
      .query({ interestName: 'kpop' })

    expect(res.status).to.equal(200);
    //temporary karma fix
    expect(res.body.questions.length).to.equal(1);
  });

  it('should create crystal level preference', async function() {
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minKarma: 100,
        },
      })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user')
      .set('authorization', '0')
    expect(res.status).to.equal(200);
    expect(res.body.socialPreferences.minKarma).to.equal(100);
  });

  it('should return feeds from crystal level preference and above', async function() {
    // set user 0 crystalLevel
    res = await request(app)
      .put('/v1/user/socialPreferences')
      .set('authorization', 0)
      .send({
        socialPreferences: {
          minKarma: 250,
        },
      })
    expect(res.status).to.equal(200);

    // init user 100
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 100)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);

    await User.findOneAndUpdate({_id: 100}, {$set: { karma: 500 }});

    // user 100 creates question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 100)
      .send({
        interestName: 'kpop',
        title: 'title from user 100',
        text: 'text from user 100',
      })
    expect(res.status).to.equal(200);
    let q100Id = res.body._id;

    // user 0 fetch feeds
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0)
      .query({ interestName: 'kpop' })

    expect(res.status).to.equal(200);
    //temporary karma fix
    expect(res.body.questions.length).to.equal(2);
    expect(res.body.questions[0]._id).to.equal(q100Id);
    expect(res.body.questions[0].title).to.equal('title from user 100');
    expect(res.body.questions[0].text).to.equal('text from user 100');

    await User.findOneAndUpdate({_id: 100}, {$set: { karma: 100 }});

    // user 0 fetch feeds
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0)
      .query({ interestName: 'kpop' })

    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);
  });
  */
});

async function logScoreData() {
  console.log(await Question.find({}, { score: 1 }));
}
it('sort by rising - score', async () => {
  try {
    clock = sinon.useFakeTimers();

    // create several users
    const numUsers = 7;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }

    user = await User.findOne({ _id: 0 });
    user.config.boost_first_three_posts = true;
    await user.save();

    // get interest for later queries
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const userInterest = res.body.interests[0]._id;

    const post_ids = [];
    // post 3 questions
    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 0)
        .send({
          interestId: userInterest,
          title: `title${i}`,
          text: `text${i}`,
        });
      expect(res.status).to.equal(200);
      post_ids.push(res.body._id);
    }

    // like question 1 and 2 by some users
    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .patch('/v1/question/like')
        .set('authorization', i)
        .send({ questionId: post_ids[1] });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/question/like')
        .set('authorization', i)
        .send({ questionId: post_ids[2] });
      expect(res.status).to.equal(200);
    }

    // add extra like to question 1
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 6)
      .send({ questionId: post_ids[1] });
    expect(res.status).to.equal(200);

    // the more liked questions should appear first
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'rising' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 3);
    expect(res.body.questions[0]._id).to.equal(post_ids[1]);
    expect(res.body.questions[1]._id).to.equal(post_ids[2]);
    expect(res.body.questions[2]._id).to.equal(post_ids[0]);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: userInterest, sort: 'rising' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 3);
    expect(res.body.questions[0]._id).to.equal(post_ids[1]);
    expect(res.body.questions[1]._id).to.equal(post_ids[2]);
    expect(res.body.questions[2]._id).to.equal(post_ids[0]);

    // pagination support descending
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'rising', beforeId: post_ids[1] })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 2);
    expect(res.body.questions[0]._id).to.equal(post_ids[2]);
    expect(res.body.questions[1]._id).to.equal(post_ids[0]);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: userInterest, sort: 'rising', beforeId: post_ids[1] })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 2);
    expect(res.body.questions[0]._id).to.equal(post_ids[2]);
    expect(res.body.questions[1]._id).to.equal(post_ids[0]);

    // pagination does not support ascending so result same as without pagination
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'rising', afterId: post_ids[1] })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 3);
    expect(res.body.questions[0]._id).to.equal(post_ids[1]);
    expect(res.body.questions[1]._id).to.equal(post_ids[2]);
    expect(res.body.questions[2]._id).to.equal(post_ids[0]);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: userInterest, sort: 'rising', afterId: post_ids[1] })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 3);
    expect(res.body.questions[0]._id).to.equal(post_ids[1]);
    expect(res.body.questions[1]._id).to.equal(post_ids[2]);
    expect(res.body.questions[2]._id).to.equal(post_ids[0]);

    // add comments to question 2
    for (let i = 0; i < 7; i++) {
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', i)
        .send({ questionId: post_ids[2], parentId: post_ids[2], text: `comment${i}` });
      expect(res.status).to.equal(200);
    }

    // question 2 excluded as score >10 now
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: userInterest, sort: 'rising' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 2);
    expect(res.body.questions[0]._id).to.equal(post_ids[1]);
    expect(res.body.questions[1]._id).to.equal(post_ids[0]);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'rising' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 2);
    expect(res.body.questions[0]._id).to.equal(post_ids[1]);
    expect(res.body.questions[1]._id).to.equal(post_ids[0]);

    // 2 days pass
    clock.tick(2 * 24 * 60 * 60 * 1000);

    res = await request(app)
      .post('/v1/worker/updateQuestionScores')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // older questions with score less than 10 should still appear
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: userInterest, sort: 'rising' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 2);
    expect(res.body.questions[0]._id).to.equal(post_ids[1]);
    expect(res.body.questions[1]._id).to.equal(post_ids[0]);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'rising' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    assert(res.body.questions.length == 2);
    expect(res.body.questions[0]._id).to.equal(post_ids[1]);
    expect(res.body.questions[1]._id).to.equal(post_ids[0]);
  } finally {
    clock.restore();
  }
});

it('search by text', async () => {
  // functions
  const checkResCode = (res, code) => expect(res.status).to.equal(code);
  const isOkReq = (res) => checkResCode(res, 200);

  const initAppReq = (id) => request(app)
    .put('/v1/user/initApp')
    .set('authorization', id);

  const postQ = (by, body) => request(app)
    .post('/v1/question')
    .set('authorization', by)
    .send({
      title: body.title,
      text: body.text,
      interestId: body.interestId,
    });

  const q_ids = [];
  const checkQs = (qDocArr, qArr) => {
    expect(qDocArr.length).to.equal(qArr.length);
    qDocArr.every(
      ({ _id }, i) => (expect(_id).to.equal(q_ids[qArr[i]])),
    );
  };

  // create user 0
  res = await initAppReq(0);
  isOkReq(res);

  // store interests for later requests
  const userInterest = res.body.interests[0]._id;
  const userInterest2 = res.body.interests[1]._id;

  // posting questions and storing ids

  // similar posts
  for (let i = 0; i < 2; i++) {
    res = await postQ(0, {
      title: `This is title num-${i}---`,
      text: `This is text num-${i}---`,
      interestId: userInterest,
    });
    isOkReq(res);
    q_ids.push(res.body._id);
  }

  // with different text containing non-english characters and punctuations
  res = await postQ(0, {
    title: 'This is Rand.... Русский日本語',
    text: 'This is Random',
    interestId: userInterest,
  });
  isOkReq(res);
  q_ids.push(res.body._id);

  // again with different text and punctuations
  res = await postQ(0, {
    title: 'This is Random....',
    text: 'This is ....Rand.....',
    interestId: userInterest,
  });
  isOkReq(res);
  q_ids.push(res.body._id);

  // feeds in correct order
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'recent' })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [3, 2, 1, 0]);

  // feeds in correct order
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ sort: 'recent', interestId: userInterest })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [3, 2, 1, 0]);

  // adding search parameter

  let str = 'a';
  for (let i = 0; i < 1000; i++) { str += 'a'; }

  // invalid input error on length >1000
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: str })
    .set('authorization', 0);
  checkResCode(res, 422);

  // empty search parameter filters nothing
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: '' })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [3, 2, 1, 0]);

  // feeds in correct order
  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({ sort: 'recent', interestId: userInterest, search: '' })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [3, 2, 1, 0]);

  // no match found
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'tit-le' })
    .set('authorization', 0);
  isOkReq(res);

  // duplicate values ignored
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'title Title' })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [1, 0]);

  // accepts non-english characters
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'Русский日本語' })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [2]);

  // should not include partial results
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'num-' })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, []);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({
      interestId: userInterest,
      search: 'num-',
    })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, []);

  // include only those containing all the words (also include words with punctuation in between)
  res = await request(app)
    .get('/v1/question/feed')
    .query({
      search: 'title num-1',
    })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [1]);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({
      interestId: userInterest,
      search: 'title num-1',
    })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [1]);

  // finds no posts
  res = await request(app)
    .get('/v1/question/feed')
    .query({
      search: 'num-0 num-1',
    })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, []);

  res = await request(app)
    .get('/v1/question/allQuestions')
    .query({
      interestId: userInterest,
      search: 'num-0 num-1',
    })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, []);

  // match include both text and title in search with punctuations ignored
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'rand' })
    .set('authorization', 0);
  isOkReq(res);
  checkQs(res.body.questions, [3, 2]);

  // supports all sort types
  for (const sortType in ['recent', 'popular', 'rising', 'nearby', 'top']) {
    res = await request(app)
      .get('/v1/question/feed')
      .query({ search: 'title', sort: sortType })
      .set('authorization', 0);
    isOkReq(res);
    checkQs(res.body.questions, [1, 0]);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestId: userInterest, search: 'title', sort: sortType })
      .set('authorization', 0);
    isOkReq(res);
    checkQs(res.body.questions, [1, 0]);
  }

  // create new user
  res = await initAppReq(1);
  isOkReq(res);

  // add interests
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 1)
    .send({
      interestIds: [userInterest2],
    });
  isOkReq(res);

  // post two questions with the followed interests
  res = await postQ(0, {
    title: 'This is title',
    text: 'This is text',
    interestId: userInterest2,
  });
  isOkReq(res);
  q_ids.push(res.body._id);

  res = await postQ(0, {
    title: 'This is titles',
    text: 'This is texts',
    interestId: userInterest2,
  });
  isOkReq(res);
  q_ids.push(res.body._id);

  // get followed interests
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'recent', filter: 'following' })
    .set('authorization', 1);

  isOkReq(res);
  checkQs(res.body.questions, [5, 4]);

  // search does not conflict with following filter
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'is title', sort: 'recent', filter: 'following' })
    .set('authorization', 1);
  isOkReq(res);
  checkQs(res.body.questions, [4]);

  // Edit previous post with new values
  res = await request(app)
    .patch('/v1/question/edit')
    .set('authorization', 0)
    .send({ questionId: q_ids[4], title: 'my newTitle', text: 'newText' });
  isOkReq(res);

  // should not have any results now for old search
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'title', sort: 'recent', filter: 'following' })
    .set('authorization', 1);
  isOkReq(res);
  assert(res.body.questions.length == 0);

  // should return for new search
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'my newtitle', sort: 'recent', filter: 'following' })
    .set('authorization', 1);
  isOkReq(res);
  checkQs(res.body.questions, [4]);

  // question of day should not appear in search results
  await createQuestion({
    createdAt: new Date(),
    text: 'qod 1',
    interestName: 'questions',
  });

  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'my newtitle', sort: 'recent', filter: 'following' })
    .set('authorization', 1);
  isOkReq(res);
  checkQs(res.body.questions, [4]);

  // search poll
  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'poll' })
    .set('authorization', 1);
  isOkReq(res);
  checkQs(res.body.questions, []);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'chess',
      title: 'title',
      poll: ['0', '1', 'poll'],
    });
  expect(res.status).to.equal(200);
  q_ids.push(res.body._id);

  await new Promise((r) => setTimeout(r, 100));

  res = await request(app)
    .get('/v1/question/feed')
    .query({ search: 'poll' })
    .set('authorization', 1);
  isOkReq(res);
  checkQs(res.body.questions, [6]);
});

it('interest num questions', async () => {
  async function checkNumQuestions(name, numQuestions) {
    const interest = await Interest.findOne({ name });
    expect(interest.numQuestions).to.equal(numQuestions);
  }

  await checkNumQuestions('kpop', 0);
  await checkNumQuestions('latin', 0);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // post questions
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);

  await checkNumQuestions('kpop', 1);
  await checkNumQuestions('latin', 0);

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'kpop' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numQuestions).to.equal(1);

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'latin' });
  expect(res.status).to.equal(200);
  expect(res.body.interest.numQuestions).to.equal(0);

  // test backfill
  const interest = await Interest.findOne({ name: 'kpop' });
  interest.numQuestions = 0;
  await interest.save();

  await checkNumQuestions('kpop', 0);
  await checkNumQuestions('latin', 0);

  await backfillInterestNumQuestions();

  await checkNumQuestions('kpop', 1);
  await checkNumQuestions('latin', 0);
});

it('interest num questions per language', async () => {
  async function checkNumQuestions(numQuestionsPerLanguage) {
    res = await request(app)
      .get('/web/interest')
      .query({ name: 'kpop' });
    expect(res.status).to.equal(200);
    expect(res.body.interest.numQuestionsPerLanguage).to.eql(numQuestionsPerLanguage);
  }

  await checkNumQuestions({});

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // post questions
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
      language: 'en',
    });
  expect(res.status).to.equal(200);

  await checkNumQuestions({en: 1});

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
      language: 'es',
    });
  expect(res.status).to.equal(200);

  await checkNumQuestions({en: 1, es: 1});

  // test backfill
  const interest = await Interest.findOne({ name: 'kpop' });
  interest.numQuestionsPerLanguage = {};
  await interest.save();

  await checkNumQuestions({});

  await backfillInterestNumQuestionsPerLanguage();

  await checkNumQuestions({en: 1, es: 1});
});

it('boost first three posts of a user', async () => {
  // create 8 users
  const numUsers = 9;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);
    // .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  user = await User.findOne({ _id: 0 });
  user.config.boost_first_three_posts = true;
  await user.save();

  // get interest for later queries
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  const userInterest = res.body.interests[0]._id;

  const post_ids = [];

  // post 5 questions by user 0
  for (let i = 0; i < 5; i++) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: userInterest,
        title: `title${i}`,
        text: `text${i}`,
      });
    expect(res.status).to.equal(200);
    post_ids.push(res.body._id);
  }

  // like all questions by user 1,2 and 3
  for (let i = 0; i < post_ids.length; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: post_ids[i] });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 2)
      .send({ questionId: post_ids[i] });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 3)
      .send({ questionId: post_ids[i] });
    expect(res.status).to.equal(200);
  }

  const firstThree = post_ids.slice(0, 3);
  const lastTwo = post_ids.slice(3, 5);

  // First three posts have more score than the rest
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'rising' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  assert(res.body.questions.length == post_ids.length);
  expect(res.body.questions[0]._id).oneOf(firstThree);
  expect(res.body.questions[1]._id).oneOf(firstThree);
  expect(res.body.questions[2]._id).oneOf(firstThree);
  expect(res.body.questions[3]._id).oneOf(lastTwo);
  expect(res.body.questions[4]._id).oneOf(lastTwo);

  // like last two questions by user 4
  for (let i = 0; i < lastTwo.length; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 4)
      .send({ questionId: lastTwo[i] });
    expect(res.status).to.equal(200);
  }

  // First three posts have more score than the rest
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'rising' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  assert(res.body.questions.length == post_ids.length);
  expect(res.body.questions[0]._id).oneOf(firstThree);
  expect(res.body.questions[1]._id).oneOf(firstThree);
  expect(res.body.questions[2]._id).oneOf(firstThree);
  expect(res.body.questions[3]._id).oneOf(lastTwo);
  expect(res.body.questions[4]._id).oneOf(lastTwo);

  // like last two questions by user 5, 6, 7 and 8
  for (let i = 0; i < lastTwo.length; i++) {
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 5)
      .send({ questionId: lastTwo[i] });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 6)
      .send({ questionId: lastTwo[i] });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 7)
      .send({ questionId: lastTwo[i] });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 7)
      .send({ questionId: lastTwo[i] });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 8)
      .send({ questionId: lastTwo[i] });
    expect(res.status).to.equal(200);
  }

  // now lastTwo have more score than firstThree so should appear first

  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'rising' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  assert(res.body.questions.length == post_ids.length);
  expect(res.body.questions[0]._id).oneOf(lastTwo);
  expect(res.body.questions[1]._id).oneOf(lastTwo);
  expect(res.body.questions[2]._id).oneOf(firstThree);
  expect(res.body.questions[3]._id).oneOf(firstThree);
  expect(res.body.questions[4]._id).oneOf(firstThree);
});
const sortOptions = [
  'popular',
  'rising',
  'recent',
  'topAllTime',
  'topYear',
  'topMonth',
  'topWeek',
];
sortOptions.forEach((sortOption) => {
  it(`pagination and banned posts: ${sortOption}`, async () => {
    sinon.stub(constants, 'getPageSize').returns(2);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    // post questions
    const ids = [];
    for (let i = 0; i < constants.getPageSize() * 3; i++) {
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 0)
        .send({
          interestName: 'kpop',
          title: i.toString(),
        });
      expect(res.status).to.equal(200);
      ids.push(res.body._id);
    }

    // ban all except first and last
    let beforeId;
    let sortedQuestions = [];
    while (true) {
      res = await request(app)
        .get('/v1/question/feed')
        .query({ sort: sortOption, beforeId })
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      if (res.body.questions.length) {
        sortedQuestions = sortedQuestions.concat(res.body.questions);
        beforeId = sortedQuestions[sortedQuestions.length - 1]._id;
      } else {
        break;
      }
    }
    expect(sortedQuestions.length).to.equal(ids.length);
    const firstId = sortedQuestions[0]._id;
    const lastId = sortedQuestions[sortedQuestions.length - 1]._id;

    for (const id of ids) {
      if (id != firstId && id != lastId) {
        res = await request(app)
          .put('/v1/admin/banQuestion')
          .set('authorization', 0)
          .send({
            questionId: id,
          });
        expect(res.status).to.equal(200);
      }
    }

    // get feed - should see first and last in separate pages
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: sortOption })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(firstId);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: sortOption, beforeId: firstId })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(lastId);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: sortOption, beforeId: lastId })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });
});

describe('replace incorrect interest', async () => {
  beforeEach(async () => {
    // add incorrect interest to the database
    const interests = [
      {
        category: 'Incorrect', interest: '#relationshipadivce', name: 'relationshipadivce', sortIndex: 1.1,
      },
      {
        category: 'Correct', interest: '#relationshipadvice', name: 'relationshipadvice', sortIndex: 1.2,
      },
    ];
    await Interest.insertMany(interests);
    await interestLib.loadInterestsFromDatabase();

    // create new user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
  });

  it('basic functionality', async () => {
    // user adds the incorrect interest
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['relationshipadivce'],
      });
    expect(res.status).to.equal(200);

    // user adds the incorrect interest to preferences
    res = await request(app)
      .patch('/v1/user/preferences/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['relationshipadivce'],
      });

    // add to hidden interests
    res = await request(app)
      .put('/v1/user/hiddenInterests')
      .set('authorization', 0)
      .send({
        interestNames: ['relationshipadivce'],
      });
    expect(res.status).to.equal(200);

    // posts two questions to the interest
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'relationshipadivce',
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    const qId = res.body._id;

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'relationshipadivce',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const qId2 = res.body._id;

    await interestLib.replaceIncorrectInterest('relationshipadivce', 'relationshipadvice');

    // status of incorrect interest set to rejected
    expect(await Interest.findOne({ name: 'relationshipadivce', status: 'rejected' }, { _id: 1 })).to.not.eql(null);

    // incorrect interest replaced by correct interest
    const correctInterestId = (await Interest.findOne({ name: 'relationshipadvice' }))._id.toString();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const userData = res.body.user;
    expect(userData.interests.length).to.equal(1);
    expect(userData.interests[0]._id).to.equal(correctInterestId);
    expect(userData.interests[0].name).to.equal('relationshipadvice');
    expect(userData.interestNames).to.eql(['relationshipadvice']);
    expect(userData.hiddenInterests).to.eql(['relationshipadvice']);
    expect(userData.preferences.interests).to.eql([correctInterestId]);
    expect(userData.preferences.interestNames).to.eql(['relationshipadvice']);

    // metrics for correct interest updated
    const interestDoc = await Interest.findOne({ name: 'relationshipadvice' });
    expect(interestDoc.numQuestions).to.eql(2);
    expect(interestDoc.numFollowers).to.eql(1);

    // get question data
    res = await request(app)
      .get('/v1/question/')
      .set('authorization', 0)
      .query({ questionId: qId });
    expect(res.status).to.equal(200);

    expect(res.body.question.interestName).to.eql('relationshipadvice');
    expect(res.body.question.interest.name).to.eql('relationshipadvice');

    res = await request(app)
      .get('/v1/question/')
      .set('authorization', 0)
      .query({ questionId: qId2 });
    expect(res.status).to.equal(200);

    expect(res.body.question.interestName).to.eql('relationshipadvice');
    expect(res.body.question.interest.name).to.eql('relationshipadvice');

    // user cannot the incorrect interest now
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['relationshipadivce'],
      });
    expect(res.status).to.equal(422);

    // user cannot resubmit the incorrect interest
    res = await request(app)
      .post('/v1/interest')
      .set('authorization', 0)
      .send({ name: 'relationshipadivce' });
    expect(res.status).to.equal(422);

    // user cannot post to the incorrect interest
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'relationshipadivce',
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(422);
  });

  it('profile has only incorrect', async () => {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['kpop', 'relationshipadivce', 'chess'],
      });
    expect(res.status).to.equal(200);

    await interestLib.replaceIncorrectInterest('relationshipadivce', 'relationshipadvice');

    // position in array should be preserved
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.interestNames).to.eql(['kpop', 'relationshipadvice', 'chess']);

    const interestDoc = await Interest.findOne({ name: 'relationshipadvice' });
    expect(interestDoc.numFollowers).to.eql(1);

    // set up redirect
    constants.getReplacedInterests.restore();
    sinon.stub(constants, 'getReplacedInterests').returns([
      ['relationshipadivce', 'relationshipadvice'],
    ]);

    // user tries to modify profile using cached interests
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['kpop', 'relationshipadivce', 'chess', 'latin'],
      });
    expect(res.status).to.equal(200);

    // incorrect should be converted to correct
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.interestNames).to.eql(['kpop', 'relationshipadvice', 'chess', 'latin']);

    // user tries to post using cached interest
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'relationshipadivce',
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    const qId = res.body._id;

    // incorrect should be converted to correct
    res = await request(app)
      .get('/v1/question/')
      .set('authorization', 0)
      .query({ questionId: qId });
    expect(res.status).to.equal(200);
    expect(res.body.question.interestName).to.eql('relationshipadvice');
  });

  it('profile has both incorrect and correct', async () => {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['kpop', 'relationshipadivce', 'chess', 'relationshipadvice'],
      });
    expect(res.status).to.equal(200);

    await interestLib.replaceIncorrectInterest('relationshipadivce', 'relationshipadvice');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const userData = res.body.user;
    expect(userData.interestNames).to.eql(['kpop', 'chess', 'relationshipadvice']);

    const interestDoc = await Interest.findOne({ name: 'relationshipadvice' });
    expect(interestDoc.numFollowers).to.eql(1);

    // set up redirect
    constants.getReplacedInterests.restore();
    sinon.stub(constants, 'getReplacedInterests').returns([
      ['relationshipadivce', 'relationshipadvice'],
    ]);

    // user tries to modify profile using cached interests
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['kpop', 'relationshipadivce', 'chess', 'relationshipadvice', 'latin'],
      });
    expect(res.status).to.equal(200);

    // incorrect should be converted to correct
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.interestNames).to.eql(['kpop', 'relationshipadvice', 'chess', 'latin']);
  });

  it('profile has neither', async () => {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({
        interestNames: ['kpop'],
      });
    expect(res.status).to.equal(200);

    await interestLib.replaceIncorrectInterest('relationshipadivce', 'relationshipadvice');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const userData = res.body.user;
    expect(userData.interestNames).to.eql(['kpop']);

    const interestDoc = await Interest.findOne({ name: 'relationshipadvice' });
    expect(interestDoc.numFollowers).to.eql(0);
  });
});

it('filter out future qod', async () => {
  // stub page size to 1 (web route will double page size to 2)
  sinon.stub(constants, 'getPageSize').returns(1);

  // create older qods
  newQuestion = await createQuestion({
    createdAt: new Date(2020, 5, 20, 4, 5, 0, 0),
    text: 'qod 1',
    interestName: 'questions',
    score: 0.9,
  });
  await newQuestion.save();

  newQuestion = await createQuestion({
    createdAt: new Date(2020, 5, 20, 4, 5, 0, 0),
    text: 'qod 2',
    interestName: 'questions',
    score: 0.1,
  });
  await newQuestion.save();

  // create future qod with score in between the other qod
  newQuestion = await createQuestion({
    createdAt: new Date(9999, 5, 20, 4, 5, 0, 0),
    text: 'qod 3',
    interestName: 'questions',
    score: 0.5,
  });
  await newQuestion.save();

  // getting qod should return both older qod in first page
  res = await request(app)
    .get('/web/question/allQuestions')
    .query({ interestName: 'questions', sort: 'popular' });
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0].text).to.equal('qod 1');
  expect(res.body.questions[1].text).to.equal('qod 2');
});

describe('karma award for early comments', async () => {
  let q1Id; let
    initialKarma;

  beforeEach(async () => {
    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.11.73' });
      expect(res.status).to.equal(200);
    }

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;
  });

  it('OP posts first comment', async () => {
    // user 0 posts first comment and gets 0 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);

    // user 1 posts 2nd comment and gets 1 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'comment2',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);
  });

  it('other user posts first comment', async () => {
    // user 1 posts first comment and gets 2 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 2);

    // user 0 posts 2nd comment and gets 0 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment2',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);
  });

  it('no more karma award after 10 comments', async () => {
    for (let i = 0; i < 10; i++) {
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', 1)
        .send({
          questionId: q1Id,
          text: 'comment1',
          parentId: q1Id,
        });
      expect(res.status).to.equal(200);
    }

    // no karma award for posting 11th comment
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'comment11',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);
  });

  it('daily limit on first comment karma award', async () => {
    // user already reached daily limit
    user = await User.findById('1');
    user.currentDayMetrics.karmaFromLikingPosts = 20;
    await user.save();

    // user 1 posts first comment and gets 0 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);
  });

  it('first like on post', async () => {
    // user 1 likes post and gets 1 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    // user 2 likes post and gets 0 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 2)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);
  });

  it('question first like karma award - like + unlike + like', async () => {
    // user 1 likes post and gets 1 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    // user 1 unlikes
    res = await request(app)
      .patch('/v1/question/unlike')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    // user 1 likes post again and gets 0 karma
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    user = await User.findOne({ _id: 1 });
    expect(user.metrics.numPostLikesSent).to.equal(1);
    expect(user.metrics.numPostUnlikesSent).to.equal(1);
  });

  it('daily limit on post like karma award', async () => {
    // user already reached daily limit
    user = await User.findById('1');
    user.currentDayMetrics.karmaFromLikingPosts = 20;
    await user.save();

    // user 1 likes post and gets 0 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);

    // reset current day
    user = await User.findById('1');
    user.metrics.currentDayResetTime = new Date();
    await user.save();

    // user 1 unlikes
    res = await request(app)
      .patch('/v1/question/unlike')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    // user 1 likes post again and gets 1 karma
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    user = await User.findOne({ _id: 1 });
    expect(user.metrics.karmaFromLikingPosts).to.equal(1);
    expect(user.currentDayMetrics.karmaFromLikingPosts).to.equal(1);
  });

  it('daily limit on post like karma award - per partner', async () => {
    // user 0 posts several questions
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    q2Id = res.body._id;

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title3',
        text: 'text3',
      });
    expect(res.status).to.equal(200);
    q3Id = res.body._id;

    // user 1 likes q1 and gets 1 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    // user 1 likes q2 and gets 0 karma
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: q2Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    // reset current day
    user = await User.findById('1');
    user.metrics.currentDayResetTime = new Date();
    await user.save();

    // user 1 likes q3 and gets 1 karma
    res = await request(app)
      .patch('/v1/question/like')
      .set('authorization', 1)
      .send({ questionId: q3Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 2);
  });

  it('first like on comment', async () => {

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;

    // user 1 likes comment and gets 1 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    // user 2 likes comment and gets 0 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 2)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);
  });

  it('comment first like karma award - like + unlike + like', async () => {

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;

    // user 1 likes comment and gets 1 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    // user 1 unlikes
    res = await request(app)
      .patch('/v1/comment/unlike')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    // user 1 likes again and gets 0 karma
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    user = await User.findOne({ _id: 1 });
    expect(user.metrics.numPostLikesSent).to.equal(1);
    expect(user.metrics.numPostUnlikesSent).to.equal(1);
  });

  it('first reply to comment', async () => {

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;

    // user 1 replies to comment and gets 1 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma + 1);

    // user 2 replies to comment and gets 0 karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    initialKarma = res.body.user.karma;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        text: 'comment1',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);
  });
});

describe('notify matches on new post', async () => {
  beforeEach(async () => {
    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.11.58' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', i)
        .send({ firstName: `name${i.toString()}` });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', i)
        .send({ fcmToken: `token${i.toString()}` });
      expect(res.status).to.equal(200);
    }
  });

  it('0 match', async () => {
    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // no notification sent
    expect(notifs.numSent).to.equal(0);
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(0);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });

  it('1 pending like', async () => {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // no notification sent
    expect(notifs.numSent).to.equal(0);
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(0);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });

  it('1 match without fcmToken', async () => {
    // user 0 and user 1 matched
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    user = await User.findOne({ _id: 1 });
    user.fcmToken = undefined;
    await user.save();

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // no notification sent
    expect(notifs.numSent).to.equal(0);
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(0);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });

  it('1 match with null fcmToken', async () => {
    // user 0 and user 1 matched
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    user = await User.findOne({ _id: 1 });
    user.fcmToken = null;
    await user.save();

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // no notification sent
    expect(notifs.numSent).to.equal(0);
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(0);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });

  it('1 match - push notification disabled', async () => {
    // user 0 and user 1 matched
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    // disable push notification
    res = await request(app)
      .put('/v1/user/notificationSettings')
      .set('authorization', 1)
      .send({
        pushNotificationSettings: {
          friendPosts: false,
        },
      });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // notification not sent to user 1
    expect(notifs.numSent).to.equal(0);
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(0);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });

  it('1 match', async () => {
    // user 0 and user 1 matched
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // notification sent to user 1
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.notification.title).to.equal('name0 posted');
    expect(notifs.recent.notification.body).to.equal('title');
    expect(notifs.recent.token).to.eql('token1');
    expect(JSON.parse(notifs.recent.data.question)).to.eql({ _id: res.body._id, interestName: 'kpop' });
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(1);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });

  it('2 match', async () => {
    // user 0 matched with user 1 and user 2
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // notification sent to user 1 and user 2
    expect(notifs.numSent).to.equal(2);
    expect(notifs.recent.notification.title).to.equal('name0 posted');
    expect(notifs.recent.notification.body).to.equal('title');
    const tokens = notifs.recentArray.map(notif => notif.token);
    expect(tokens).to.include('token1');
    expect(tokens).to.include('token2');
    expect(JSON.parse(notifs.recent.data.question)).to.eql({ _id: res.body._id, interestName: 'kpop' });
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(1);
    expect((await User.findById('2')).events.friendPosted).to.equal(1);
  });

  it('translated notification', async () => {
    // user 0 matched with user 1 and user 2
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ locale: 'es' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .send({ user: '0' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ locale: 'es' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // translated notification sent to user 1 and user 2
    expect(notifs.numSent).to.equal(2);
    expect(notifs.recent.notification.title).to.equal('name0 publicó');
    expect(notifs.recent.notification.body).to.equal('title');
    const tokens = notifs.recentArray.map(notif => notif.token);
    expect(tokens).to.include('token1');
    expect(tokens).to.include('token2');
  });

  it('question is banned', async () => {
    // user 0 and user 1 matched
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    // user 0 posts banned question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'censor',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // no notifications sent
    expect(notifs.numSent).to.equal(0);
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(0);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });

  it('user is banned', async () => {
    // user 0 and user 1 matched
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    // user 0 is banned
    user = await User.findOne({ _id: 0 });
    user.shadowBanned = true;
    await user.save();

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // no notifications sent
    expect(notifs.numSent).to.equal(0);
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(0);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });

  it('user unmatched', async () => {
    // user 0 and user 1 matched
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    // user unmatches
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    // user 0 posts question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'title',
        text: 'text',
      });
    expect(res.status).to.equal(200);

    // no notifications sent
    expect(notifs.numSent).to.equal(0);
    expect((await User.findById('0')).events.friendPosted).to.equal(0);
    expect((await User.findById('1')).events.friendPosted).to.equal(0);
    expect((await User.findById('2')).events.friendPosted).to.equal(0);
  });
});

it('get users in interest group', async () => {
  // stub page size to 1
  sinon.stub(constants, 'getPageSize').returns(1);

  // create user 0 and user 1
  for (let i = 0; i < 2; i++) {
    await createUser(i);
  }

  // no users in kpop
  res = await request(app)
    .get('/v1/interest/users')
    .set('authorization', 0)
    .query({ interestName: 'kpop' })
  expect(res.status).to.equal(200);
  expect(res.body.users.length).to.equal(0);

  // user 0 joins kpop
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 0)
    .send({
      interestNames: ['kpop'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/interest/users')
    .set('authorization', 0)
    .query({ interestName: 'kpop' })
  expect(res.status).to.equal(200);
  expect(res.body.users.length).to.equal(1);
  expect(res.body.users[0]).to.eql(getProfilePreview(0));

  res = await request(app)
    .get('/v1/interest/users')
    .set('authorization', 0)
    .query({ interestName: 'kpop', beforeId: '0' })
  expect(res.status).to.equal(200);
  expect(res.body.users.length).to.equal(0);

  // user 1 joins kpop
  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 1)
    .send({
      interestNames: ['kpop'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/interest/users')
    .set('authorization', 0)
    .query({ interestName: 'kpop' })
  expect(res.status).to.equal(200);
  expect(res.body.users.length).to.equal(1);
  expect(res.body.users[0]._id).to.equal('1');

  res = await request(app)
    .get('/v1/interest/users')
    .set('authorization', 0)
    .query({ interestName: 'kpop', beforeId: '1' })
  expect(res.status).to.equal(200);
  expect(res.body.users.length).to.equal(1);
  expect(res.body.users[0]._id).to.equal('0');

  res = await request(app)
    .get('/v1/interest/users')
    .set('authorization', 0)
    .query({ interestName: 'kpop', beforeId: '0' })
  expect(res.status).to.equal(200);
  expect(res.body.users.length).to.equal(0);
});

it('linked keywords', async () => {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  // create rejected interest
  sinon.stub(interestLib, 'shouldInterestBeApproved')
    .callsFake((params) => {
      const impl = function (resolve, reject) {
        resolve(false);
      };
      return new Promise(impl);
    });

  res = await request(app)
    .post('/v1/interest')
    .set('authorization', 0)
    .send({ name: 'like' });
  expect(res.status).to.equal(200);

  // post question and comment
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: `I like latin music`,
      text: `and also rap music. I am ENFJ MBTI.`,
    });
  expect(res.status).to.equal(200);

  await new Promise((r) => setTimeout(r, 100));

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0].linkedKeywords).to.eql([]);
  expect(res.body.questions[0].linkedPillarKeywords).to.eql([
    {
      keyword: 'music',
      url: '/database/musicians',
    },
    {
      keyword: 'ENFJ',
      url: '/enfj-personality',
    },
    {
      keyword: 'MBTI',
      url: '/u/mbti',
    }
  ]);
  const q1Id = res.body.questions[0]._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: 'Me too I like latin music, but I am scorpio',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedKeywords).to.eql([]);
  expect(res.body.comments[0].linkedPillarKeywords).to.eql([
    {
      keyword: 'scorpio',
      url: '/database/scorpios',
    },
    {
      keyword: 'music',
      url: '/database/musicians',
    },
  ]);

  res = await request(app)
    .get('/v1/interest')
    .query({ name: 'latin' })
    .set('authorization', 0)
  expect(res.status).to.equal(200)
  expect(res.body.interest.numQuestions).to.equal(0);
;

  for(let i = 0; i < 5; i++) {
    res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'latin',
      title: `I like latin music type ${i}`,
      text: `and also rap music. I am ENFJ MBTI.`,
    });
  }


  res = await request(app)
    .get('/v1/interest')
    .query({ name: 'latin' })
    .set('authorization', 0)
  expect(res.status).to.equal(200)
  expect(res.body.interest.numQuestions).to.equal(5);

  await new Promise((r) => setTimeout(r, 100));

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(6);
  expect(res.body.questions[0].linkedKeywords).to.eql(['latin']);
  expect(res.body.questions[0].linkedPillarKeywords).to.eql([
    {
      keyword: 'music',
      url: '/database/musicians',
    },
    {
      keyword: 'ENFJ',
      url: '/enfj-personality',
    },
    {
      keyword: 'MBTI',
      url: '/u/mbti',
    }
  ]);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedKeywords).to.eql(['latin']);
  expect(res.body.comments[0].linkedPillarKeywords).to.eql([
    {
      keyword: 'scorpio',
      url: '/database/scorpios',
    },
    {
      keyword: 'music',
      url: '/database/musicians',
    },
  ]);

  // create new interest
  interestLib.shouldInterestBeApproved.restore();
  sinon.stub(interestLib, 'shouldInterestBeApproved')
    .callsFake((params) => {
      const impl = function (resolve, reject) {
        resolve(true);
      };
      return new Promise(impl);
    });

  res = await request(app)
    .post('/v1/interest')
    .set('authorization', 0)
    .send({ name: 'music' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/interest')
    .query({ name: 'music' })
    .set('authorization', 0)
  expect(res.status).to.equal(200)
  expect(res.body.interest.numQuestions).to.equal(0);

  // linked keywords should not include new interest as interest music has numQuestions less than 5
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(6);
  expect(res.body.questions[0].linkedKeywords).to.eql(['latin']);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedKeywords).to.eql(['latin']);

  // test backfill
  await Question.updateMany({}, { $set: { linkedKeywords: [], linkedPillarKeywords: [] } });
  await Comment.updateMany({}, { $set: { linkedKeywords: [], linkedPillarKeywords: [] } });

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(6);
  expect(res.body.questions[0].linkedKeywords).to.eql([]);
  expect(res.body.questions[0].linkedPillarKeywords).to.eql([]);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedKeywords).to.eql([]);
  expect(res.body.comments[0].linkedPillarKeywords).to.eql([]);

  await backfillLinkedKeywords();
  await backfillLinkedPillarKeywords();

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(6);
  expect(res.body.questions[0].linkedKeywords).to.eql(['latin']);
  expect(res.body.questions[0].linkedPillarKeywords).to.eql([
    {
      keyword: 'music',
      url: '/database/musicians',
    },
    {
      keyword: 'ENFJ',
      url: '/enfj-personality',
    },
    {
      keyword: 'MBTI',
      url: '/u/mbti',
    }
  ]);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedKeywords).to.eql(['latin']);
  expect(res.body.comments[0].linkedPillarKeywords).to.eql([
    {
      keyword: 'scorpio',
      url: '/database/scorpios',
    },
    {
      keyword: 'music',
      url: '/database/musicians',
    },
  ]);

  // remove interest
  await interestLib.replaceIncorrectInterest('latin', 'kpop');
  //after latin is renamed to kpop then the questions will not have keyword latin so there wont be any linkedKeywords

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(6);
  expect(res.body.questions[0].linkedKeywords).to.eql([]);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedKeywords).to.eql([]);

    // rename interest
    await interestLib.replaceIncorrectInterest('kpop', 'music');
    //after kpop is renamed to music then the question will have keyword music and numQuestions greater than 5

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(6);
    expect(res.body.questions[0].linkedKeywords).to.eql(['music']);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].linkedKeywords).to.eql(['music']);
});

it('linked explore keywords', async () => {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  // post question and comment
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: `I like black cat`,
      text: `I LIKE BLACK CAT.`,
    });
  expect(res.status).to.equal(200);

  await new Promise((r) => setTimeout(r, 100));

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0].linkedExploreKeywords).to.eql(['black', 'cat', 'black-cat']);

  const q1Id = res.body.questions[0]._id;
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: 'I like black cat',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedExploreKeywords).to.eql(['black', 'cat', 'black-cat']);

  // test backfill
  await Question.updateMany({}, { $set: { linkedExploreKeywords: [] } });
  await Comment.updateMany({}, { $set: { linkedExploreKeywords: [] } });

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0].linkedExploreKeywords).to.eql([]);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedExploreKeywords).to.eql([]);

  await backfillLinkedExploreKeywords();

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0].linkedExploreKeywords).to.eql(['black', 'cat', 'black-cat']);

  res = await request(app)
    .get('/v1/comment')
    .query({ parentId: q1Id })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].linkedExploreKeywords).to.eql(['black', 'cat', 'black-cat']);
});

describe('mentioned users', async () => {

  beforeEach(async () => {
    for (let i = 0; i < 3; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', i)
        .send({ firstName: 'name' + i.toString() });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', i)
        .send({ fcmToken: 'token' + i.toString() });
      expect(res.status).to.equal(200);
    }
  });

  it('basic functionality', async () => {
    // mention in question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'Hi @name1',
        text: '@name1 is the best',
        mentionedUsersTitle: [ { _id: '1', firstName: 'name1' } ],
        mentionedUsersText: [ { _id: '1', firstName: 'name1' } ],
      });
    expect(res.status).to.equal(200);
    expect(res.body.mentionedUsersTitle).to.eql([ { _id: '1', firstName: 'name1' } ]);
    expect(res.body.mentionedUsersText).to.eql([ { _id: '1', firstName: 'name1' } ]);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].mentionedUsersTitle).to.eql([ { _id: '1', firstName: 'name1' } ]);
    expect(res.body.questions[0].mentionedUsersText).to.eql([ { _id: '1', firstName: 'name1' } ]);
    const q1Id = res.body.questions[0]._id;

    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token1');
    expect(notifs.recent.notification.title).to.equal('name0 mentioned you in a post');
    expect(notifs.recent.notification.body).to.equal('Hi @name1');
    expect(JSON.parse(notifs.recent.data.question)).to.eql({
      _id: q1Id,
      interestName: 'kpop',
    });
    reset();

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(1);
    expect(res.body.notifications[0].postType).to.equal('question');
    expect(res.body.notifications[0].notificationType).to.equal('mention');
    expect(res.body.notifications[0].seen).to.equal(false);
    expect(res.body.notifications[0].profile._id).to.equal('0');
    expect(res.body.notifications[0].numProfiles).to.equal(1);
    data = JSON.parse(res.body.notifications[0].data.question);
    expect(data._id).to.equal(q1Id);

    // edit post
    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        title: 'Hi @name1 and @name2',
        text: 'none',
        mentionedUsersTitle: [ { _id: '1', firstName: 'name1' }, { _id: '2', firstName: 'name2' } ],
        mentionedUsersText: [],
      })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].mentionedUsersTitle).to.eql([ { _id: '1', firstName: 'name1' }, { _id: '2', firstName: 'name2' } ]);
    expect(res.body.questions[0].mentionedUsersText).to.eql([]);

    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token2');
    expect(notifs.recent.notification.title).to.equal('name0 mentioned you in a post');
    expect(notifs.recent.notification.body).to.equal('Hi @name1 and @name2');
    expect(JSON.parse(notifs.recent.data.question)).to.eql({
      _id: q1Id,
      interestName: 'kpop',
    });
    reset();

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(1);
    expect(res.body.notifications[0].postType).to.equal('question');
    expect(res.body.notifications[0].notificationType).to.equal('mention');
    expect(res.body.notifications[0].seen).to.equal(false);
    expect(res.body.notifications[0].profile._id).to.equal('0');
    expect(res.body.notifications[0].numProfiles).to.equal(1);
    data = JSON.parse(res.body.notifications[0].data.question);
    expect(data._id).to.equal(q1Id);

    console.log((await Notification.find()))
    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(1);
    expect(res.body.notifications[0].postType).to.equal('question');
    expect(res.body.notifications[0].notificationType).to.equal('mention');
    expect(res.body.notifications[0].seen).to.equal(false);
    expect(res.body.notifications[0].profile._id).to.equal('0');
    expect(res.body.notifications[0].numProfiles).to.equal(1);
    data = JSON.parse(res.body.notifications[0].data.question);
    expect(data._id).to.equal(q1Id);

    // mention in comment
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        parentId: q1Id,
        text: 'hi hi @name1',
        mentionedUsersText: [ { _id: '1', firstName: 'name1' } ],
      });
    expect(res.status).to.equal(200);
    expect(res.body.mentionedUsersText).to.eql([ { _id: '1', firstName: 'name1' } ]);
    const c1Id = res.body._id;

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].mentionedUsersText).to.eql([ { _id: '1', firstName: 'name1' } ]);

    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token1');
    expect(notifs.recent.notification.title).to.equal('name0 mentioned you in a comment');
    expect(notifs.recent.notification.body).to.equal('hi hi @name1');
    data = JSON.parse(notifs.recent.data.comment);
    expect(data._id).to.equal(c1Id);
    reset();

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(2);
    expect(res.body.notifications[0].postType).to.equal('comment');
    expect(res.body.notifications[0].notificationType).to.equal('mention');
    expect(res.body.notifications[0].seen).to.equal(false);
    expect(res.body.notifications[0].profile._id).to.equal('0');
    expect(res.body.notifications[0].numProfiles).to.equal(1);
    data = JSON.parse(res.body.notifications[0].data.comment);
    expect(data._id).to.equal(c1Id);

    // edit comment
    res = await request(app)
      .patch('/v1/comment/edit')
      .set('authorization', 0)
      .send({
        commentId: c1Id,
        text: 'hi hi @name1 and @name2',
        mentionedUsersText: [ { _id: '1', firstName: 'name1' }, { _id: '2', firstName: 'name2' } ],
      })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].mentionedUsersText).to.eql([ { _id: '1', firstName: 'name1' }, { _id: '2', firstName: 'name2' } ]);

    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token2');
    expect(notifs.recent.notification.title).to.equal('name0 mentioned you in a comment');
    expect(notifs.recent.notification.body).to.equal('hi hi @name1 and @name2');
    data = JSON.parse(notifs.recent.data.comment);
    expect(data._id).to.equal(c1Id);
    reset();

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(2);
    expect(res.body.notifications[0].postType).to.equal('comment');
    expect(res.body.notifications[0].notificationType).to.equal('mention');
    expect(res.body.notifications[0].seen).to.equal(false);
    expect(res.body.notifications[0].profile._id).to.equal('0');
    expect(res.body.notifications[0].numProfiles).to.equal(1);
    data = JSON.parse(res.body.notifications[0].data.comment);
    expect(data._id).to.equal(c1Id);

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications.length).to.equal(2);
    expect(res.body.notifications[0].postType).to.equal('comment');
    expect(res.body.notifications[0].notificationType).to.equal('mention');
    expect(res.body.notifications[0].seen).to.equal(false);
    expect(res.body.notifications[0].profile._id).to.equal('0');
    expect(res.body.notifications[0].numProfiles).to.equal(1);
    data = JSON.parse(res.body.notifications[0].data.comment);
    expect(data._id).to.equal(c1Id);
    let notificationId = res.body.notifications[0]._id;

    // other user cannot mark notification seen
    res = await request(app)
      .put('/v1/notification/seen')
      .set('authorization', 0)
      .send({notificationId})
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications[0].seen).to.equal(false);

    // mark notification seen
    res = await request(app)
      .put('/v1/notification/seen')
      .set('authorization', 1)
      .send({notificationId})
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/notification')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.notifications[0].seen).to.equal(true);
  });

  it('mention non-existent user', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'Hi @name1',
        mentionedUsersTitle: [ { _id: 'invalid', firstName: 'name1' } ],
      });
    expect(res.status).to.equal(200);
    expect(res.body.mentionedUsersTitle).to.eql([]);
    expect(res.body.mentionedUsersText).to.eql();
  });
});

describe('hashtags', async () => {

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
  });

  it('basic functionality', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'Hi',
        text: 'chess is the best',
        hashtags: ['chess'],
      });
    expect(res.status).to.equal(200);
    expect(res.body.hashtags).to.eql(['kpop', 'chess']);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].hashtags).to.eql(['kpop', 'chess']);

    // question should be visible in both kpop and chess
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].hashtags).to.eql(['kpop', 'chess']);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'chess' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].hashtags).to.eql(['kpop', 'chess']);
    console.log('question : ', res.body.questions[0])
    const questionId = res.body.questions[0]._id

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'latin' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    //edit remove chess from hashtags
    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: questionId,
        title: 'Hi',
        text: 'chess is the best edit',
        hashtags: ['kpop']
      })
    expect(res.status).to.equal(200);

    //should not return when filter interestName: 'chess'
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'chess' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    //should return when filter interestName: 'kpop'
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].hashtags).to.eql(['kpop']);

    //edit put back chess as hashtags
    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: questionId,
        title: 'Hi',
        text: 'chess is the best edit',
        hashtags: ['kpop','chess']
      })
    expect(res.status).to.equal(200);

    //should return when filter interestName: 'chess'
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'chess' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    //should return when filter interestName: 'kpop'
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].hashtags).to.eql(['kpop', 'chess']);


    // edit remove all hashtags
    res = await request(app)
      .patch('/v1/question/edit')
      .set('authorization', 0)
      .send({
        questionId: questionId,
        title: 'Hi',
        text: 'chess is the best',
        hashtags: []
      })
    expect(res.status).to.equal(200);

    //should not return when filter interestName: 'chess'
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'chess' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    //should return when filter interestName: 'kpop'
    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].hashtags).to.eql(['kpop']);
    console.log('question feed : ', res.body.questions)
  });

  it('invalid input', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'Hi',
        text: 'chess is the best',
        hashtags: ['invalid'],
      });
    expect(res.status).to.equal(200);
    expect(res.body.hashtags).to.eql(['kpop']);
  });

  it('backfill', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'Hi',
        text: 'chess is the best',
      });
    expect(res.status).to.equal(200);
    expect(res.body.hashtags).to.eql(['kpop']);

    await Question.updateMany({}, { $unset: { hashtags: 1 } });

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].hashtags).to.eql();

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    await backfillQuestionHashtags();

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].hashtags).to.eql(['kpop']);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });
});

describe('video feed', async () => {

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
  });

  it('basic functionality', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '1',
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    res = await request(app)
      .post('/v1/question/video')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('video', validVideoPath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '2',
      });
    expect(res.status).to.equal(200);
    q2Id = res.body._id;

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ videosOnly: true })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    res = await request(app)
      .get('/v1/question/allQuestions')
      .query({ interestName: 'kpop', videosOnly: true })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
  });

  it('no qod', async () => {
    // load question of day
    await createQuestion({
      createdAt: new Date(),
      text: 'qod 1',
      interestName: 'questions',
    });

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'explore', videosOnly: true })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });
});

it('hide from keywords', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', '0')
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', '1')
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', '2')
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 2)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.eql(3);

  res = await request(app)
    .put(`/v1/user/description`)
    .set('authorization', 0)
    .send({
      description: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put(`/v1/user/hideFromKeywords`)
    .set('authorization', 0)
    .send({
      hideFromKeywords: ['0'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put(`/v1/user/hideFromKeywords`)
    .set('authorization', 1)
    .send({
      hideFromKeywords: ['0'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put(`/v1/user/hideFromKeywords`)
    .set('authorization', 2)
    .send({
      hideFromKeywords: ['1'],
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0].createdBy._id).to.not.equal('1');
  expect(res.body.questions[1].createdBy._id).to.not.equal('1');

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0].createdBy._id).to.not.equal('0');
  expect(res.body.questions[1].createdBy._id).to.not.equal('0');
});

it('hide from nearby', async () => {

  for (let i = 0; i < 3; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i)
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', i)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', i)
      .send({
        interestName: 'kpop',
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.eql(3);

  res = await request(app)
    .put(`/v1/user/hideFromNearby`)
    .set('authorization', 1)
    .send({
      hideFromNearby: true,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);
  expect(res.body.questions[0].createdBy._id).to.not.equal('1');
  expect(res.body.questions[1].createdBy._id).to.not.equal('1');

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0].createdBy._id).to.equal('1');
});

it('posts without createdBy should be filtered out', async () => {
  const numUsers = 2;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid)
      .send({ appVersion: '1.10.21' });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // remove user 1's account data
  await User.deleteOne({_id: '1'});

  // feed should be empty
  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(0);
});

describe('for you algo', async () => {
  beforeEach(async () => {
    sinon.stub(constants, 'getPageSize').returns(1);

    const numUsers = 4;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }

    // user 0 - US, male looking for female friends, interest kpop
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', 0)
      .send({ interestNames: ['kpop'] });
    expect(res.status).to.equal(200);
  });

  it('gender preference match', async () => {
    // user 1 - female looking for female friends
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 0 - does not see q1
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log('questions : ', res.body.questions)
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'recent', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // change gender preferences
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'recent', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('age preference match', async () => {
    // user 0: age 30, minAge 25, maxAge 35
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: new Date().getFullYear() - 30,
        month: 4,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        minAge: 25,
        maxAge: 35
      });
    expect(res.status).to.equal(200);

    // user 1 - age 30, minAge 40
    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: new Date().getFullYear() - 30,
        month: 4,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        minAge: 40,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 0 - does not see q1
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // change age preference
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        minAge: 20,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('nearby boost', async () => {
    // user 1 - nearby, female looking for male friends
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 2 - not nearby, female looking for male friends
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: 34.05,
        longitude: -118.24,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 2)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 2)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'chess',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    // set scores equal
    await Question.updateMany({}, {score: 1});

    // user 0 - sees q1 first
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // sort by recent
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'recent', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'recent', filter: 'for_you', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'recent', filter: 'for_you', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // ban q1
    q = await Question.findById(q1Id);
    q.banned = true;
    await q.save();

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('same interest boost', async () => {
    // user 1 - not same region, female looking for male friends
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 19.07,
        longitude: 72.87,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    // set scores equal
    await Question.updateMany({}, {score: 1});

    // user 0 - sees q1 first
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'for_you', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('qod', async () => {
    // load question of day
    await createQuestion({
      createdAt: new Date(),
      text: 'qod 1',
      interestName: 'questions',
    });

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'for_you', sort: 'popular' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0].text).to.equal('qod 1');
  });

  it('selfies', async () => {
    // set up user 1
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    // q1 - has selfie
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'title1',
        text: 'text1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    q = await Question.findById(q1Id);
    q.faceDetected = true;
    await q.save();

    // dating preferences don't match - selfie hidden on for you and explore
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'popular', filter: 'for_you' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'popular', filter: 'explore' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // kpop feed - selfie not hidden
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0)
      .query({ sort: 'popular', interestName: 'kpop' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    // set matching dating preferences
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        dating: ['female'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        dating: ['male'],
      });
    expect(res.status).to.equal(200);

    // now selfie appears
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'popular', filter: 'for_you' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ sort: 'popular', filter: 'explore' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    // user should be able to see own post regardless of selfie/dating preferences
    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .query({ sort: 'popular', filter: 'for_you' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1)
      .query({ sort: 'popular', filter: 'explore' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);
  });
});

describe('custom feeds', async () => {
  beforeEach(async () => {
    sinon.stub(constants, 'getPageSize').returns(1);

    const numUsers = 4;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.10.21' });
      expect(res.status).to.equal(200);
    }
  });

  it('create, update, delete', async () => {
    // no feeds yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(0);

    // create new custom feed
    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "everyone",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(1);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 1',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "everyone",
    });

    // edit custom feed
    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "friends",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(1);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 1',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "friends",
    });

    // create another new feed
    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 2',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "everyone",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(2);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 1',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "friends",
    });
    expect(res.body.user.customFeeds[1]).to.eql({
      feedName: 'Feed 2',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "everyone",
    });

    // delete a feed
    res = await request(app)
      .delete('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        feedName: 'Feed 1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(1);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 2',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "everyone",
    });
  });

  it('create, update, delete, reorder', async () => {
    // no feeds yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(0);

    // create new custom feed
    res = await request(app)
      .put('/v1/user/customFeeds')
      .set('authorization', 0)
      .send({
        customFeeds: [{
          feedName: 'Feed 1',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "everyone",
        }]
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(1);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 1',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "everyone",
    });

    // edit custom feed
    res = await request(app)
      .put('/v1/user/customFeeds')
      .set('authorization', 0)
      .send({
        customFeeds: [{
          feedName: 'Feed 1',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "friends",
        }]
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(1);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 1',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "friends",
    });

    // create another new feed
    res = await request(app)
      .put('/v1/user/customFeeds')
      .set('authorization', 0)
      .send({
        customFeeds: [
        {
          feedName: 'Feed 1',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "friends",
        },
        {
          feedName: 'Feed 2',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "everyone",
        }]
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(2);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 1',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "friends",
    });
    expect(res.body.user.customFeeds[1]).to.eql({
      feedName: 'Feed 2',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "everyone",
    });

    // reorder feeds
    res = await request(app)
      .put('/v1/user/customFeeds')
      .set('authorization', 0)
      .send({
        customFeeds: [
        {
          feedName: 'Feed 2',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "everyone",
        },
        {
          feedName: 'Feed 1',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "friends",
        }
        ]
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(2);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 2',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "everyone",
    });
    expect(res.body.user.customFeeds[1]).to.eql({
      feedName: 'Feed 1',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "friends",
    });

    // delete a feed
    res = await request(app)
      .put('/v1/user/customFeeds')
      .set('authorization', 0)
      .send({
        customFeeds: [
        {
          feedName: 'Feed 2',
          "dating": ["female"],
          "friends": ["female"],
          "minAge": 18,
          "maxAge": 30,
          "showVerifiedOnly": true,
          "relationship": "everyone",
        },
        ]
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.customFeeds.length).to.equal(1);
    expect(res.body.user.customFeeds[0]).to.eql({
      feedName: 'Feed 2',
      "dating": ["female"],
      "friends": ["female"],
      "minAge": 18,
      "maxAge": 30,
      "showVerifiedOnly": true,
      "relationship": "everyone",
    });
  });

  it('age', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'q1',
        text: 'q1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 0)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/birthday')
      .set('authorization', 1)
      .send({
        year: new Date().getFullYear() - 30,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        minAge: 25,
        maxAge: 35,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "minAge": 40,
          "maxAge": 50,
          "relationship": "everyone",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "minAge": 20,
          "maxAge": 25,
          "relationship": "everyone",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "minAge": 20,
          "maxAge": 40,
          "relationship": "everyone",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // other user's age filter doesn't match
    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        minAge: 33,
        maxAge: 35,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // reset social preference age filter
    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "minAge": 18,
          "maxAge": 200,
          "relationship": "everyone",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('relationship - friends', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'q1',
        text: 'q1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "relationship": "friends",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // become friends
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({ user: '0' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('relationship - following', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'q1',
        text: 'q1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "relationship": "following",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // user 0 follows user 1
    res = await request(app)
      .patch('/v1/follow/sendFollowRequest')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.approved).to.equal(true);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
  });

  it('interests and keywords', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: 'q1',
        text: 'q1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          interestNames: ['chess'],
          keywords: ['chess'],
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          interestNames: ['kpop'],
          keywords: ['chess'],
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          interestNames: ['chess'],
          keywords: ['q1'],
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          interestNames: ['kpop'],
          excludeKeywords: ['q1'],
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('prioritize nearby', async () => {
    // user 0 - US, male looking for female friends
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 0)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 0)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        friends: ['female'],
      });
    expect(res.status).to.equal(200);

    // user 1 - nearby, female looking for male friends
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 1)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    await Question.updateOne({_id: q1Id}, {score: 1});

    // user 2 - not nearby, female looking for male friends
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 2)
      .send({
        latitude: 34.05,
        longitude: -118.24,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 2)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 2)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'chess',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    await Question.updateOne({_id: q2Id}, {score: 2});

    // user 3 - nearby, male looking for male friends
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 3)
      .send({
        latitude: 21.30,
        longitude: -157.85,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 3)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/preferences')
      .set('authorization', 3)
      .send({
        friends: ['male'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 3)
      .send({
        interestName: 'chess',
        title: 'title2',
        text: 'text2',
      });
    expect(res.status).to.equal(200);
    const q3Id = res.body._id;

    await Question.updateOne({_id: q3Id}, {score: 10});

    // user 0 - without prioritize nearby
    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "friends": ["female"],
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'Feed 1' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'Feed 1', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'Feed 1', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // user 0 - prioritize nearby
    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          friends: ["female"],
          prioritizeNearby: true
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'Feed 1' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'Feed 1', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q2Id);

    before = res.body.questions[0]._id;
    res = await request(app)
      .get('/v1/question/feed')
      .query({ sort: 'popular', filter: 'Feed 1', beforeId: before })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('no qod', async () => {
    // load question of day
    await createQuestion({
      createdAt: new Date(),
      text: 'qod 1',
      interestName: 'questions',
    });

    res = await request(app)
      .put('/v1/user/customFeed')
      .set('authorization', 0)
      .send({
        customFeed: {
          feedName: 'Feed 1',
          "relationship": "following",
        }
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0)
      .query({ filter: 'Feed 1' })
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

});

it('sandbox countries', async () => {
  // user 0 - USA
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title0',
      text: 'text0',
    });
  expect(res.status).to.equal(200);
  const q0Id = res.body._id;

  // user 1 - India
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 28.6139,
      longitude: 77.2090,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  // sort by popular - both posts should appear to both users
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore', language: 'en' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);

  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore', language: 'en' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(2);

  // sort by recent - posts restricted based on sandbox
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'recent', filter: 'explore', language: 'en' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'recent', filter: 'explore', language: 'en' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q1Id);
});

it('hebrew feed', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      language: 'iw',
      title: 'title0',
      text: 'text0',
    });
  expect(res.status).to.equal(200);
  const q0Id = res.body._id;

  // query for iw
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore', language: 'iw' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);

  // query for he
  res = await request(app)
    .get('/v1/question/feed')
    .query({ sort: 'popular', filter: 'explore', language: 'he' })
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions.length).to.equal(1);
  expect(res.body.questions[0]._id).to.equal(q0Id);
});

it('reject bold/italic fonts', async () => {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/interest')
    .set('authorization', 0)
    .send({ name: '𝙨𝙖𝙙' });
  expect(res.status).to.equal(200);
  expect(res.body.approved).to.equal(false);

  res = await request(app)
    .post('/v1/interest')
    .set('authorization', 0)
    .send({ name: '𝗹𝗼𝘃𝗲' });
  expect(res.status).to.equal(200);
  expect(res.body.approved).to.equal(false);
});

/*
describe('premium', async () => {

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.16' });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    user.config.social_premium = true;
    await user.save();
  });

  it('boosted visibility', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '0',
        text: '0',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    const q1 = await Question.findById(q1Id);
    expect(q1.isLightlyBoosted).to.equal(true);
  });

  it('2x post daily limit', async () => {
    constants.getDailyPostLimit.restore();
    sinon.stub(constants, 'getDailyPostLimit').returns(2);

    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 0)
        .send({
          interestName: 'kpop',
          title: i.toString(),
          text: i.toString(),
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '4',
        text: '4',
      });
    expect(res.status).to.equal(403);
  });
});
*/

it('qod with null createdBy', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.12' });
  expect(res.status).to.equal(200);

  // create question
  newQuestion = await createQuestion({
    createdAt: new Date(),
    text: 'qod',
    interestName: 'questions',
  });
  await newQuestion.save();

  res = await request(app)
    .get('/v1/question/feed')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.questions[0].createdBy).to.equal();
  expect(res.body.questions[0].profilePreview).to.equal(null);
  expect(res.body.questions[0].allowIncomingRequests).to.equal(false);

  res = await request(app)
    .get('/web/question/feed')
  expect(res.status).to.equal(200);
  expect(res.body.questions[0].createdBy).to.equal();
  expect(res.body.questions[0].profilePreview).to.equal(null);
  expect(res.body.questions[0].allowIncomingRequests).to.equal();
});

it('testimonials', async () => {
  const maleTestimonialId = '64cc42eb16ac601840da523a';
  const femaleTestimonialId = '648bc938fa81646c02849a70';

  // create questions
  await createQuestion({
    _id: maleTestimonialId,
    createdBy: '0',
    text: 'male testimonial',
    interestName: 'chess',
  });
  await createQuestion({
    _id: femaleTestimonialId,
    createdBy: '0',
    text: 'female testimonial',
    interestName: 'chess',
  });

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 0)
    .send({ gender: 'male' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/social-proof/testimonials')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.testimonials.length).to.equal(1);
  expect(res.body.testimonials[0]._id).to.equal(maleTestimonialId);
  expect(res.body.testimonials[0].text).to.equal('male testimonial');

  res = await request(app)
    .put('/v1/user/gender')
    .set('authorization', 0)
    .send({ gender: 'female' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/social-proof/testimonials')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.testimonials.length).to.equal(1);
  expect(res.body.testimonials[0]._id).to.equal(femaleTestimonialId);
  expect(res.body.testimonials[0].text).to.equal('female testimonial');
});

it('like and comment from blocked user', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 0)
    .send({
      interestName: 'kpop',
      title: 'title1',
      text: 'text1',
    });
  expect(res.status).to.equal(200);
  const q1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;

  res = await request(app)
    .patch('/v1/user/block')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/question/like')
    .set('authorization', 1)
    .send({ questionId: q1Id });
  expect(res.status).to.equal(200);
  expect((await Question.findById(q1Id)).numLikes).to.equal(0);

  res = await request(app)
    .patch('/v1/comment/like')
    .set('authorization', 1)
    .send({ commentId: c1Id });
  expect(res.status).to.equal(200);
  expect((await Comment.findById(c1Id)).numLikes).to.equal(0);

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: q1Id,
    });
  expect(res.status).to.equal(403);

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q1Id,
      text: 'comment1',
      parentId: c1Id,
    });
  expect(res.status).to.equal(403);
});

it('get interest follower pictures', async () => {
  for (let i = 0; i < 10; i++) {
    await createUser(i);
    user = await User.findById(i.toString());
    user.pictures = [`picture${i}`];
    await user.save();
  }

  // set up preferences and genders
  res = await request(app)
    .patch('/v1/user/preferences')
    .set('authorization', 0)
    .send({
      dating: ['male'],
      friends: ['female'],
    })
  expect(res.status).to.equal(200);

  for (let i = 0; i <= 3; i++) {
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', i)
      .send({'gender': 'male'})
    expect(res.status).to.equal(200);
  }
  for (let i = 4; i <= 6; i++) {
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', i)
      .send({'gender': 'female'})
    expect(res.status).to.equal(200);
  }
  for (let i = 7; i <= 9; i++) {
    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', i)
      .send({'gender': 'non-binary'})
    expect(res.status).to.equal(200);
  }

  // no followers yet
  res = await request(app)
    .get('/v1/interest/followerPictures?name=kpop&name=chess&name=error')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body).to.eql({
    interests: [
      {
        name: 'kpop',
        numFollowers: 0,
        followerPictures: [],
      },
      {
        name: 'chess',
        numFollowers: 0,
        followerPictures: [],
      },
    ],
  });

  // all users join kpop
  for (let i = 0; i < 10; i++) {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', i)
      .send({
        interestNames: ['kpop'],
      });
    expect(res.status).to.equal(200);
  }

  // should see dating, excluding self
  res = await request(app)
    .get('/v1/interest/followerPictures?name=kpop')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('kpop');
  expect(res.body.interests[0].numFollowers).to.equal(10);
  expect(res.body.interests[0].followerPictures).to.have.members(['picture1', 'picture2', 'picture3']);

  // if not enough dating, then should see friends
  for (let i = 3; i <= 5; i++) {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', i)
      .send({
        interestNames: ['chess'],
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/interest/followerPictures?name=kpop')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('kpop');
  expect(res.body.interests[0].numFollowers).to.equal(7);
  expect(res.body.interests[0].followerPictures).to.have.members(['picture1', 'picture2', 'picture6']);

  // if not enough dating or friends, then should see others
  for (let i of [2, 7, 8]) {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', i)
      .send({
        interestNames: ['chess'],
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/interest/followerPictures?name=kpop')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(1);
  expect(res.body.interests[0].name).to.equal('kpop');
  expect(res.body.interests[0].numFollowers).to.equal(4);
  expect(res.body.interests[0].followerPictures).to.have.members(['picture1', 'picture9', 'picture6']);

  // test multiple interests
  for (let i of [4]) {
    res = await request(app)
      .put('/v1/user/interests')
      .set('authorization', i)
      .send({
        interestNames: [],
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/v1/interest/followerPictures?name=kpop&name=chess')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(2);
  expect(res.body.interests[0].name).to.equal('kpop');
  expect(res.body.interests[0].numFollowers).to.equal(4);
  expect(res.body.interests[0].followerPictures).to.have.members(['picture1', 'picture9', 'picture6']);
  expect(res.body.interests[1].name).to.equal('chess');
  expect(res.body.interests[1].numFollowers).to.equal(5);
  expect(res.body.interests[1].followerPictures).to.have.members(['picture2', 'picture3', 'picture5']);

  // banned users and users without pictures should be skipped
  user = await User.findById('2');
  user.pictures = [];
  await user.save();

  user = await User.findById('3');
  user.shadowBanned = true;
  await user.save();

  res = await request(app)
    .get('/v1/interest/followerPictures?name=kpop&name=chess')
    .set('authorization', 0)
  expect(res.status).to.equal(200);
  expect(res.body.interests.length).to.equal(2);
  expect(res.body.interests[0].name).to.equal('kpop');
  expect(res.body.interests[0].numFollowers).to.equal(4);
  expect(res.body.interests[0].followerPictures).to.have.members(['picture1', 'picture9', 'picture6']);
  expect(res.body.interests[1].name).to.equal('chess');
  expect(res.body.interests[1].numFollowers).to.equal(5);
  expect(res.body.interests[1].followerPictures).to.have.members(['picture5', 'picture7', 'picture8']);
});

it('noIndex for posts in interests', async () => {

  await Interest.insertMany([
    {
      name: 'gaming',
      interest: 'gaming',
    },
    {
      name: 'sports',
      interest: 'sports',
    }
  ])

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
  .put('/v1/user/initApp')
  .set('authorization', 0);
expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/interests')
    .set('authorization', 1)
    .send({
      interestNames: ['gaming', 'sports']
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'gaming' })
  expect(res.status).to.equal(200);
  expect(res.body.noIndex).to.eql(true);

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'sports' })
  expect(res.status).to.equal(200);
  expect(res.body.noIndex).to.eql(true);

  for (let i = 0; i < 5; i++) {
    res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'gaming',
      title: `${i}-title-gaming`,
      text: `${i}-text`,
    });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'gaming' })
  expect(res.status).to.equal(200);
  expect(res.body.noIndex).to.eql(false);

  let user0 = await User.findOne({ _id: 0 })
  user0.shadowBanned = true
  await user0.save()

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'gaming' })
  expect(res.status).to.equal(200);
  expect(res.body.noIndex).to.eql(false);

  for (let i = 0; i < 4; i++) {
    res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'sports',
      title: `${i}-title-sports`,
      text: `${i}-text`,
    });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'sports' })
  expect(res.status).to.equal(200);
  expect(res.body.noIndex).to.eql(true);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'sports',
      title: `${5}-title-sports`,
      text: `${5}-text`,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/web/interest')
    .query({ name: 'sports' })
  expect(res.status).to.equal(200);
  expect(res.body.noIndex).to.eql(false);
});
