const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const omit = require('lodash/omit');
const {
  app, validGif, validResponseGif, validTenorGif, validTenorResponseGif, validImagePath, validAudioPath, initSocket, destroySocket, getSocketPromise, createUser, getProfilePreview, validVideoPath,
} = require('./common');
const constants = require('../lib/constants');
const { pageSize } = require('../lib/constants');
const coinsConstants= require('../lib/coins-constants');
const { createQuestion } = require('../lib/social');
const socialLib = require('../lib/social');
const User = require('../models/user');
const Comment = require('../models/comment');
const Question = require('../models/question');
const Profile = require('../models/profile');
const WebPage = require('../models/web-page');
const LanguageMismatch = require('../models/language-mismatch');
const googleTranslate = require('../lib/google-translate');
const PostModeration = require('../models/post-moderation');
const Subcategory = require('../models/subcategory');
const Category = require('../models/category');
const databaseLib = require('../lib/database');

const {
  initApp, getUserProfile, getWebDbProfile, getSingleCommentById, getComments,fetchCoinData
} = require('./helper/api');

const { validMbti } = require('../lib/personality');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');
const { BOO_SUPPORT_ID } = require('../lib/chat');
const { fakeAdminMessaging, setMockPromptResponse, setMockImageModerationResponse, setS3MockContentList } = require('./stub');
const PostReport = require('../models/post-report');

describe('image moderation test', () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 4; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }
  });

  it('should display or hide image comment based on moderation config', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 3)
      .send({
        interestName: 'chess',
        title: 'first post',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // Setup moderation response: Flagged by Hive
    const hiveResponse = {
      isFlagged: true,
      detectionLabels: [
        {
          ParentName: 'Explicit Nudity',
          Name: 'Exposed Male Genitalia',
          Confidence: 95,
        },
      ],
      flaggedModerationLabel: {
        ParentName: 'Explicit Nudity',
        Name: 'Exposed Male Genitalia',
        Confidence: 95,
      },
    };

    const rekognitionResponse = {
      isFlagged: false,
      detectionLabels: [],
    };

    setMockImageModerationResponse(rekognitionResponse, hiveResponse);

    // user 2 creates image comment
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const c0Id = res.body._id;

    res = await request(app)
      .post('/v1/comment/image')
      .set('authorization', 2)
      .query({ commentId: c0Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // Flagged by hive, so comment will not be visible to user
    res = await request(app)
      .get('/v1/comment/v2')
      .set('authorization', 1)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    res = await request(app)
      .get('/v1/comment/v2')
      .set('authorization', 0)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);
  });

  it('should display or hide video comment based on moderation config', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 3)
      .send({
        interestName: 'chess',
        title: 'first post',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // Setup moderation response: Flagged by Hive
    const hiveResponse = {
      isFlagged: true,
      detectionLabels: [
        {
          ParentName: 'Explicit Nudity',
          Name: 'Exposed Male Genitalia',
          Confidence: 95,
        },
      ],
      flaggedModerationLabel: {
        ParentName: 'Explicit Nudity',
        Name: 'Exposed Male Genitalia',
        Confidence: 95,
      },
    };

    const rekognitionResponse = {
      isFlagged: false,
      detectionLabels: [],
    };

    setMockImageModerationResponse(rekognitionResponse, hiveResponse);
    setS3MockContentList({
      Contents: [{
        Key: "photos/photo1.jpg",
      },
      {
        Key: "photos/photo2.jpg",
      }],
    });

    // user 2 creates video comment
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const c0Id = res.body._id;

    res = await request(app)
      .post('/v1/comment/video')
      .set('authorization', 2)
      .query({ commentId: c0Id })
      .attach('video', validVideoPath);
    expect(res.status).to.equal(200);

    // comment is visible to creator
    res = await request(app)
      .get('/v1/comment/v2')
      .set('authorization', 2)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);

    // comment not will be visible to user
    res = await request(app)
      .get('/v1/comment/v2')
      .set('authorization', 1)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    res = await request(app)
      .get('/v1/comment/v2')
      .set('authorization', 0)
      .query({ questionId: q1Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);
  });
});

describe('testing user comment report with same device ID', () => {
  beforeEach(async () => {
    for (let i = 0; i < 5; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);
      if (i % 2 === 0) {
        const user = await User.findById(i);
        user.deviceId = 'same-device-id';
        await user.save();
      }
    }
  });

  it('should not create report if comment was reported with same device ID user', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: 'first post',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 4)
      .send({
        questionId: res.body._id,
        text: 'this is a test comment!',
        parentId: res.body._id,
      });
    expect(res.status).to.equal(200);
    const c0Id = res.body._id;

    // Reporting comment with user 0
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 0)
      .send({ commentId: c0Id });
    expect(res.status).to.equal(200);

    // Should not create report because same device ID with user 0
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 2)
      .send({ commentId: c0Id });
    expect(res.status).to.equal(200);

    let reports = await PostReport.find({ reportedComment: c0Id });
    expect(reports.length).to.equal(1);

    // Reporting comment with user 3
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 3)
      .send({ commentId: c0Id });
    expect(res.status).to.equal(200);

    reports = await PostReport.find({ reportedComment: c0Id });
    expect(reports.length).to.equal(2);

    // sending invalid comment ID
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 3)
      .send({ commentId: '123ab' });
    expect(res.status).to.equal(422);
  });
});

describe(`blocked user's comment on qod`, () => {
  it('blocked user will be able to comment but notification will not be sent', async () => {
    for (let i = 0; i < 4; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', i)
        .send({
          fcmToken: 'token',
        });
      expect(res.status).to.equal(200);

      const user = await User.findById(i);
      user.firstName = `name ${i}`;
      user.gender = 'male';
      user.admin = i === 0;
      user.adminPermissions = { approveQod: i === 0 };
      await user.save();
    }

    // create support user
    const supportId = BOO_SUPPORT_ID;
    let res = await request(app)
      .get('/v1/user')
      .set('authorization', supportId);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question/submitQod')
      .set('authorization', 1)
      .send({
        text: 'boo1',
        isAnonymous: false,
      });
    expect(res.status).to.eql(200);

    res = await request(app)
      .get('/v1/admin/questionCandidates')
      .set('authorization', 0)
      .send({});
    expect(res.status).to.eql(200);
    const id = res.body.candidates[0].id;

    res = await request(app)
      .post('/v1/admin/questionCandidates/status')
      .set('authorization', 0)
      .send({
        id: id,
        status: 'approved',
      });
    expect(res.status).to.eql(200);

    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.eql(200);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    const qod = res.body.questions[0];

    // stubing admin messaging here so that we can check if the notification is sent or not
    const sendStub = sinon.stub(fakeAdminMessaging, 'send').callsFake((params) => new Promise((resolve, reject) => {
      if (params.token === 'invalidToken') {
        return reject(new Error('Fake error'));
      }
      resolve({ response: 'success' });
    }));

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: qod._id,
        text: 'this is a comment to a blocked user',
        parentId: qod._id,
      });
    expect(res.status).to.equal(200);

    // wait for few miliseconds and confirm notification is not sent
    await new Promise((resolve) => setTimeout(resolve, 200));
    sinon.assert.notCalled(sendStub);

    // Commenting with non-blocked user
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: qod._id,
        text: 'this is a comment by a non-blocked user',
        parentId: qod._id,
      });
    expect(res.status).to.equal(200);

    // wait for few miliseconds
    await new Promise((resolve) => setTimeout(resolve, 200));

    // Confirm notification is sent for this comment
    const user1 = await User.findById(1);
    const formattedComment = {
      _id: res.body._id,
      question: qod._id,
      parent: qod._id,
      postRepliedTo: qod._id,
      interest: null,
      interestName: 'questions',
    };
    const params = {
      token: 'token',
      notification: {
        title: qod.text,
        body: `${res.body.createdBy.firstName}: @${user1.firstName} ${res.body.text}`,
      },
      data: {
        comment: JSON.stringify(formattedComment),
      },
      fcmOptions: { analyticsLabel: 'new-comment-on-post-other-souls' }, // analytics label updated
    };

    sinon.assert.calledOnce(sendStub);
    sinon.assert.calledWith(sendStub, sinon.match(params));
  });
});

describe('comment tests', () => {
  it('comment tests basic', async function () {
    this.timeout(1000000);

    const numUsers = pageSize * 2;
    const profiles = {};
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/relationshipStatus')
        .set('authorization', uid)
        .send({ relationshipStatus: 'Single' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/datingSubPreferences')
        .set('authorization', uid)
        .send({ datingSubPreferences: 'Short term fun' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/relationshipType')
        .set('authorization', uid)
        .send({ relationshipType: 'Polyamorous' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
      profiles[uid.toString()] = {
        _id: uid.toString(),
        firstName: '',
        pictures: [],
        personality: null,
        gender: null,
        age: null,
        description: '',
        education: '',
        prompts: [],
        interests: [],
        interestNames: [],
        crown: false,
        hideQuestions: false,
        hideComments: false,
        handle: res.body.handle,
        teleport: false,
        preferences: { purpose: ['dating', 'friends'] },
        horoscope: null,
        location: null,
        karma: 0,
        numFollowers: 0,
        verified: false,
        verificationStatus: 'unverified',
        relationshipStatus: 'Single',
        datingSubPreferences: 'Short term fun',
        relationshipType: 'Polyamorous',
      };
    }

    /* Testing comment structure, mainly testing sorting

    Question 1 (user 1)
      Comment 1 (user 1) - 7 likes - 5/20
        Comment 2 (user 2) - 5 likes - 5/23
        Comment 3 (user 1) - 4 likes - 5/24
      Comment 7 (user 4) - 2 likes - 5/21
      Comment 6 (user 3) - 1 like - 5/22

    Question 2 (user 2)
      Comment 8 (user 6) - 2 likes - 5/20
      Comment 9 (user 7) - 3 likes - 5/21

    */

    let newQuestion = await createQuestion({
      createdAt: new Date(2021, 5, 20, 0, 0, 0, 0),
      createdBy: '1',
      text: 'question 1',
      interestName: 'questions',
    });
    await newQuestion.save();
    newQuestion = await createQuestion({
      createdAt: new Date(2021, 5, 20, 0, 0, 0, 0),
      createdBy: '2',
      text: 'question 2',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    const q1Id = res.body.questions[0]._id;
    const q2Id = res.body.questions[1]._id;
    const q1Url = res.body.questions[0].url;

    // // question 1
    // res = await request(app)
    //   .post('/v1/question')
    //   .set('authorization', '1')
    //   .send({
    //     "createdAt": new Date(2021, 5, 20, 0, 0, 0, 0),
    //     "text": "question 1"
    //   })
    // expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 20, 0, 0, 0, 0)).getTime());
    // expect(res.body.createdBy).to.equal('1');
    // expect(res.body.text).to.equal('question 1');
    // expect(res.body.numLikes).to.equal(0);
    // expect(res.body.isDeleted).to.equal(false);
    // let q1Id = res.body._id;

    // // question 2
    // res = await request(app)
    //   .post('/v1/question')
    //   .set('authorization', '2')
    //   .send({
    //     "createdAt": new Date(2021, 5, 20, 0, 0, 0, 0),
    //     "text": "question 2"
    //   })
    // expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 20, 0, 0, 0, 0)).getTime());
    // expect(res.body.createdBy).to.equal('2');
    // expect(res.body.text).to.equal('question 2');
    // expect(res.body.numLikes).to.equal(0);
    // expect(res.body.isDeleted).to.equal(false);
    // let q2Id = res.body._id;

    // no comments for question 1
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({ comments: [] });

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sortBy: 'createdAtRecent' })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({ comments: [] });

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sortBy: 'createdAtOldest' })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({ comments: [] });

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sortBy: 'numLikes' })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({ comments: [] });

    console.log(`question1id:${q1Id}`);
    // comment 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '1')
      .send({
        createdAt: new Date(2021, 5, 20, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 1', // intentional typo
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 20, 0, 0, 0, 0)).getTime());
    const c1Id = res.body._id;
    const c1 = {
      _id: c1Id,
      createdAt: res.body.createdAt,
      createdBy: profiles['1'],
      question: q1Id,
      interestName: 'questions',
      language: 'en',
      text: 'Comment 1',
      parent: q1Id,
      repliedTo: null,
      depth: 1,
      numComments: 0,
      numLikes: 0,
      isDeleted: false,
      isEdited: false,
      hasUserLiked: false,
      isFriendComment: false,
      comments: [],
      postRepliedTo: q1Id,
      linkedKeywords: [],
      linkedExploreKeywords: [],
      linkedPillarKeywords: [],
      linkedCategories: [],
      linkedSubcategories: [],
      linkedProfiles: [],
    };
    expect(res.body).to.eql(c1);
    c1.isFriendComment = false;
    c1.linkedExploreKeywords = ['comment'];

    const c1WithQuestionUrl = {
      ...c1,
      questionUrl: q1Url,
    };

    // one comment for question 1
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '4');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]).to.eql(c1);

    // get single comment by id
    res = await request(app)
      .get('/v1/comment/single')
      .query({ commentId: c1._id })
      .set('authorization', 4);
    expect(res.status).to.equal(200);
    expect(res.body.comment).to.eql(c1WithQuestionUrl);

    res = await request(app)
      .get('/web/comment/single')
      .query({ commentId: c1._id })
    expect(res.status).to.equal(200);
    expect(res.body.comment._id).to.eql(c1._id);
    expect(res.body.comment.questionUrl).to.eql(c1WithQuestionUrl.questionUrl);

    // try to get comment that does not exist
    res = await request(app)
      .get('/v1/comment/single')
      .query({ commentId: 'fake' })
      .set('authorization', 4);
    expect(res.status).to.equal(422);

    // user 2 likes comment 1
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', '2')
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    c1.numLikes += 1;
    c1.createdBy.karma = 1;

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '4');
    expect(res.status).to.equal(200);
    expect(res.body.comments[0]).to.eql(c1);

    // user 2 tries to like comment 1 again
    res = await request(app)
      .patch('/v1/comment/like')
      .set('authorization', '2')
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '4');
    expect(res.status).to.equal(200);
    expect(res.body.comments[0]).to.eql(c1);

    // user 1 edits comment 1 (his own comment)
    res = await request(app)
      .patch('/v1/comment/edit')
      .set('authorization', '1')
      .send({ commentId: c1Id, text: 'Comment 1' });
    expect(res.status).to.equal(200);

    c1.text = 'Comment 1';
    c1.isEdited = true;
    c1.linkedExploreKeywords = ['comment'];

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '4');
    expect(res.status).to.equal(200);
    expect(res.body.comments[0]).to.eql(c1);

    // user 2 tries to edit comment 1 (not his)
    res = await request(app)
      .patch('/v1/comment/edit')
      .set('authorization', '2')
      .send({ commentId: c1Id, text: 'I am hacker' });
    expect(res.status).to.equal(404);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '4');
    expect(res.status).to.equal(200);
    expect(res.body.comments[0]).to.eql(c1);

    // user 2 unlikes comment 1
    res = await request(app)
      .patch('/v1/comment/unlike')
      .set('authorization', '2')
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    c1.numLikes -= 1;
    c1.createdBy.karma = 0;

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '4');
    expect(res.status).to.equal(200);
    expect(res.body.comments[0]).to.eql(c1);

    // user 2 tries to unlike comment 1 again
    res = await request(app)
      .patch('/v1/comment/unlike')
      .set('authorization', '2')
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '3');
    expect(res.status).to.equal(200);
    expect(res.body.comments[0]).to.eql(c1);

    // comment 2
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '2')
      .send({
        createdAt: new Date(2021, 5, 23, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 2',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 23, 0, 0, 0, 0)).getTime());
    const c2Id = res.body._id;
    const c2 = {
      _id: c2Id,
      createdAt: res.body.createdAt,
      createdBy: profiles['2'],
      question: q1Id,
      interestName: 'questions',
      language: 'en',
      text: 'Comment 2',
      parent: c1Id,
      repliedTo: null,
      depth: 2,
      numComments: 0,
      numLikes: 0,
      isDeleted: false,
      isEdited: false,
      hasUserLiked: false,
      isFriendComment: false,
      comments: [],
      postRepliedTo: c1Id,
      linkedKeywords: [],
      linkedExploreKeywords: [],
      linkedPillarKeywords: [],
      linkedCategories: [],
      linkedSubcategories: [],
      linkedProfiles: [],
    };
    expect(res.body).to.eql(c2);

    // comment 3
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '1')
      .send({
        createdAt: new Date(2021, 5, 24, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 3',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 24, 0, 0, 0, 0)).getTime());
    const c3Id = res.body._id;
    const c3 = {
      _id: c3Id,
      createdAt: res.body.createdAt,
      createdBy: profiles['1'],
      question: q1Id,
      interestName: 'questions',
      language: 'en',
      text: 'Comment 3',
      parent: c1Id,
      repliedTo: null,
      depth: 2,
      numComments: 0,
      numLikes: 0,
      isDeleted: false,
      isEdited: false,
      hasUserLiked: false,
      isFriendComment: false,
      comments: [],
      postRepliedTo: c1Id,
      linkedKeywords: [],
      linkedExploreKeywords: [],
      linkedPillarKeywords: [],
      linkedCategories: [],
      linkedSubcategories: [],
      linkedProfiles: [],
    };
    c3.createdBy.karma = 1;
    expect(res.body).to.eql(c3);

    // comment 7
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '4')
      .send({
        createdAt: new Date(2021, 5, 21, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 7',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 21, 0, 0, 0, 0)).getTime());
    expect(res.body.createdBy._id).to.equal('4');
    expect(res.body.question).to.equal(q1Id);
    expect(res.body.text).to.equal('Comment 7');
    expect(res.body.parent).to.equal(q1Id);
    expect(res.body.repliedTo).to.equal(null);
    expect(res.body.depth).to.equal(1);
    expect(res.body.numLikes).to.equal(0);
    expect(res.body.isDeleted).to.equal(false);
    expect(res.body.isEdited).to.equal(false);
    const c7Id = res.body._id;

    // comment 6
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '3')
      .send({
        createdAt: new Date(2021, 5, 22, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 6',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 22, 0, 0, 0, 0)).getTime());
    expect(res.body.createdBy._id).to.equal('3');
    expect(res.body.question).to.equal(q1Id);
    expect(res.body.text).to.equal('Comment 6');
    expect(res.body.parent).to.equal(q1Id);
    expect(res.body.repliedTo).to.equal(null);
    expect(res.body.depth).to.equal(1);
    expect(res.body.numLikes).to.equal(0);
    expect(res.body.isDeleted).to.equal(false);
    expect(res.body.isEdited).to.equal(false);
    const c6Id = res.body._id;

    // comment 8
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '6')
      .send({
        createdAt: new Date(2021, 5, 20, 0, 0, 0, 0),
        questionId: q2Id,
        text: 'Comment 8',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 20, 0, 0, 0, 0)).getTime());
    expect(res.body.createdBy._id).to.equal('6');
    expect(res.body.question).to.equal(q2Id);
    expect(res.body.text).to.equal('Comment 8');
    expect(res.body.parent).to.equal(q2Id);
    expect(res.body.repliedTo).to.equal(null);
    expect(res.body.depth).to.equal(1);
    expect(res.body.numLikes).to.equal(0);
    expect(res.body.isDeleted).to.equal(false);
    expect(res.body.isEdited).to.equal(false);
    const c8Id = res.body._id;

    // comment 9
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '7')
      .send({
        createdAt: new Date(2021, 5, 21, 0, 0, 0, 0),
        questionId: q2Id,
        text: 'Comment 9',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    // expect((new Date(res.body.createdAt).getTime())).to.equal((new Date(2021, 5, 21, 0, 0, 0, 0)).getTime());
    expect(res.body.createdBy._id).to.equal('7');
    expect(res.body.question).to.equal(q2Id);
    expect(res.body.text).to.equal('Comment 9');
    expect(res.body.parent).to.equal(q2Id);
    expect(res.body.repliedTo).to.equal(null);
    expect(res.body.depth).to.equal(1);
    expect(res.body.numLikes).to.equal(0);
    expect(res.body.isDeleted).to.equal(false);
    expect(res.body.isEdited).to.equal(false);
    const c9Id = res.body._id;

    // fill comment likes based on testing structure
    for (let uid = 1; uid <= 7; uid++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', uid.toString())
        .send({ commentId: c1Id });
      expect(res.status).to.equal(200);
    }
    for (let uid = 1; uid <= 5; uid++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', uid.toString())
        .send({ commentId: c2Id });
      expect(res.status).to.equal(200);
    }
    for (let uid = 1; uid <= 4; uid++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', uid.toString())
        .send({ commentId: c3Id });
      expect(res.status).to.equal(200);
    }
    for (let uid = 1; uid <= 1; uid++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', uid.toString())
        .send({ commentId: c6Id });
      expect(res.status).to.equal(200);
    }
    for (let uid = 1; uid <= 2; uid++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', uid.toString())
        .send({ commentId: c7Id });
      expect(res.status).to.equal(200);
    }
    for (let uid = 1; uid <= 2; uid++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', uid.toString())
        .send({ commentId: c8Id });
      expect(res.status).to.equal(200);
    }
    for (let uid = 1; uid <= 3; uid++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', uid.toString())
        .send({ commentId: c9Id });
      expect(res.status).to.equal(200);
    }

    // sorting q1 children no sortBy
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c1Id);

    /*
    // sorting q1 children sortBy="createdAtRecent"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sortBy: "createdAtRecent" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c1Id);

    // sorting q1 children sortBy="createdAtOldest"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sortBy: "createdAtOldest" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c1Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c6Id);

    // sorting q1 children sortBy="numLikes"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sortBy: "numLikes" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c1Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c6Id);
    */

    // sorting c1 children no sortBy
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: c1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[1]._id).to.equal(c2Id);

    /*
    // sorting c1 children sortBy="createdAtRecent"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: c1Id, sortBy: "createdAtRecent" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[1]._id).to.equal(c2Id);

    // sorting c1 children sortBy="createdAtOldest"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: c1Id, sortBy: "createdAtOldest" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[1]._id).to.equal(c3Id);

    // sorting c1 children sortBy="numLikes"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: c1Id, sortBy: "numLikes" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[1]._id).to.equal(c3Id);
    */

    // sorting q2 children no sortBy
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c9Id);
    expect(res.body.comments[1]._id).to.equal(c8Id);

    /*
    // sorting q2 children sortBy="createdAtRecent"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id, sortBy: "createdAtRecent" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c9Id);
    expect(res.body.comments[1]._id).to.equal(c8Id);

    // sorting q2 children sortBy="createdAtOldest"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id, sortBy: "createdAtOldest" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c8Id);
    expect(res.body.comments[1]._id).to.equal(c9Id);

    // sorting q2 children sortBy="numLikes"
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id, sortBy: "numLikes" })
      .set('authorization', '1')
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c9Id);
    expect(res.body.comments[1]._id).to.equal(c8Id);
    */

    // testing numComments for question
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(2);
    expect(res.body.questions[0].numComments).to.equal(5);
    expect(res.body.questions[1].numComments).to.equal(2);

    // testing numComments for comment
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '3');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[0].numComments).to.equal(0);
    expect(res.body.comments[0].hasUserLiked).to.equal(false);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[1].numComments).to.equal(0);
    expect(res.body.comments[1].hasUserLiked).to.equal(false);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[2].numComments).to.equal(2);
    expect(res.body.comments[2].hasUserLiked).to.equal(true);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: c1Id })
      .set('authorization', '5');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[0].numComments).to.equal(0);
    expect(res.body.comments[0].hasUserLiked).to.equal(false);
    expect(res.body.comments[1]._id).to.equal(c2Id);
    expect(res.body.comments[1].numComments).to.equal(0);
    expect(res.body.comments[1].hasUserLiked).to.equal(true);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c9Id);
    expect(res.body.comments[0].numComments).to.equal(0);
    expect(res.body.comments[0].hasUserLiked).to.equal(true);
    expect(res.body.comments[1]._id).to.equal(c8Id);
    expect(res.body.comments[1].numComments).to.equal(0);
    expect(res.body.comments[1].hasUserLiked).to.equal(true);

    // user 7 blocks user 6 - should no longer see user 6's comments
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 7)
      .send({
        user: '6',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q2Id })
      .set('authorization', 7);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c9Id);

    // user 6 should not see user 7's comments
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q2Id })
      .set('authorization', '6');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c8Id);

    // test getting nested comments for questionId
    // user 1 should still see user 1's comments
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[0].comments.length).to.equal(0);
    expect(res.body.comments[1].comments.length).to.equal(0);
    expect(res.body.comments[2].comments.length).to.equal(2);
    expect(res.body.comments[2].comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[2].comments[1]._id).to.equal(c3Id);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c9Id);
    expect(res.body.comments[1]._id).to.equal(c8Id);

    // ban comment 1
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true };
    res = await user.save();

    res = await request(app)
      .put('/v1/admin/banComment')
      .set('authorization', 0)
      .send({
        commentId: c1Id,
      });
    expect(res.status).to.equal(200);

    // user 1 should still see own comment, but user 8 should not
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[0].comments.length).to.equal(0);
    expect(res.body.comments[1].comments.length).to.equal(0);
    expect(res.body.comments[2].comments.length).to.equal(2);
    expect(res.body.comments[2].comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[2].comments[1]._id).to.equal(c3Id);

    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id })
      .set('authorization', 8);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[0].comments.length).to.equal(0);
    expect(res.body.comments[1].comments.length).to.equal(0);

    // test shadow-ban keywords
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '6')
      .send({
        questionId: q2Id,
        text: 'here is my Instagram: @me',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c10Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '6')
      .send({
        questionId: q2Id,
        text: 'i like instant ramen',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c11Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '6')
      .send({
        questionId: q2Id,
        text: 'download at App-Birdy.com/download',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c12Id = res.body._id;

    // insert banned keywords when editing
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '6')
      .send({
        questionId: q2Id,
        text: 'innocent comment',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c13Id = res.body._id;
    res = await request(app)
      .patch('/v1/comment/edit')
      .set('authorization', '6')
      .send({ commentId: c13Id, text: 'text at 1234567890' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '6');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(5);
    expect(res.body.comments[0]._id).to.equal(c13Id);
    expect(res.body.comments[1]._id).to.equal(c12Id);
    expect(res.body.comments[2]._id).to.equal(c11Id);
    expect(res.body.comments[3]._id).to.equal(c10Id);
    expect(res.body.comments[4]._id).to.equal(c8Id);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c11Id);
    expect(res.body.comments[1]._id).to.equal(c9Id);
    expect(res.body.comments[2]._id).to.equal(c8Id);

    // user deletes account
    const deleteId = constants.IMMEDIATE_DELETION_ID;
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', deleteId);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', deleteId)
      .send({
        questionId: q2Id,
        text: 'hello',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c14Id = res.body._id;

    res = await request(app)
      .post('/v1/user/accountDeletion')
      .set('authorization', deleteId)
      .send({
        reason: [1, 4],
        feedback: 'feedback',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c11Id);
    expect(res.body.comments[1]._id).to.equal(c9Id);
    expect(res.body.comments[2]._id).to.equal(c8Id);

    // user 6 gets banned
    u = await User.findById('6');
    u.shadowBanned = true;
    await u.save();

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c9Id);

    // User posts same comment 2+ times
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '4')
      .send({
        questionId: q2Id,
        text: 'Comment 7',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c15Id = res.body._id;

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '4');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c15Id);
    expect(res.body.comments[1]._id).to.equal(c9Id);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q2Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c9Id);

    // ******** reply to a depth 2 comment ***************

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '4')
      .send({
        questionId: q1Id,
        text: 'Comment 16',
        parentId: c3Id,
      });
    expect(res.status).to.equal(200);
    const c16Id = res.body._id;
    const c16 = {
      _id: c16Id,
      createdAt: res.body.createdAt,
      createdBy: profiles['4'],
      question: q1Id,
      interestName: 'questions',
      language: 'en',
      text: 'Comment 16',
      parent: c1Id,
      repliedTo: profiles['1'],
      depth: 2,
      numComments: 0,
      numLikes: 0,
      isDeleted: false,
      isEdited: false,
      hasUserLiked: false,
      isFriendComment: false,
      comments: [],
      postRepliedTo: c3Id,
      linkedKeywords: [],
      linkedExploreKeywords: [],
      linkedPillarKeywords: [],
      linkedCategories: [],
      linkedSubcategories: [],
      linkedProfiles: [],
    };
    c16.createdBy.karma = 2;
    c16.repliedTo.karma = 6;
    expect(res.body).to.eql(c16);

    c16.isFriendComment = false;
    c16.linkedExploreKeywords = ['comment', '16'];
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[0].comments.length).to.equal(0);
    expect(res.body.comments[1].comments.length).to.equal(0);
    expect(res.body.comments[2].comments.length).to.equal(3);
    expect(res.body.comments[2].comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[2].comments[1]._id).to.equal(c3Id);
    expect(res.body.comments[2].comments[0].numComments).to.equal(0);
    expect(res.body.comments[2].comments[1].numComments).to.equal(1);
    expect(res.body.comments[2].comments[2]).to.eql(c16);

    // reply to my own depth 2 comment
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '4')
      .send({
        questionId: q1Id,
        text: 'Comment 17',
        parentId: c16Id,
      });
    expect(res.status).to.equal(200);
    c16.numComments += 1;
    const c17Id = res.body._id;
    const c17 = {
      _id: c17Id,
      createdAt: res.body.createdAt,
      createdBy: profiles['4'],
      question: q1Id,
      interestName: 'questions',
      language: 'en',
      text: 'Comment 17',
      parent: c1Id,
      repliedTo: null, // should not have repliedTo
      depth: 2,
      numComments: 0,
      numLikes: 0,
      isDeleted: false,
      isEdited: false,
      hasUserLiked: false,
      isFriendComment: false,
      comments: [],
      postRepliedTo: c16Id,
      linkedKeywords: [],
      linkedExploreKeywords: [],
      linkedPillarKeywords: [],
      linkedCategories: [],
      linkedSubcategories: [],
      linkedProfiles: [],
    };
    expect(res.body).to.eql(c17);
    c17.isFriendComment = false;
    c17.linkedExploreKeywords = ['comment', '17'];

    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[0].comments.length).to.equal(0);
    expect(res.body.comments[1].comments.length).to.equal(0);
    expect(res.body.comments[2].comments.length).to.equal(4);
    expect(res.body.comments[2].comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[2].comments[1]._id).to.equal(c3Id);
    expect(res.body.comments[2].comments[0].numComments).to.equal(0);
    expect(res.body.comments[2].comments[1].numComments).to.equal(1);
    expect(res.body.comments[2].comments[2]).to.eql(c16);
    expect(res.body.comments[2].comments[3]).to.eql(c17);

    // ****** pagination for viewing comments *******************

    // add new comments
    for (let uid = pageSize; uid < numUsers; uid++) {
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', uid.toString())
        .send({
          questionId: q1Id,
          text: uid.toString(),
          parentId: q1Id,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', uid.toString())
        .send({
          questionId: q1Id,
          text: `c${uid}`,
          parentId: c1Id,
        });
      expect(res.status).to.equal(200);
    }
    // add banned comments
    for (let i = 0; i < pageSize; i++) {
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', pageSize.toString())
        .send({
          questionId: q1Id,
          text: pageSize.toString(),
          parentId: q1Id,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .post('/v1/comment')
        .set('authorization', pageSize.toString())
        .send({
          questionId: q1Id,
          text: pageSize.toString(),
          parentId: c1Id,
        });
      expect(res.status).to.equal(200);
    }

    // set min version for paging
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', '1')
      .send({ appVersion: '1.10.15' });
    expect(res.status).to.equal(200);

    // get first page - should not include banned comments
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i].text).to.equal((numUsers - 1 - i).toString());
    }

    // get second page
    before = res.body.comments[pageSize - 1].createdAt;
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id, before })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[2].comments.length).to.equal(4 + pageSize);
    expect(res.body.comments[2].comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[2].comments[1]._id).to.equal(c3Id);
    expect(res.body.comments[2].comments[2]._id).to.equal(c16Id);
    expect(res.body.comments[2].comments[3]._id).to.equal(c17Id);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[2].comments[4 + i].text).to.equal(`c${pageSize + i}`);
    }
    const depth2CommentContextTestId = res.body.comments[2].comments[0]._id;

    // try get third page - no more
    before = res.body.comments[2].createdAt;
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id, before })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // User 1 sends like to user 2
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    // User 2 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // new comment from friend
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '2')
      .send({
        questionId: q1Id,
        text: 'Commnet', // intentional typo
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const newCommentId = res.body._id;

    // should sort comments by friends popular comment first then popular everyone else
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id, sort: 'popular' })
      .set('authorization', '1');
    console.log('*****res.body.comments*****', res.body.comments);

    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.be.greaterThan(0);
    expect(res.body.comments[0]._id).to.equal(newCommentId);
    expect(res.body.comments[1]._id).to.equal(c1Id);
    expect(res.body.comments[2]._id).to.equal(c7Id);

    // delete the new comment
    res = await request(app)
      .delete('/v1/comment')
      .set('authorization', 2)
      .query({ commentId: newCommentId });
    expect(res.status).to.equal(200);

    // pagination when sorting by popular
    totalComments = 0;

    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id, sort: 'popular' })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    console.log(res.body.comments);
    const q1PopularComments = res.body.comments;
    expect(res.body.comments.length).to.be.greaterThan(0);
    expect(res.body.comments[0]._id).to.equal(c1Id);
    expect(res.body.comments[1]._id).to.equal(c7Id);
    totalComments += res.body.comments.length;

    while (totalComments < pageSize + 3) {
      beforeId = res.body.comments[res.body.comments.length - 1]._id;
      res = await request(app)
        .get('/v1/comment')
        .query({ questionId: q1Id, sort: 'popular', beforeId })
        .set('authorization', '1');
      expect(res.status).to.equal(200);
      console.log(res.body.comments);
      expect(res.body.comments.length).to.be.greaterThan(0);
      totalComments += res.body.comments.length;
    }

    afterId = res.body.comments[0]._id;
    totalCommentsAfterId = res.body.comments.length;

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id, sort: 'popular', beforeId })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    expect(totalComments).to.equal(pageSize + 3);

    // afterId
    while (totalCommentsAfterId < pageSize + 3) {
      res = await request(app)
        .get('/v1/comment')
        .query({ questionId: q1Id, sort: 'popular', afterId })
        .set('authorization', '1');
      expect(res.status).to.equal(200);
      console.log(res.body.comments);
      expect(res.body.comments.length).to.be.greaterThan(0);
      totalCommentsAfterId += res.body.comments.length;
      afterId = res.body.comments[0]._id;
    }

    expect(res.body.comments[0]._id).to.equal(c1Id);
    expect(totalComments).to.equal(pageSize + 3);

    res = await request(app)
      .get('/v1/comment')
      .query({ questionId: q1Id, sort: 'popular', afterId })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // ********* get comment context *************************

    res = await request(app)
      .get('/v1/comment/context')
      .query({ commentId: c7Id, parentId: q1Id, questionId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize + 1);
    expect(res.body.comments[0]._id).to.equal(c7Id);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i + 1].text).to.equal((numUsers - 1 - i).toString());
    }

    res = await request(app)
      .get('/v1/comment/context')
      .query({
        commentId: c1Id, parentId: q1Id, questionId: q1Id, sort: 'popular',
      })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments).to.eql(q1PopularComments);

    res = await request(app)
      .get('/v1/comment/context')
      .query({
        commentId: c7Id, parentId: q1Id, questionId: q1Id, sort: 'popular',
      })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments).to.eql(q1PopularComments);

    res = await request(app)
      .get('/v1/comment/context')
      .query({ commentId: depth2CommentContextTestId, parentId: c1Id, questionId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(pageSize + 1);
    expect(res.body.comments[0]._id).to.equal(c1Id);
    expect(res.body.comments[0].comments.length).to.equal(4 + pageSize);
    expect(res.body.comments[0].comments[0]._id).to.equal(depth2CommentContextTestId);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.comments[i + 1].text).to.equal((numUsers - 1 - i).toString());
    }

    // ********* view likes *************************

    profiles['7'].karma = 2;
    profiles['3'].karma = 1;
    profiles['2'].karma = 5;
    profiles['2'].numFollowers = 1;

    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(1);
    expect(res.body.usersThatLiked).to.eql([
      profiles['7'], profiles['5'], profiles['4'], profiles['3'],
      profiles['2'],
    ]);

    // user 1 blocks user 3 - should not see user 3's like
    res = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 1)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(1);
    expect(res.body.usersThatLiked).to.eql([
      profiles['7'], profiles['5'], profiles['4'],
      profiles['2'],
    ]);

    // user 3 should not see user 1's like
    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c6Id })
      .set('authorization', '3');
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(1);
    expect(res.body.usersThatLiked).to.eql([]);

    // User 2 can't see likes for user 1's comment
    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c1Id })
      .set('authorization', '2');
    expect(res.body.totalPages).to.equal(0);
    expect(res.body.usersThatLiked).to.eql([]);

    // ************** pagination for likes ********************

    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c1Id, page: 0 })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(1);
    expect(res.body.usersThatLiked).to.eql([
      profiles['7'], profiles['5'], profiles['4'],
      profiles['2'],
    ]);

    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c1Id, page: 1 })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(1);
    expect(res.body.usersThatLiked.length).to.equal(0);

    // add new likes
    for (let uid = pageSize; uid < numUsers; uid++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', uid.toString())
        .send({ commentId: c1Id });
      expect(res.status).to.equal(200);
    }

    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c1Id, page: 0 })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2);
    expect(res.body.usersThatLiked.length).to.equal(pageSize);
    for (let i = 0; i < pageSize; i++) {
      expect(res.body.usersThatLiked[i]).to.eql(profiles[(numUsers - 1 - i).toString()]);
    }

    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c1Id, page: 1 })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2);
    expect(res.body.usersThatLiked).to.eql([
      profiles['7'], profiles['5'], profiles['4'],
      profiles['2'],
    ]);

    res = await request(app)
      .get('/v1/comment/likes')
      .query({ commentId: c1Id, page: 2 })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.totalPages).to.equal(2);
    expect(res.body.usersThatLiked.length).to.equal(0);

    // ***** test like notifications - should only send once ********

    console.log('Testing like notifications - should be sent only once');
    for (let i = 0; i < 10; i++) {
      res = await request(app)
        .patch('/v1/comment/like')
        .set('authorization', pageSize.toString())
        .send({ commentId: c1Id });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/comment/unlike')
        .set('authorization', pageSize.toString())
        .send({ commentId: c1Id });
      expect(res.status).to.equal(200);
    }
    console.log('Done testing like notifications');
  });

  it('comment level2 should always oldest-latest', async function () {
    // test scenario
    // user 0 create question1
    // user 1 write comment1 on question1
    // user 2 write comment2 on question1
    // user 2 and user 0 are friends
    // user 3 reply1 on comment1
    // user 3 and user 1 are friends
    // user 4 reply2 on comment1
    // user 5 reply3 on comment1

    // expected structure
    // Question 1
    // - comment2 (user 2)
    // - comment1 (user 1)
    //    - reply 1 (user 3)
    //    - reply 2 (user 4)
    //    - reply 3 (user 5)

    const numUsers = 7;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }

    const newQuestion = await createQuestion({
      createdAt: new Date(2021, 5, 20, 0, 0, 0, 0),
      createdBy: '0',
      text: 'question 1',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    const q1Id = res.body.questions[0]._id;

    console.log('q1Id :', q1Id)

    // User 0 sends like to user 2
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    // User 2 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 2)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 3 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 3)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    // User 5 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 5)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '5',
      });
    expect(res.status).to.equal(200);

    // user 1 comment on question 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        text: 'comment 1',
        parentId: q1Id,
      });
    c1Id = res.body._id;

    // user 2 comment on question 1
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        text: 'comment 2',
        parentId: q1Id,
      });
    c2Id = res.body._id;

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '0');
    expect(res.status).to.equal(200);


    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '0');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[1]._id).to.equal(c1Id);

    console.log(res.body.comments);

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '3')
      .send({
        createdAt: new Date(2021, 5, 23, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 3',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);
    c3Id = res.body._id;


    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '4')
      .send({
        createdAt: new Date(2021, 5, 23, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 4',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);
    c4Id = res.body._id;


    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '5')
      .send({
        createdAt: new Date(2021, 5, 23, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 5',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);
    c5Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '6')
      .send({
        createdAt: new Date(2021, 5, 23, 0, 0, 0, 0),
        questionId: q1Id,
        text: 'Comment 6',
        parentId: c1Id,
      });
    expect(res.status).to.equal(200);
    c6Id = res.body._id;


    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c2Id);
    expect(res.body.comments[1]._id).to.equal(c1Id);
    expect(res.body.comments[1].numComments).to.equal(4);

    console.log('replies on comment 1 :', res.body.comments[1].comments)

    // get c1 children sort by oldest, POV user 1 (friends of user 3 and user 5)
    console.log('c1Id', c1Id)
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: c1Id })
      .query({ sort: 'oldest' })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    console.log('child comment1, POV user 1', res.body.comments)
    expect(res.body.comments.length).to.equal(4);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[1]._id).to.equal(c4Id);
    expect(res.body.comments[2]._id).to.equal(c5Id);
    expect(res.body.comments[3]._id).to.equal(c6Id);

    // get c1 children sort by oldest, POV user 1 (friends of user 3 and user 5)
    console.log('c1Id', c1Id)
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: c1Id })
      .query({ sort: 'oldest' })
      .set('authorization', '5');
    expect(res.status).to.equal(200);
    console.log('child comment1, POV user 5', res.body.comments)
    expect(res.body.comments.length).to.equal(4);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[1]._id).to.equal(c4Id);
    expect(res.body.comments[2]._id).to.equal(c5Id);
    expect(res.body.comments[3]._id).to.equal(c6Id);


  })

  it('should sort comments by friends recent first then recent everyone else', async () => {
    const numUsers = pageSize * 2;
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app)
        .get('/v1/user')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }

    const newQuestion = await createQuestion({
      createdAt: new Date(2021, 5, 20, 0, 0, 0, 0),
      createdBy: '1',
      text: 'question 1',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    const q1Id = res.body.questions[0]._id;

    // User 0 sends like to user 1
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // User 1 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    // User 1 sends like to user 3
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 1)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);

    // User 3 approves the chat request
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 3)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // user 0 comment on question 2
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'User0 has one comment on question2',
        parentId: q1Id,
      });
    // c1 = res.body._id;

    // user 2 comment on question 2
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: q1Id,
        text: 'User2 has one comment on question2',
        parentId: q1Id,
      });
    // c1 = res.body._id;

    // user 3 comment on question 2
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 3)
      .send({
        questionId: q1Id,
        text: 'User3 has one comment on question2',
        parentId: q1Id,
      });

    // user 4 comment on question 2
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 4)
      .send({
        questionId: q1Id,
        text: 'User4 has one comment on question2',
        parentId: q1Id,
      });

    // test pagination with friend sort
    sinon.stub(constants, 'getPageSize').returns(1);

    // first page should contain all friend comments and the first page of non-friend comments
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sort: 'recent' })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0].text).to.equal('User3 has one comment on question2');
    expect(res.body.comments[0].isFriendComment).to.equal(true);
    expect(res.body.comments[1].text).to.equal('User0 has one comment on question2');
    expect(res.body.comments[1].isFriendComment).to.equal(true);
    expect(res.body.comments[2].text).to.equal('User4 has one comment on question2');
    expect(res.body.comments[2].isFriendComment).to.equal(false);

    // second page should contain the next page of non-friend comments
    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sort: 'recent', beforeId })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0].text).to.equal('User2 has one comment on question2');
    expect(res.body.comments[0].isFriendComment).to.equal(false);

    // no more comments
    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id, sort: 'recent', beforeId })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);
  });

  it('hide nested comments', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.13' })
    expect(res.status).to.equal(200);

    /* Testing comment structure

    Question 1
      Comment 3
        Comment 4
        Comment 5
      Comment 2
        Comment 6
      Comment 1
        Comment 7
    */

    const newQuestion = await createQuestion({
      createdAt: new Date(2021, 5, 20, 0, 0, 0, 0),
      createdBy: '0',
      text: 'question 1',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const q1Id = res.body.questions[0]._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: '1',
        parentId: q1Id,
      });
    c1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: '2',
        parentId: q1Id,
      });
    c2Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: '3',
        parentId: q1Id,
      });
    c3Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: '4',
        parentId: c3Id,
      });
    c4Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: '5',
        parentId: c3Id,
      });
    c5Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: '6',
        parentId: c2Id,
      });
    c6Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: '7',
        parentId: c1Id,
      });
    c7Id = res.body._id;

    res = await request(app)
      .get('/v1/comment/v2')
      .query({ parentId: q1Id, sort: 'recent' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[0].comments).to.eql([]);
    expect(res.body.comments[1]._id).to.equal(c2Id);
    expect(res.body.comments[1].comments).to.eql([]);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[2].comments).to.eql([]);

    res = await request(app)
      .get('/v1/comment/v2')
      .query({ parentId: c3Id, sort: 'oldest' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c4Id);
    expect(res.body.comments[1]._id).to.equal(c5Id);
    expect(res.body.hasMore).to.equal(false);

    res = await request(app)
      .get('/v1/comment/v2')
      .query({ parentId: c2Id, sort: 'oldest' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c6Id);
    expect(res.body.hasMore).to.equal(false);

    res = await request(app)
      .get('/v1/comment/context/v2')
      .query({ commentId: c2Id, parentId: q1Id, questionId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[0].comments).to.eql([]);
    expect(res.body.comments[1]._id).to.equal(c2Id);
    expect(res.body.comments[1].comments).to.eql([]);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[2].comments).to.eql([]);

    res = await request(app)
      .get('/v1/comment/context/v2')
      .query({ commentId: c5Id, parentId: c3Id, questionId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[0].comments.length).to.equal(2);
    expect(res.body.comments[0].comments[0]._id).to.equal(c4Id);
    expect(res.body.comments[0].comments[1]._id).to.equal(c5Id);
    expect(res.body.comments[1]._id).to.equal(c2Id);
    expect(res.body.comments[1].comments).to.eql([]);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[2].comments).to.eql([]);

    // test web route - nested comments hidden
    res = await request(app)
      .get('/web/comment')
      .query({ parentId: q1Id, sort: 'recent' })
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[0].comments).to.eql([]);
    expect(res.body.comments[1]._id).to.equal(c2Id);
    expect(res.body.comments[1].comments).to.eql([]);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[2].comments).to.eql([]);

    res = await request(app)
      .get('/web/comment')
      .query({ parentId: c3Id, sort: 'oldest' })
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(2);
    expect(res.body.comments[0]._id).to.equal(c4Id);
    expect(res.body.comments[1]._id).to.equal(c5Id);

    res = await request(app)
      .get('/web/comment')
      .query({ parentId: c2Id, sort: 'oldest' })
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c6Id);

    res = await request(app)
      .get('/web/comment/context/v2')
      .query({ commentId: c2Id, parentId: q1Id, questionId: q1Id })
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[0].comments).to.eql([]);
    expect(res.body.comments[1]._id).to.equal(c2Id);
    expect(res.body.comments[1].comments).to.eql([]);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[2].comments).to.eql([]);

    res = await request(app)
      .get('/web/comment/context/v2')
      .query({ commentId: c5Id, parentId: c3Id, questionId: q1Id })
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(3);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[0].comments.length).to.equal(2);
    expect(res.body.comments[0].comments[0]._id).to.equal(c4Id);
    expect(res.body.comments[0].comments[1]._id).to.equal(c5Id);
    expect(res.body.comments[1]._id).to.equal(c2Id);
    expect(res.body.comments[1].comments).to.eql([]);
    expect(res.body.comments[2]._id).to.equal(c1Id);
    expect(res.body.comments[2].comments).to.eql([]);

    // test pagination
    sinon.stub(constants, 'getPageSize').returns(1);

    res = await request(app)
      .get('/v1/comment/v2')
      .query({ parentId: c3Id, sort: 'oldest' })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c4Id);
    expect(res.body.hasMore).to.equal(true);

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment/v2')
      .query({ parentId: c3Id, sort: 'oldest', beforeId })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c5Id);
    expect(res.body.hasMore).to.equal(false);

    beforeId = res.body.comments[res.body.comments.length - 1]._id;
    res = await request(app)
      .get('/v1/comment/v2')
      .query({ parentId: c3Id, sort: 'oldest', beforeId })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    res = await request(app)
      .get('/v1/comment/context/v2')
      .query({ commentId: c5Id, parentId: c3Id, questionId: q1Id })
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c3Id);
    expect(res.body.comments[0].comments.length).to.equal(2);
    expect(res.body.comments[0].comments[0]._id).to.equal(c4Id);
    expect(res.body.comments[0].comments[1]._id).to.equal(c5Id);
  });

  it('profile preview', async () => {
    for (let i = 0; i < 2; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i)
        .send({ appVersion: '1.13.12' })
      expect(res.status).to.equal(200);
      await createUser(i);
    }

    const profile0 = getProfilePreview(0, true);
    const profile0Nearby = {
      ...profile0,
      nearby: true,
    };

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title',
        language: 'en',
      });
    expect(res.status).to.equal(200);
    expect(res.body.profilePreview).to.eql(profile0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions[0].profilePreview).to.eql(profile0Nearby);
    const q1Id = res.body.questions[0]._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: '1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    expect(res.body.profilePreview).to.eql(profile0);
    c1Id = res.body._id;

    res = await request(app)
      .get('/v1/comment/v2')
      .query({ parentId: q1Id, sort: 'recent' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c1Id);
    expect(res.body.comments[0].profilePreview).to.eql(profile0Nearby);

    // shadow banned user can still see own post, others cannot
    u0 = await User.findById('0');
    u0.shadowBanned = true;
    await u0.save();

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });
});

it('repliedTo user is shadow banned', async () => {
  const numUsers = 3;
  for (let uid = 0; uid < numUsers; uid++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', uid);
    expect(res.status).to.equal(200);
  }

  // load question of day
  newQuestion = await createQuestion({
    createdAt: new Date(2021, 5, 20, 4, 5, 0, 0),
    text: 'qod0',
  });
  await newQuestion.save();
  const q0Id = newQuestion._id.toString();

  // user 0 posts comment
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q0Id,
      text: 'comment0',
      parentId: q0Id,
    });
  expect(res.status).to.equal(200);
  const c0Id = res.body._id;

  // user 1 posts nested comment
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 1)
    .send({
      questionId: q0Id,
      text: 'comment1',
      parentId: c0Id,
    });
  expect(res.status).to.equal(200);
  const c1Id = res.body._id;

  // user 2 replies to user 1
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 2)
    .send({
      questionId: q0Id,
      text: 'comment2',
      parentId: c1Id,
    });
  expect(res.status).to.equal(200);
  const c2Id = res.body._id;

  // user 1 gets banned
  u = await User.findById('1');
  u.shadowBanned = true;
  await u.save();

  // web user should not see c1 or c2
  res = await request(app)
    .get('/web/comment')
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].comments.length).to.equal(0);

  // user 0 should not see c1 or c2
  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 0)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].comments.length).to.equal(0);

  // user 1 should see all comments
  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 1)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].comments.length).to.equal(2);
  expect(res.body.comments[0].comments[0]._id).to.equal(c1Id);
  expect(res.body.comments[0].comments[1]._id).to.equal(c2Id);

  // user 2 should not see c1 or c2
  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 2)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].comments.length).to.equal(0);
});

it('gif in comment', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // load question of day
  newQuestion = await createQuestion({
    createdAt: new Date(2021, 5, 20, 4, 5, 0, 0),
    text: 'qod0',
  });
  await newQuestion.save();
  const q0Id = newQuestion._id.toString();

  // invalid gif
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q0Id,
      text: 'comment0',
      gif: 'yahoo.com',
      parentId: q0Id,
    });
  expect(res.status).to.equal(422);

  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 0)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(0);

  // user 0 posts comment with gif and text
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q0Id,
      text: 'comment0',
      gif: validGif,
      parentId: q0Id,
    });
  expect(res.status).to.equal(200);
  const c0Id = res.body._id;

  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 0)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].text).to.equal('comment0');
  expect(res.body.comments[0].gif).to.equal(validResponseGif);

  // edit and remove the text
  res = await request(app)
    .patch('/v1/comment/edit')
    .set('authorization', 0)
    .send({
      commentId: c0Id,
      text: '',
      gif: validGif,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 0)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].text).to.equal('');
  expect(res.body.comments[0].gif).to.equal(validResponseGif);

    // edit and remove the text
    res = await request(app)
    .patch('/v1/comment/edit')
    .set('authorization', 0)
    .send({
      commentId: c0Id,
      text: '',
      gif: validTenorGif,
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 0)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].text).to.equal('');
  expect(res.body.comments[0].gif).to.equal(validTenorResponseGif);

  // edit and remove the gif
  res = await request(app)
    .patch('/v1/comment/edit')
    .set('authorization', 0)
    .send({
      commentId: c0Id,
      text: 'text',
      gif: '',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 0)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].text).to.equal('text');
  expect(res.body.comments[0].gif).to.equal('');

  // edit and remove text and gif
  res = await request(app)
    .patch('/v1/comment/edit')
    .set('authorization', 0)
    .send({
      commentId: c0Id,
      text: '',
      gif: '',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/comment')
    .set('authorization', 0)
    .query({ parentId: q0Id });
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0]._id).to.equal(c0Id);
  expect(res.body.comments[0].text).to.equal('');
  expect(res.body.comments[0].gif).to.equal('');
});

it('duplicate text in comment', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // load question of day
  newQuestion = await createQuestion({
    createdAt: new Date(2021, 5, 20, 4, 5, 0, 0),
    text: 'qod0',
  });
  await newQuestion.save();
  const q0Id = newQuestion._id.toString();

  // user 0 posts comment
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q0Id,
      text: 'text',
      parentId: q0Id,
    });
  expect(res.status).to.equal(200);

  // duplicate
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q0Id,
      text: 'text',
      parentId: q0Id,
    });
  expect(res.status).to.equal(200);

  comments = await Comment.find({ banned: true });
  expect(comments.length).to.equal(1);
});

it('duplicate gif in comment', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // load question of day
  newQuestion = await createQuestion({
    createdAt: new Date(2021, 5, 20, 4, 5, 0, 0),
    text: 'qod0',
  });
  await newQuestion.save();
  const q0Id = newQuestion._id.toString();

  // user 0 posts comment with gif and no text
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q0Id,
      gif: validGif,
      parentId: q0Id,
    });
  expect(res.status).to.equal(200);

  // different gif
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q0Id,
      gif: 'https://media2.giphy.com/media/KEBNHvaPPJ4LURsdEf/giphy.gif',
      parentId: q0Id,
    });
  expect(res.status).to.equal(200);

  comments = await Comment.find({ banned: true });
  expect(comments.length).to.equal(0);

    // check comment
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
expect(res.body.comments[0].gif).to.equal('https://media2.giphy.com/media/KEBNHvaPPJ4LURsdEf/giphy.gif');


  // duplicate gif
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q0Id,
      gif: 'https://media2.giphy.com/media/KEBNHvaPPJ4LURsdEf/giphy.gif',
      parentId: q0Id,
    });
  expect(res.status).to.equal(200);

  comments = await Comment.find({ banned: true });
  expect(comments.length).to.equal(1);
});

describe('image in comment', async () => {
  let q0Id; let c0Id; let
    img0Id;

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // load question of day
    newQuestion = await createQuestion({
      createdAt: new Date(2021, 5, 20, 4, 5, 0, 0),
      text: 'qod0',
    });
    await newQuestion.save();
    q0Id = newQuestion._id.toString();

    // user 0 posts empty comment
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q0Id,
        parentId: q0Id,
      });
    expect(res.status).to.equal(200);
    c0Id = res.body._id;

    // add image to comment
    res = await request(app)
      .post('/v1/comment/image')
      .set('authorization', 0)
      .query({ commentId: c0Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    assert(res.body.image.includes(`comments/${c0Id}/`));
    img0Id = res.body.image;

    // check comment
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].image).to.equal(img0Id);
    expect(res.body.comments[0].isEdited).to.equal(false);
  });

  it('edit image', async () => {
    res = await request(app)
      .post('/v1/comment/image')
      .set('authorization', 0)
      .query({ commentId: c0Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    assert(res.body.image.includes(`comments/${c0Id}/`));
    const img1Id = res.body.image;

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].image).to.equal(img1Id);
    expect(res.body.comments[0].isEdited).to.equal(true);
  });

  it('replace image with gif', async () => {
    res = await request(app)
      .patch('/v1/comment/edit')
      .set('authorization', 0)
      .send({
        commentId: c0Id,
        text: '',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal(validResponseGif);
    expect(res.body.comments[0].image).to.equal();
    expect(res.body.comments[0].isEdited).to.equal(true);

    // now replace gif with image
    res = await request(app)
      .post('/v1/comment/image')
      .set('authorization', 0)
      .query({ commentId: c0Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    assert(res.body.image.includes(`comments/${c0Id}/`));
    const img1Id = res.body.image;

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].image).to.equal(img1Id);
    expect(res.body.comments[0].isEdited).to.equal(true);
  });

  it('delete image', async () => {
    res = await request(app)
      .delete('/v1/comment/image')
      .set('authorization', 0)
      .query({ commentId: c0Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].image).to.equal();
    expect(res.body.comments[0].isEdited).to.equal(true);
  });

  it('other user cannot edit or delete image', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/comment/image')
      .set('authorization', 1)
      .query({ commentId: c0Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(404);

    res = await request(app)
      .delete('/v1/comment/image')
      .set('authorization', 1)
      .query({ commentId: c0Id });
    expect(res.status).to.equal(404);

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].image).to.equal(img0Id);
    expect(res.body.comments[0].isEdited).to.equal(false);
  });

  it('duplicate detection', async () => {
    comments = await Comment.find({ banned: true });
    expect(comments.length).to.equal(0);

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q0Id,
        parentId: q0Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment/image')
      .set('authorization', 0)
      .query({ commentId: c1Id })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    comments = await Comment.find({ banned: true });
    expect(comments.length).to.equal(0);
  });
});

describe('audio in comment', async () => {
  let q0Id; let c0Id; let
    audio0Id;

  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // load question of day
    newQuestion = await createQuestion({
      createdAt: new Date(2021, 5, 20, 4, 5, 0, 0),
      text: 'qod0',
    });
    await newQuestion.save();
    q0Id = newQuestion._id.toString();

    // user 0 posts empty comment
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q0Id,
        parentId: q0Id,
      });
    expect(res.status).to.equal(200);
    c0Id = res.body._id;

    // add audio to comment
    res = await request(app)
      .post('/v1/comment/audio')
      .set('authorization', 0)
      .query({ commentId: c0Id })
      .attach('audio', validAudioPath)
      .field({ waveform: JSON.stringify([1.3, 1.5]), duration: 1.5 });
    expect(res.status).to.equal(200);
    assert(res.body.audio.includes(`comments/${c0Id}/`));
    audio0Id = res.body.audio;

    // check comment
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].audio).to.equal(audio0Id);
    expect(res.body.comments[0].audioWaveform).to.eql([1.3, 1.5]);
    expect(res.body.comments[0].audioDuration).to.equal(1.5);
    expect(res.body.comments[0].isEdited).to.equal(false);
  });

  it('edit audio', async () => {
    res = await request(app)
      .post('/v1/comment/audio')
      .set('authorization', 0)
      .query({ commentId: c0Id })
      .attach('audio', validAudioPath);
    expect(res.status).to.equal(200);
    assert(res.body.audio.includes(`comments/${c0Id}/`));
    const audio1Id = res.body.audio;

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].audio).to.equal(audio1Id);
    expect(res.body.comments[0].isEdited).to.equal(true);
  });

  it('add gif', async () => {
    res = await request(app)
      .patch('/v1/comment/edit')
      .set('authorization', 0)
      .send({
        commentId: c0Id,
        text: '',
        gif: validGif,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal(validResponseGif);
    expect(res.body.comments[0].audio).to.equal(audio0Id);
    expect(res.body.comments[0].isEdited).to.equal(true);

    // now edit audio
    res = await request(app)
      .post('/v1/comment/audio')
      .set('authorization', 0)
      .query({ commentId: c0Id })
      .attach('audio', validAudioPath);
    expect(res.status).to.equal(200);
    assert(res.body.audio.includes(`comments/${c0Id}/`));
    const audio1Id = res.body.audio;

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal(validResponseGif);
    expect(res.body.comments[0].audio).to.equal(audio1Id);
    expect(res.body.comments[0].isEdited).to.equal(true);
  });

  it('delete audio', async () => {
    res = await request(app)
      .delete('/v1/comment/audio')
      .set('authorization', 0)
      .query({ commentId: c0Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].audio).to.equal();
    expect(res.body.comments[0].isEdited).to.equal(true);
  });

  it('other user cannot edit or delete audio', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/comment/audio')
      .set('authorization', 1)
      .query({ commentId: c0Id })
      .attach('audio', validAudioPath);
    expect(res.status).to.equal(404);

    res = await request(app)
      .delete('/v1/comment/audio')
      .set('authorization', 1)
      .query({ commentId: c0Id });
    expect(res.status).to.equal(404);

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: q0Id });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);
    expect(res.body.comments[0]._id).to.equal(c0Id);
    expect(res.body.comments[0].text).to.equal('');
    expect(res.body.comments[0].gif).to.equal();
    expect(res.body.comments[0].audio).to.equal(audio0Id);
    expect(res.body.comments[0].isEdited).to.equal(false);
  });

  it('duplicate detection', async () => {
    comments = await Comment.find({ banned: true });
    expect(comments.length).to.equal(0);

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q0Id,
        parentId: q0Id,
      });
    expect(res.status).to.equal(200);
    c1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment/audio')
      .set('authorization', 0)
      .query({ commentId: c1Id })
      .attach('audio', validAudioPath);
    expect(res.status).to.equal(200);

    comments = await Comment.find({ banned: true });
    expect(comments.length).to.equal(0);
  });
});

describe('banned keywords', async () => {
  let qId;

  beforeEach(async () => {
    // create two users
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // post question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title',
        language: 'ja',
      });
    expect(res.status).to.equal(200);
    qId = res.body._id;
  });

  it('translated keywords', async () => {
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: qId,
        parentId: qId,
        text: 'なに！くそ！',
      });
    expect(res.status).to.equal(200);

    // user 1 can see the comment, user 0 cannot
    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 1)
      .query({ parentId: qId });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);

    res = await request(app)
      .get('/v1/comment')
      .set('authorization', 0)
      .query({ parentId: qId });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // post question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'Bonjour',
        language: 'fr',
      });
    expect(res.status).to.equal(200);
    qId = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: qId,
        parentId: qId,
        text: 'Il y en a un qui en a fait les frais 😆\nMais rien ne fait plus mal que marcher sur un dé 4 😅',
      });
    expect(res.status).to.equal(200);

    let comment = await Comment.findById(res.body._id);
    expect(comment.banned).to.equal(true);
    expect(comment.bannedReason).to.equal('keyword: mal');

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: qId,
        parentId: qId,
        text: 'et bon appétit',
      });
    expect(res.status).to.equal(200);

    comment = await Comment.findById(res.body._id);
    expect(comment.banned).to.equal();

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 2)
      .send({
        questionId: qId,
        parentId: qId,
        text: 'Das ist ganz einfach. Ich bin als kleiner Stöpsel mit Pokémon aufgewachsen und hatte damals bei RTL 2 u.a. diesen Anime  gesehen. Da gab es dann auch Yu-Gi-Oh! und Detektiv Conan und gerade letzterer begeistert mich extrem. Deshalb hatte ich das Interesse nie verloren. Heute schaue ich noch immer regelmäßig und liebend gerne Anime-Serien, vor allem, da sie wirklich für unterschiedliche Altersgruppen verfügbar sind.',
      });
    expect(res.status).to.equal(200);

    comment = await Comment.findById(res.body._id);
    expect(comment.banned).to.equal();

    // post question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 2)
      .send({
        interestName: 'chess',
        title: 'Test post',
        language: 'en',
      });
    expect(res.status).to.equal(200);
    qId = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: qId,
        parentId: qId,
        text: `If a convo is not vulgar than I don't know what a good convo is.. cunt!`,
      });
    expect(res.status).to.equal(200);

    comment = await Comment.findById(res.body._id);
    expect(comment.banned).to.equal(true);
    expect(comment.bannedReason).to.equal('keyword: cunt');
  });
});

describe('profile comments', async () => {
  function getMockProfile(index, createdBy) {
    return {
      id: index,
      createdBy,
      horoscope:'Scorpio',
      mbti:'ENFP',
      enneagram:'2w1',
      name: `profile${index}`,
      description: `desc${index}`,
      subcategories: [1, 2],
      image: `database/profiles/${index}`,
      imageSource: `mockSource${index}`,
    };
  }

  let dbProfiles = [];
  let userProfiles = [];
  const voteMap = {};
  beforeEach(async () => {
    // create two mock profiles
    dbProfiles = [];
    userProfiles = [];
    for (let i = 1; i <= 2; i++) {
      await initApp(i);
      // user 1 creates 2 mock profiles
      await (new Profile(getMockProfile(i, 1))).save();
      const dbProfile = await getWebDbProfile(i);
      dbProfiles.push(dbProfile);
    }
    await Category.create({
      id: 1,
      name: 'test',
      slug: 'test',
    });
    await Subcategory.create({
      id: 1,
      name: 'test',
      slug: 'test',
      category: 1,
    });
    await Subcategory.create({
      id: 2,
      name: 'test2',
      slug: 'test2',
      category: 1,
    });
    await databaseLib.downloadCategoriesToFiles();
    await databaseLib.loadCategoriesFromDatabase();
  });

  const postComment = (userId, params) => request(app)
    .post('/v1/comment')
    .set('authorization', userId)
    .send(params);

  it('post comment - profile comment', async () => {
    res = await initApp(1, { appVersion: '1.11.59' });// upgrade to 1.11.59
    res=await fetchCoinData(1);
    expect(res.profileCommentReward).to.eql(undefined);
    res = await initApp(1, { appVersion: '1.11.63' });// upgrade to 1.11.63
    res=await fetchCoinData(1);
    let coins=res.coins;//get coins inititally.
    expect(res.rewards.profileCommentReward).to.eql({ reward: coinsConstants.profileCommentReward, received: 0 });
    res = await getUserProfile(1, { user: 1 });
    userProfiles.push(res.user);

    // no text or vote
    res = await postComment(1, {
      parentId: dbProfiles[0]._id,
    });
    expect(res.status).to.equal(422);

    res = await postComment(1, {
      parentId: dbProfiles[0]._id,
      vote: {},
    });
    expect(res.status).to.equal(422);

    {
      const keys = ['mbti', 'enneagram', 'horoscope'];
      for (let i = 0; i < keys.length; i++) {
        res = await postComment(1, {
          parentId: dbProfiles[0]._id,
          vote: {
            [keys[i]]: 'abc',
          },
        });
        expect(res.status).to.equal(422);
      }
    }

    const socket1 = await initSocket(1);
    let socketPromise=getSocketPromise(socket1,'coin reward');
    // success (only text)
    let afterDate = new Date();
    res = await postComment(1, {
      parentId: dbProfiles[0]._id,
      text: 'text1',
    });
    let beforeDate = new Date();

    expect(res.status).to.equal(200);
    let comment = res.body;
    comment.isFriendComment = false;

    expect(comment._id).to.be.a('string');
    expect(comment.createdBy).to.deep.equal(userProfiles[0]);
    expect(new Date(comment.createdAt)).to.be.greaterThan(afterDate);
    expect(new Date(comment.createdAt)).to.be.lessThan(beforeDate);
    expect(comment.profile).to.eql(dbProfiles[0]._id.valueOf());
    expect(comment.text).to.eql('text1');
    expect(comment.vote).to.eql();
    expect(comment.parent).to.eql(dbProfiles[0]._id.valueOf());
    expect(comment.repliedTo).to.eql(null);
    expect(comment.depth).to.eql(1);
    expect(comment.numComments).to.eql(0);
    expect(comment.numLikes).to.eql(0);
    expect(comment.isDeleted).to.eql(false);
    expect(comment.isEdited).to.eql(false);
    expect(comment.hasUserLiked).to.eql(false);
    expect(comment.comments).to.eql([]);
    expect(comment.postRepliedTo).to.eql(dbProfiles[0]._id.valueOf());

    res = await getSingleCommentById(1, comment._id);
    expect(JSON.stringify(res)).to.eql(JSON.stringify(comment));
    res = await getComments(1, { profileId: dbProfiles[0]._id.valueOf() });
    expect(res.comments.length).to.eql(1);
    expect(JSON.stringify(res.comments[0])).to.eql(JSON.stringify(comment));

    const allComments = [];
    allComments.push(comment);

    let profile = await getWebDbProfile(1);
    expect(omit(profile, 'lastUpdated')).to.eql(omit(dbProfiles[0], 'lastUpdated'));
    expect(new Date(dbProfiles[0].lastUpdated)).to.be.lessThan(new Date(profile.lastUpdated));

    //coin reward for 1st comment on the profile.
    coins+=coinsConstants.profileCommentReward
    expect(await socketPromise).to.eql({
      caption: 'First comment on a personality type!',
      rewardAmount: coinsConstants.profileCommentReward,
      newTotal: coins,
    });

    res=await fetchCoinData(1);
    expect(res.rewards.profileCommentReward).to.eql({ reward: coinsConstants.profileCommentReward, received: coinsConstants.profileCommentReward });
    expect(res.coins).to.eql(coins);
    await destroySocket(socket1);

    // success (only vote)

    afterDate = new Date();
    res = await postComment(1, {
      parentId: dbProfiles[0]._id,
      vote: { mbti: 'INTJ' },
    });
    beforeDate = new Date();
    expect(res.status).to.equal(200);
    comment = res.body;
    comment.isFriendComment = false;
    expect(comment._id).to.be.a('string');
    expect(comment.createdBy).to.eql(userProfiles[0]);
    expect(new Date(comment.createdAt)).to.be.greaterThan(afterDate);
    expect(new Date(comment.createdAt)).to.be.lessThan(beforeDate);
    expect(comment.profile).to.eql(dbProfiles[0]._id.valueOf());
    expect(comment.text).to.eql('');
    expect(comment.vote).to.eql({ mbti: 'INTJ' });
    expect(comment.parent).to.eql(dbProfiles[0]._id.valueOf());
    expect(comment.repliedTo).to.eql(null);
    expect(comment.depth).to.eql(1);
    expect(comment.numComments).to.eql(0);
    expect(comment.numLikes).to.eql(0);
    expect(comment.isDeleted).to.eql(false);
    expect(comment.isEdited).to.eql(false);
    expect(comment.hasUserLiked).to.eql(false);
    expect(comment.comments).to.eql([]);
    expect(comment.postRepliedTo).to.eql(dbProfiles[0]._id.valueOf());

    res = await getSingleCommentById(1, comment._id);
    //karma awarded to user 1 on first vote
    comment.createdBy.karma+=1;
    allComments[0].createdBy.karma+=1;
    expect(JSON.stringify(res)).to.eql(JSON.stringify(comment));

    res = await getComments(1, { profileId: dbProfiles[0]._id.valueOf() });
    expect(res.comments.length).to.eql(2);
    expect(JSON.stringify(res.comments[0])).to.eql(JSON.stringify(comment));
    expect(JSON.stringify(res.comments[1])).to.eql(JSON.stringify(allComments[0]));
    allComments.push(comment);

    coins+=1;//1 coin reward for reaching karma tier
    res=await fetchCoinData(1);
    expect(res.rewards.profileCommentReward).to.eql({ reward: coinsConstants.profileCommentReward, received: coinsConstants.profileCommentReward });
    expect(res.coins).to.eql(coins);

    // web route for comments should also contain vote data
    let commentWithProfilePreview = JSON.parse(JSON.stringify(comment));
    delete commentWithProfilePreview.createdBy;
    commentWithProfilePreview.profilePreview = {"_id":"1","firstName":"","personality":{},"karma":1,"age":null,handle:userProfiles[0].handle};
    res = await request(app)
    .get('/web/comment')
    .query({ profileId: dbProfiles[0]._id.valueOf() });
    expect(res.status).to.equal(200);
    expect(res.body.comments[0]).to.eql(commentWithProfilePreview);

    voteMap[0] = {
      totalCount: 1,
      mbti: { INTJ: 1 },
    };
    dbProfiles[0].vote = voteMap[0];

    //update mbti to most voted
    dbProfiles[0].mbti='INTJ';
    profile = await getWebDbProfile(1);
    //after 1 vote for INTJ without pdb score the mbti has changed to 100*0.25= 25% from 4% so total also updated
    dbProfiles[0].confidence_score={ mbti: 25, enneagram: 2, horoscope: 4, total: 10 }
    expect(omit(profile, 'lastUpdated')).to.eql(omit(dbProfiles[0], 'lastUpdated'));
    expect(new Date(dbProfiles[0].lastUpdated)).to.be.lessThan(new Date(profile.lastUpdated));
    res = await initApp(2, { appVersion: '1.11.63' });// upgrade to 1.11.59
    res = await getUserProfile(1, { user: 2 });
    userProfiles.push(res.user);

    // user 2 posts with text and vote
    afterDate = new Date();
    res = await postComment(2, {
      parentId: dbProfiles[0]._id,
      text: 'text2',
      vote: {
        mbti: 'INTJ',
        horoscope: 'Gemini',
      },
    });
    beforeDate = new Date();
    expect(res.status).to.equal(200);
    comment = res.body;
    expect(comment._id).to.be.a('string');
    expect(comment.createdBy).to.eql(userProfiles[1]);
    expect(new Date(comment.createdAt)).to.be.greaterThan(afterDate);
    expect(new Date(comment.createdAt)).to.be.lessThan(beforeDate);
    expect(comment.profile).to.eql(dbProfiles[0]._id.valueOf());
    expect(comment.text).to.eql('text2');
    expect(comment.vote).to.eql({
      mbti: 'INTJ',
      horoscope: 'Gemini',
    });
    expect(comment.parent).to.eql(dbProfiles[0]._id.valueOf());
    expect(comment.repliedTo).to.eql(null);
    expect(comment.depth).to.eql(1);
    expect(comment.numComments).to.eql(0);
    expect(comment.numLikes).to.eql(0);
    expect(comment.isDeleted).to.eql(false);
    expect(comment.isEdited).to.eql(false);
    expect(comment.hasUserLiked).to.eql(false);
    expect(comment.comments).to.eql([]);
    expect(comment.postRepliedTo).to.eql(dbProfiles[0]._id.valueOf());

    res = await getSingleCommentById(1, comment._id);
    //karma awarded to user 2 on first vote
    comment.createdBy.karma += 1;
    expect(JSON.stringify(res)).to.eql(JSON.stringify(comment));

    res = await getComments(1, { profileId: dbProfiles[0]._id.valueOf() });
    expect(res.comments.length).to.eql(3);
    expect(JSON.stringify(res.comments[0])).to.eql(JSON.stringify(comment));
    expect(JSON.stringify(res.comments[1])).to.eql(JSON.stringify(allComments[1]));
    expect(JSON.stringify(res.comments[2])).to.eql(JSON.stringify(allComments[0]));

    voteMap[0] = {
      totalCount: 2,
      mbti: {
        INTJ: 2,
      },
      horoscope: {
        Gemini: 1,
      },
    };
    dbProfiles[0].vote = voteMap[0];
    profile = await getWebDbProfile(1);

    //update horoscope to most voted
    dbProfiles[0].horoscope='Gemini';
    //after 1 vote for INTJ without pdb score the mbti has changed to 100*0.25= 25% from 25% so total also updated
    //after 1 vote for Gemini without pdb score the horoscope has changed to 100*0.25= 25% from 4% so total also updated
    dbProfiles[0].confidence_score={ mbti: 25, enneagram: 2, horoscope: 25, total: 17 }
    res = await initApp(2, { appVersion: '1.11.63' });// upgrade to 1.11.59
    expect(omit(profile, 'lastUpdated')).to.eql(omit(dbProfiles[0], 'lastUpdated'));
    expect(new Date(dbProfiles[0].lastUpdated)).to.be.lessThan(new Date(profile.lastUpdated));

    // the vote count is only updated for those replaced
    // and does not remove the ones not sent

    res = await postComment(2, {
      parentId: dbProfiles[0]._id,
      text: 'text3',
      vote: {
        mbti: 'INFP',
        horoscope: 'Capricorn',
        enneagram:'1w2',
      },
    });
    expect(res.status).to.eql(200);

    await new Promise((r) => setTimeout(r, 50));

    voteMap[0] = {
      totalCount: 2,
      mbti: {
        INTJ: 1,
        INFP: 1,
      },
      horoscope: {
        Capricorn: 1,
        Gemini: 0,
      },
      enneagram:{
        '1w2':1,
      }
    };
    dbProfiles[0].vote = voteMap[0];

    //updated to most voted data
    dbProfiles[0].horoscope='Capricorn';
    dbProfiles[0].enneagram='1w2';
    profile = await getWebDbProfile(1);
    console.log(profile,dbProfiles[0])
    //after 1 vote for 1w2 without pdb score the enneagram has changed to 100*0.25= 25% from 4% so total also updated
    //after equal vote for INTJ and INFP and without pdb score the mbti has changed to 50*0.25= 13% from 25% also updated
    //after vote change for horoscope from Gemini:0 and Capricorn:1 and without pdb score the horoscope has changed to 100*0.25= 25% from 25% also updated
    dbProfiles[0].confidence_score={ mbti: 13, enneagram: 25, horoscope: 25, total: 21 }
    expect(omit(profile, 'lastUpdated')).to.eql(omit(dbProfiles[0], 'lastUpdated'));
    expect(new Date(dbProfiles[0].lastUpdated)).to.be.lessThan(new Date(profile.lastUpdated));

    // profile comments should not be returned in /v1/user/comments
    res = await request(app)
      .get('/v1/user/comments')
      .set('authorization', 2)
      .query({ createdBy: 1 });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // make user 2 an admin - profile comments should still not be returned
    user = await User.findOne({ _id: '2' });
    user.admin = true;
    await user.save();

    res = await request(app)
      .get('/v1/user/comments')
      .set('authorization', 2)
      .query({ createdBy: 1 });
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    res = await fetchCoinData(1);
    expect(res.rewards.profileCommentReward).to.eql({ reward: coinsConstants.profileCommentReward, received: coinsConstants.profileCommentReward });
    expect(res.coins).to.eql(coins);
    //no reward received because user 2 wasn't the first to post
    res = await fetchCoinData(2);
    expect(res.rewards.profileCommentReward).to.eql({ reward: coinsConstants.profileCommentReward, received: 0 });
    expect(res.coins).to.eql(coins - coinsConstants.profileCommentReward);

    //default values saved in their respective keys
    const profileData=await Profile.findOne({_id:dbProfiles[0]});
    expect(profileData.def_mbti).to.eql('ENFP');
    expect(profileData.def_enneagram).to.eql('2w1');
    expect(profileData.def_horoscope).to.eql('Scorpio');
  });

  it('vote with null values', async () => {
    res = await postComment(1, {
      parentId: dbProfiles[0]._id,
      text: 'text1',
      vote: { mbti: null },
    });
    expect(res.status).to.equal(200);
    const comment = res.body;
    expect(comment.vote).to.eql();
  });

  it('update descriptions after 5 votes - without intros fields or translated languages', async () => {
    doc = await Profile.findById(dbProfiles[0]._id);
    expect(doc.intros).to.eql();
    expect(doc.personalityDescriptions).to.eql();

    for (let i = 0; i < 5; i++) {
      await initApp(i);
      res = await postComment(i, {
        parentId: dbProfiles[0]._id,
        text: 'text1',
        vote: { mbti: 'ISTJ' },
      });
      expect(res.status).to.equal(200);
    }
    await new Promise((r) => setTimeout(r, 50));
    doc = await Profile.findById(dbProfiles[0]._id);
    console.log(doc);
    expect(doc.intros).to.eql({ en: { mbti: 'mock response' } });
    expect(doc.personalityDescriptions).to.eql({ ISTJ: { en: 'mock response' } });
  });

  it('update descriptions after 5 votes - with intros fields', async () => {
    profile = await Profile.findById(dbProfiles[0]._id);
    profile.translatedLanguages = ['en', 'es'];
    profile.intros = {
      en: {
        mbti: 'English ENFP description',
      },
      es: {
        mbti: 'Spanish ENFP description',
      },
    };
    await profile.save();

    for (let i = 0; i < 5; i++) {
      await initApp(i);
      res = await postComment(i, {
        parentId: dbProfiles[0]._id,
        text: 'text1',
        vote: { mbti: 'ISTJ' },
      });
      expect(res.status).to.equal(200);
    }

    await new Promise((r) => setTimeout(r, 50));
    profile = await Profile.findById(dbProfiles[0]._id);
    console.log(profile);
    expect(profile.mbti).to.equal('ISTJ');
    expect(profile.intros).to.eql({ en: { mbti: 'mock response' }, es: { mbti: 'mock response' } });
    expect(profile.personalityDescriptions).to.eql({
      ENFP: { en: 'English ENFP description', es: 'Spanish ENFP description' },
      ISTJ: { en: 'mock response', es: 'mock response' }
    });
    expect(profile.currentDescriptionTypes.mbti).to.equal('ISTJ');
    expect(profile.currentDescriptionTypes.enneagram).to.equal();
    expect(profile.currentDescriptionTypes.zodiac).to.equal();

    // change votes back to ENFP
    for (let i = 0; i < 5; i++) {
      res = await postComment(i, {
        parentId: dbProfiles[0]._id,
        text: 'text1',
        vote: { mbti: 'ENFP' },
      });
      expect(res.status).to.equal(200);
    }

    profile = await Profile.findById(dbProfiles[0]._id);
    console.log(profile);
    expect(profile.mbti).to.equal('ENFP');
    expect(profile.intros).to.eql({ en: { mbti: 'English ENFP description' }, es: { mbti: 'Spanish ENFP description' } });
    expect(profile.personalityDescriptions).to.eql({
      ENFP: { en: 'English ENFP description', es: 'Spanish ENFP description' },
      ISTJ: { en: 'mock response', es: 'mock response' }
    });
    expect(profile.currentDescriptionTypes.mbti).to.equal('ENFP');
    expect(profile.currentDescriptionTypes.enneagram).to.equal();
    expect(profile.currentDescriptionTypes.zodiac).to.equal();

    // add a new translated language
    profile.translatedLanguages = ['en', 'es', 'ja'];
    await profile.save();

    for (let i = 0; i < 5; i++) {
      res = await postComment(i, {
        parentId: dbProfiles[0]._id,
        text: 'text1',
        vote: { mbti: 'ISTJ' },
      });
      expect(res.status).to.equal(200);
    }

    profile = await Profile.findById(dbProfiles[0]._id);
    console.log(profile);
    expect(profile.mbti).to.equal('ISTJ');
    expect(profile.intros).to.eql({ en: { mbti: 'mock response' }, es: { mbti: 'mock response' }, ja: { mbti: 'mock response' } });
    expect(profile.personalityDescriptions).to.eql({
      ENFP: { en: 'English ENFP description', es: 'Spanish ENFP description' },
      ISTJ: { en: 'mock response', es: 'mock response', ja: 'mock response' }
    });
    expect(profile.currentDescriptionTypes.mbti).to.equal('ISTJ');
    expect(profile.currentDescriptionTypes.enneagram).to.equal();
    expect(profile.currentDescriptionTypes.zodiac).to.equal();
  });
});

it('post comment to web page', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // user 0 posts comments
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      webPageUrl: '/infp',
      text: 'infp',
    });
  expect(res.status).to.equal(200);
  let c1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      webPageUrl: '/entj',
      text: 'entj',
    });
  expect(res.status).to.equal(200);

  expect((await WebPage.findOne({url: '/infp'})).numComments).to.equal(1);
  expect((await WebPage.findOne({url: '/entj'})).numComments).to.equal(1);

  // get comment
  console.log((await Comment.find()))
  res = await request(app)
    .get('/v1/comment')
    .query({ webPageUrl: '/infp' })
    .set('authorization', '0');
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].text).to.equal('infp');
  expect(res.body.comments[0].depth).to.equal(1);
  expect(res.body.comments[0].numComments).to.equal(0);

  res = await request(app)
    .get('/v1/comment')
    .query({ webPageUrl: '/entj' })
    .set('authorization', '0');
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].text).to.equal('entj');

  res = await request(app)
    .get('/v1/comment')
    .query({ webPageUrl: '/invalid' })
    .set('authorization', '0');
  expect(res.status).to.equal(404);

  // user 0 posts reply
  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      parentId: c1Id,
      text: 'infp2',
    });
  expect(res.status).to.equal(200);

  expect((await WebPage.findOne({url: '/infp'})).numComments).to.equal(2);
  expect((await WebPage.findOne({url: '/entj'})).numComments).to.equal(1);

  res = await request(app)
    .get('/v1/comment')
    .query({ webPageUrl: '/infp' })
    .set('authorization', '0');
  expect(res.status).to.equal(200);
  expect(res.body.comments.length).to.equal(1);
  expect(res.body.comments[0].numComments).to.equal(1);
  expect(res.body.comments[0].comments.length).to.equal(1);
  expect(res.body.comments[0].comments[0].text).to.equal('infp2');
  expect(res.body.comments[0].comments[0].depth).to.equal(2);
});

describe('language detection', async () => {
  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.5' })
    expect(res.status).to.equal(200);
  });

  it('check language - mismatch', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'da',
        checkLanguage: false,
      });
    expect(res.status).to.equal(200);
    let q1Id = res.body._id;

    // language mismatch
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'normal english text text',
        parentId: q1Id,
        checkLanguage: true,
      });
    expect(res.status).to.equal(409);

    comment = await Comment.find();
    expect(comment.length).to.equal(0);

    // post anyway
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'normal english text text',
        parentId: q1Id,
        checkLanguage: false,
      });
    expect(res.status).to.equal(200);

    comment = await Comment.findById(res.body._id);
    expect(comment.language).to.equal('da');
    expect(comment.detectedLanguage).to.equal('en');
    expect(comment.languageMismatch).to.equal(true);
    expect(comment.detectedLanguageConfidence).to.equal(1);
    expect(comment.banned).to.equal(true);

    mismatches = await LanguageMismatch.find();
    expect(mismatches.length).to.equal(1);
    mismatch = mismatches[0];
    expect(mismatch.user).to.equal('0');
    expect(mismatch.type).to.equal('comment');
    expect(mismatch.title).to.equal();
    expect(mismatch.text).to.equal('normal english text text');
    expect(mismatch.targetLanguage).to.equal('da');
    expect(mismatch.detectedLanguage).to.equal('en');
    expect(mismatch.confidence).to.equal(1);
  });

  it('check language - no mismatch', async () => {
    googleTranslate.detect.restore();
    sinon.stub(googleTranslate, 'detect').callsFake(async (params) => {
      return [ {
        confidence: 1,
        language: 'da',
      } ];
    });

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'da',
        checkLanguage: false,
      });
    expect(res.status).to.equal(200);
    let q1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'normal da text text text',
        parentId: q1Id,
        checkLanguage: true,
      });
    expect(res.status).to.equal(200);

    comment = await Comment.findById(res.body._id);
    expect(comment.language).to.equal('da');
    expect(comment.detectedLanguage).to.equal('da');
    expect(comment.languageMismatch).to.equal(false);
    expect(comment.detectedLanguageConfidence).to.equal(1);
    expect(comment.banned).to.equal();
  });

  it('no check for popular languages', async () => {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'title title',
        text: 'title title',
        language: 'es',
        checkLanguage: false,
      });
    expect(res.status).to.equal(200);
    let q1Id = res.body._id;

    // language check skipped
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        text: 'normal english text text',
        parentId: q1Id,
        checkLanguage: true,
      });
    expect(res.status).to.equal(200);

    comment = await Comment.findById(res.body._id);
    expect(comment.language).to.equal('es');
    expect(comment.detectedLanguage).to.equal();
    expect(comment.languageMismatch).to.equal();
    expect(comment.detectedLanguageConfidence).to.equal();
    expect(comment.banned).to.equal();
  });

});

it('ban on comment report', async () => {
  setMockPromptResponse(JSON.stringify({ ban: true, explanation: 'spam' }));

  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'chess',
      title: 'first post',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: 'comment',
    });
  expect(res.status).to.equal(200);
  c1Id = res.body._id;

  res = await request(app)
    .patch('/v1/comment/report')
    .set('authorization', 1)
    .send({ commentId: c1Id, reason: ['Spam'], explanation: 'seems like spam' });
  expect(res.status).to.equal(200);

  post = await Comment.findById(c1Id);
  expect(post.banned).to.equal(true);

  // admin checks reports
  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  res = await request(app)
    .get('/v1/admin/user/postReports')
    .set('authorization', 0)
    .query({ user: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.reports.length).to.equal(1);
  console.log(res.body.reports[0]);
  expect(res.body.reports[0].reportedComment).to.equal(c1Id);
  expect(res.body.reports[0].parentQuestion).to.equal(q1Id);
  expect(res.body.reports[0].openaiBan).to.equal(true);
  expect(res.body.reports[0].openaiExplanation).to.equal('spam');
});

it('shadow ban if 10 comments within 10 different post threads are banned within 30 days', async () => {
  setMockPromptResponse(JSON.stringify({ ban: true, explanation: 'spam' }));

  clock = sinon.useFakeTimers();

  await initApp(0);
  await initApp(1);
  await initApp(2);

  // 10 comments on the same post
  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'chess',
      title: 'first post',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  for (let i = 0; i < 10; i++) {
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        parentId: q1Id,
        text: i.toString(),
      });
    expect(res.status).to.equal(200);
    id = res.body._id;

    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: id, reason: ['Spam'], explanation: 'seems like spam' });
    expect(res.status).to.equal(200);

    post = await Comment.findById(id);
    expect(post.banned).to.equal(true);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
  }

  // 5 comments on different posts
  for (let i = 20; i < 25; i++) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: i.toString(),
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        parentId: q1Id,
        text: i.toString(),
      });
    expect(res.status).to.equal(200);
    id = res.body._id;

    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 2)
      .send({ commentId: id, reason: ['Spam'], explanation: 'seems like spam' });
    expect(res.status).to.equal(200);

    post = await Comment.findById(id);
    expect(post.banned).to.equal(true);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
  }

  // 35 days
  clock.tick(35 * 24 * 3600 * 1000);

  // 10 comments on different posts - user banned on the last
  for (let i = 30; i < 40; i++) {
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'chess',
        title: i.toString(),
      });
    expect(res.status).to.equal(200);
    q1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        parentId: q1Id,
        text: i.toString(),
      });
    expect(res.status).to.equal(200);
    id = res.body._id;

    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: id, reason: ['Spam'], explanation: 'seems like spam' });
    expect(res.status).to.equal(200);

    post = await Comment.findById(id);
    expect(post.banned).to.equal(true);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(i == 39);
    expect(user.bannedReason).to.equal(i == 39 ? '10 comments within 10 different post threads banned within 30 days' : undefined);
  }
});

it('openai moderation - keyword trigger on question', async () => {
  setMockPromptResponse(JSON.stringify({ ban: true, explanation: 'spam' }));

  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'chess',
      title: 'Post 1 - boo',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  await new Promise((r) => setTimeout(r, 100));

  // parsing error
  postModeration = await PostModeration.find();
  expect(postModeration.length).to.equal(1);
  console.log(postModeration[0]);
  expect(postModeration[0].openai.isError).to.equal(true);

  post = await Question.findById(q1Id);
  expect(post.flaggedByOpenai).to.equal();
  expect(post.banned).to.equal(false);
  expect(post.bannedBy).to.equal();
  expect(post.bannedReason).to.equal();
});

it('openai moderation - keyword trigger on comment', async () => {
  await initApp(0);
  await initApp(1);

  /*
  Comment 1
  Comment 2
  */

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'chess',
      title: 'Post 1',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: 'Comment 1',
    });
  expect(res.status).to.equal(200);
  c1Id = res.body._id;

  // ban q1
  jsonResponse = [
    {
      postId: q1Id,
      ban: true,
      explanation: 'spam',
    },
    {
      commentId: 'sampleId', // should be ignored
      ban: false,
      explanation: ' not spam',
    },
  ];

  setMockPromptResponse(JSON.stringify(jsonResponse));

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: 'Comment 2 - Infinity',
    });
  expect(res.status).to.equal(200);
  c2Id = res.body._id;

  await new Promise((r) => setTimeout(r, 100));

  postModeration = await PostModeration.find();
  expect(postModeration.length).to.equal(1);
  console.log(postModeration[0]);
  expect(postModeration[0].openai.isError).to.equal(false);

  post = await Question.findById(q1Id);
  expect(post.flaggedByOpenai).to.equal(true);
  expect(post.banned).to.equal(true);
  expect(post.bannedBy).to.equal('openai');
  expect(post.bannedReason).to.equal('spam');

  post = await Comment.findById(c1Id);
  expect(post.flaggedByOpenai).to.equal();
  expect(post.banned).to.equal();
  expect(post.bannedBy).to.equal();
  expect(post.bannedReason).to.equal();

  post = await Comment.findById(c2Id);
  expect(post.flaggedByOpenai).to.equal();
  expect(post.banned).to.equal();
  expect(post.bannedBy).to.equal();
  expect(post.bannedReason).to.equal();

  // edit comment
  res = await request(app)
    .patch('/v1/comment/edit')
    .set('authorization', '0')
    .send({ commentId: c2Id, text: 'Comment 2 - Infinity - Edited' });
  expect(res.status).to.equal(200);
});

it('openai moderation - keyword trigger on reply', async () => {
  await initApp(0);
  await initApp(1);

  /*
  Comment 1
    Reply 1
    Reply 2
  Comment 2
  */

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'chess',
      title: 'Post 1',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: 'Comment 1',
    });
  expect(res.status).to.equal(200);
  c1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: 'Comment 2',
    });
  expect(res.status).to.equal(200);
  c2Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: c1Id,
      text: 'Reply 1',
    });
  expect(res.status).to.equal(200);
  r1Id = res.body._id;

  // dismiss q1, dismiss c1, ban r1
  jsonResponse = [
    {
      postId: q1Id,
      ban: false,
      explanation: 'not spam',
    },
    {
      commentId: c1Id,
      ban: false,
      explanation: 'not spam',
    },
    {
      commentId: r1Id,
      ban: true,
      explanation: 'spam',
    },
  ];

  setMockPromptResponse(JSON.stringify(jsonResponse));

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: c1Id,
      text: 'Reply 2 - infinity',
    });
  expect(res.status).to.equal(200);
  r2Id = res.body._id;

  await new Promise((r) => setTimeout(r, 100));

  postModeration = await PostModeration.find();
  expect(postModeration.length).to.equal(1);
  console.log(postModeration[0]);
  expect(postModeration[0].openai.isError).to.equal(false);

  post = await Question.findById(q1Id);
  expect(post.flaggedByOpenai).to.equal(false);
  // Model field has default value 'false'
  expect(post.banned).to.equal(false);
  expect(post.bannedBy).to.equal();
  expect(post.bannedReason).to.equal();

  post = await Comment.findById(c1Id);
  expect(post.flaggedByOpenai).to.equal(false);
  expect(post.banned).to.equal();
  expect(post.bannedBy).to.equal();
  expect(post.bannedReason).to.equal();

  post = await Comment.findById(r1Id);
  expect(post.flaggedByOpenai).to.equal(true);
  expect(post.banned).to.equal(true);
  expect(post.bannedBy).to.equal('openai');
  expect(post.bannedReason).to.equal('spam');

  post = await Comment.findById(r2Id);
  expect(post.flaggedByOpenai).to.equal();
  expect(post.banned).to.equal();
  expect(post.bannedBy).to.equal();
  expect(post.bannedReason).to.equal();

  post = await Comment.findById(c2Id);
  expect(post.flaggedByOpenai).to.equal();
  expect(post.banned).to.equal();
  expect(post.bannedBy).to.equal();
  expect(post.bannedReason).to.equal();
});

it('backfillCommentInterestName', async () => {
  await initApp(0);
  await initApp(1);

  res = await request(app)
    .post('/v1/question')
    .set('authorization', 1)
    .send({
      interestName: 'chess',
      title: 'Post 1',
    });
  expect(res.status).to.equal(200);
  q1Id = res.body._id;

  res = await request(app)
    .post('/v1/comment')
    .set('authorization', 0)
    .send({
      questionId: q1Id,
      parentId: q1Id,
      text: '',
    });
  expect(res.status).to.equal(200);
  c1Id = res.body._id;

  originalComment = await Comment.findById(c1Id);
  comment = await Comment.findById(c1Id);
  expect(comment.interestName).to.equal('chess');
  expect(comment.language).to.equal('en');

  comment.interestName = undefined;
  comment.language = undefined;
  await comment.save();

  comment = await Comment.findById(c1Id);
  expect(comment.interestName).to.equal();
  expect(comment.language).to.equal();

  await socialLib.backfillCommentInterestName();

  comment = await Comment.findById(c1Id);
  console.log(comment);
  expect(comment.interestName).to.equal('chess');
  expect(comment.language).to.equal('en');
  expect(comment).to.eql(originalComment);
});
