const stub = require('./stub');

stub.createStubs();

const request = require('supertest');
const chai = require('chai');
const chaiSubset = require('chai-subset');
chai.use(chaiSubset);
const { expect } = require('chai');

const app = require('express')();
const { createServer, configureRoutes } = require('../lib/server');

const server = createServer(app);
const workerRoutes = require('../worker/routes/home');

app.use('/v1/worker', workerRoutes());
configureRoutes(app);

const temp = require('temp').track();
const fs = require('fs');

const PurchaseReceipt = require('../models/purchase-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const Interest = require('../models/interest');
const StickerPack = require('../models/sticker-pack');

const interestLib = require('../lib/interest');
const geocoder = require('../lib/geocoder');
const { deferred } = require('promise-callbacks');

const mongoose = require('mongoose');

const port = process.env.PORT || 3000;
const { MongoMemoryServer } = require('mongodb-memory-server');
const { getMockStickerPacks } = require('./helper/sticker-packs');
const databaseLib = require('../lib/database');
//const { updateDailyExchangeRate } = require('../lib/currency-exchange');
const { loadAndCacheTranslatedCities } = require('../lib/translated-locations');

let mongoServer;

before(async () => {
  promise = deferred();
  await geocoder.init({
    load: {
      admin1: true, admin2: false, admin3And4: false, alternateNames: false,
    },
  }, promise.defer());
  await promise;

  server.listen(port, () => {
    console.log('Express started. Listening on %s', port);
  });

  mongoServer = await MongoMemoryServer.create({
    instance: {
      args: [
        '--profile',
        '2',
      ],
    },
    binary: {
      version: '6.0.3',
    },
  });
  const mongoUri = mongoServer.getUri();
  if (process.env.DEBUG) {
    mongoose.set('debug', (collectionName, method, query, doc) => {
      console.log(`${collectionName}.${method}`, JSON.stringify(query, null, 2), JSON.stringify(doc,null,2));
    });
  }
  await mongoose.connect(mongoUri, {
    autoIndex: true,
    compressors: ['snappy'],
  });

  await loadAndCacheTranslatedCities();
});

after(async () => {
  server.close();

  await mongoose.disconnect();
  await mongoServer.stop();
});

beforeEach(async () => {
  stub.reset();
  for (const modelName of mongoose.modelNames()) {
    await mongoose.model(modelName).ensureIndexes().catch((err) => err);
  }
});

beforeEach(stub.createStubs);
beforeEach(loadInterests);
//beforeEach(updateDailyExchangeRate);
async function insertMockStickerPacks() {
  await StickerPack.insertMany(getMockStickerPacks());
}

beforeEach(insertMockStickerPacks);

afterEach(async () => {
  if (process.env.PROFILE) {
    profiler = await mongoose.connection.db.collection('system.profile')
      .find({})
      .sort({ millis: -1 })
      .limit(10)
      .toArray();
    console.log('mongodb profiler: ');
    console.log(JSON.stringify(profiler.reverse(), null, 2));
  }

  console.log('Dropping database');
  await mongoose.connection.db.dropDatabase();
  console.log('Dropped database');
});

// initSocket returns a promise
// success: resolve a new socket object
// fail: reject a error
const socketclient = require('socket.io-client');

const initSocket = function (auth) {
  return new Promise((resolve, reject) => {
    // create socket for communication
    const socket = socketclient(`http://localhost:${port}?authorization=${auth}`, {
      'reconnection delay': 0,
      'reopen delay': 0,
      'force new connection': true,
    });

    // define event handler for sucessful connection
    socket.on('connect', () => {
      console.log(`socket client ${auth} connected`);
      resolve(socket);
    });

    // if connection takes longer than 5 seconds throw error
    setTimeout(() => {
      reject(new Error('Failed to connect within 5 seconds.'));
    }, 5000);
  });
};

// destroySocket returns a promise
// success: resolve true
// fail: resolve false
const destroySocket = (socket) => new Promise((resolve, reject) => {
  // check if socket connected
  if (socket.connected) {
    // disconnect socket
    console.log('disconnecting...');
    socket.disconnect();
    resolve(true);
  } else {
    // not connected
    console.log('no connection to break...');
    resolve(false);
  }
});

function getSocketPromise(socket, event, timeout = 1000) {
  return new Promise((resolve, reject) => {
    socket.on(event, (data4Client) => {
      console.log('Socket received data: ', data4Client);
      resolve(data4Client);
    });
    setTimeout(() => {
      reject(new Error('Failed to get reponse, connection timed out...'));
    }, timeout);
  });
}

buffer = 'a'.repeat(100);
validImagePath = temp.openSync({ suffix: '.jpg' }).path;
fs.writeFileSync(validImagePath, buffer);

buffer = 'a'.repeat(100);
validAIImagePath = temp.openSync({ suffix: '.jpg' }).path;
fs.writeFileSync(validAIImagePath, buffer);

buffer = 'a'.repeat(100);
validVideoPath = temp.openSync({ suffix: '.mov' }).path;
fs.writeFileSync(validVideoPath, buffer);

const validYotiPayload = {
  img: 'base-64-image-data',
  secure: {
    version: "2.4.0",
    token: "token-data",
    verification: "verification-data",
    signature: "signature-data",
  },
};

// message - audio
buffer = 'a'.repeat(100);
validAudioPath = temp.openSync({ suffix: '.mp3' }).path;
fs.writeFileSync(validAudioPath, buffer);

const validGif = 'https://media0.giphy.com/media/t2eBr71ACeDC0/giphy.gif?cid=49c3c173pqhnz94j2hd6ylph2ehnkf948agopmesxpzezimp&rid=giphy.gif';
const validResponseGif = 'https://media0.giphy.com/media/t2eBr71ACeDC0/giphy.gif?cid=49c3c173pqhnz94j2hd6ylph2ehnkf948agopmesxpzezimp&rid=giphy.gif';
const validTenorGif = 'https://media.tenor.com/CtgnPDyNjLkAAAAC/mariah-mariah-carey.gif';
const validTenorResponseGif = 'https://media.tenor.com/CtgnPDyNjLkAAAAM/mariah-mariah-carey.gif';

const initialCoins = 250;

async function createUser(i) {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', i);
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: i });
  user.gender = 'female';
  user.birthday = new Date(new Date().getFullYear() - 31, 0, 1);
  user.age = 31;
  user.horoscope = 'Capricorn';
  user.personality = {
    mbti: 'ESTJ',
  };
  user.preferences = {
    distance: 12500,
    gender: ['female'],
    minAge: 18,
    maxAge: 200,
    personality: ['ISTP'],
  };
  user.pictures = ['picture0', 'picture1'];
  user.location = {
    type: 'Point',
    coordinates: [-157.85, 21.30],
  };
  user.fcmToken = 'token';
  user.description = 'description';
  user.education = 'education';
  user.work = 'work';
  user.enneagram = '1w9';
  user.firstName = i.toString();
  user.handle = `handle${i.toString()}`;
  await user.save();
}

async function createAdminUser() {
  const user = new User({
    _id: '1000',
    admin: true,
    adminPermissions: { all: true },
  });
  await user.save();
  return user._id;
}

function getProfile(i) {
  return {
    _id: i.toString(),
    firstName: i.toString(),
    personality: { mbti: 'ESTJ', avatar: 'Executive' },
    enneagram: '1w9',
    preferences: { purpose: ['dating', 'friends'] },
    horoscope: 'Capricorn',
    profilePicture: 'picture0',
    pictures: ['picture0', 'picture1'],
    age: 31,
    description: 'description',
    location: 'Honolulu, HI 🇺🇸',
    teleport: false,
    education: 'education',
    work: 'work',
    gender: 'female',
    handle: `handle${i.toString()}`,
    prompts: [],
    interests: [],
    interestNames: [],
    verificationStatus: 'unverified',
    verified: false,
    crown: false,
    hideQuestions: false,
    hideComments: false,
    karma: 0,
    numFollowers: 0,
  };
}

function getProfilePreview(i, includeKarma) {
  const preview = {
    _id: i.toString(),
    firstName: i.toString(),
    handle: `handle${i.toString()}`,
    personality: { mbti: 'ESTJ' },
    enneagram: '1w9',
    horoscope: 'Capricorn',
    picture: 'picture0',
    age: 31,
    gender: 'female',
  };
  if (includeKarma) {
    preview.karma = 0;
  }
  return preview;
}

async function loadInterests() {
  const interests = [
    {
      category: 'Music', interest: '#kpop', name: 'kpop', sortIndex: 1,
    },
    {
      category: 'Games', interest: '#chess', name: 'chess', sortIndex: 3,
    },
    {
      category: 'Music', interest: '#latin', name: 'latin', sortIndex: 2,
    },
  ];
  await Interest.insertMany(interests);
  await interestLib.loadInterestsFromDatabase();
}

// conflicts with sinon.fakeTimers()
async function waitMs(time) {
  await new Promise((resolve) => setTimeout(resolve, time));
}

module.exports = {
  app,
  mongoose,
  initSocket,
  destroySocket,
  getSocketPromise,
  validImagePath,
  validVideoPath,
  validAudioPath,
  validGif,
  validResponseGif,
  validTenorGif,
  validTenorResponseGif,
  validYotiPayload,
  initialCoins,
  createUser,
  createAdminUser,
  getProfile,
  getProfilePreview,
  loadInterests,
  waitMs,
  validAIImagePath
};
