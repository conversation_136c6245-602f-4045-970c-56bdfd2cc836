const dotenv = require("dotenv").config();
const mongoose = require("mongoose");
const { addCategoryToProfiles } = require('../lib/database')
const MONGODB_URI = process.env.MONGODB_URI || "mongodb://localhost/test";

(async () => {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB");

    console.log("Backfilling Categories to Profiles...");
    await addCategoryToProfiles();
    console.log("Profiles Backfill Complete");

    mongoose.disconnect();
    console.log("Disconnected from MongoDB");
  } catch (error) {
    console.error("Error during backfill:", error);
  }
})();
