const mongoose = require('mongoose');
const { languageCodes } = require('../lib/languages');

const interestSchema = new mongoose.Schema({
  createdAt: { type: Date, default: () => new Date() },
  createdBy: { type: String, ref: 'User' },
  category: { type: String },
  libCategory: { type: String },
  interest: { type: String, unique: true, trim: true },
  name: { type: String, unique: true, trim: true },
  sortIndex: { type: Number },
  allowImages: { type: Boolean, default: true },
  pending: { type: Boolean },
  status: {
    type: String,
    enum: ['pending', 'rejected'],
  },
  openaiApproval: { type: Boolean },
  numFollowers: { type: Number, default: 0 },
  numFollowersSortIndex: { type: Number, default: Math.random },
  numQuestions: { type: Number, default: 0 },
  numQuestionsPerLanguage: {
    type: Map,
    of: Number,
    default: {},
  },
  eligibleForRanks: {
    type: Map,
    of: Boolean,
    default: {},
  },
  similar: [{ type: String }],
  language: {
    type: String,
    enum: languageCodes,
  },
  lastPostAddedTime: { type: Date },
});

interestSchema.index({
  sortIndex: 1,
});

interestSchema.index({
  pending: 1,
  numFollowersSortIndex: -1,
});

interestSchema.index({
  status: 1,
  numFollowersSortIndex: -1,
});

// Export schema =====================================================================================================================================================================
module.exports = mongoose.model('Interest', interestSchema);
