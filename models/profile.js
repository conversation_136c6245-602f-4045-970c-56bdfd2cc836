const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');
const { validMbti } = require('../lib/personality');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');

function arrToObj(arr) {
  return arr.reduce((obj, val) => ({ ...obj, [val]: { type: Number } }), {});
}

const profileSchema = new mongoose.Schema({
  id: { type: Number, unique: true },
  createdAt: { type: Date, default: Date.now },
  createdBy: { type: String, ref: 'User' },
  name: {
    type: String, required: true, trim: true, maxlength: 1000,
  },
  slug: { type: String },
  gender: { type: String },
  mbti: { type: String, enum: validMbti },
  enneagram: { type: String, enum: enneagrams },
  horoscope: { type: String, enum: horoscopes },
  birthday: { type: Date },
  description: { type: String, trim: true, maxlength: 10000 },
  subcategories: [{ type: Number }],
  countries: [{ type: String }],
  continents: [{ type: String }],
  image: { type: String },
  imageSource: { type: String },
  sort: { type: Number, default: Math.random },
  vote: {
    totalCount: { type: Number, default: 0 },
    mbti: arrToObj(validMbti),
    enneagram: arrToObj(enneagrams),
    horoscope: arrToObj(horoscopes),
  },
  pdb_mbti: { type: String, enum: [...validMbti,null] },
  pdb_enneagram: { type: String, enum: [...enneagrams,null] },
  pdb_horoscope: { type: String, enum: [...horoscopes,null] },
  pdb_confidence:{type:Number,default:0},
  hasComment: { type: Boolean },
  def_mbti: { type: String, enum: validMbti.concat([null]) },
  def_enneagram: { type: String, enum: enneagrams.concat([null]) },
  def_horoscope: { type: String, enum: horoscopes.concat([null]) },
  translatedLanguages: [ { type: String } ],
  intros: { type: mongoose.Mixed },
  /*
  {
    en: {
      quote: '',
      intro: '',
      mbti: '',
      enneagram: '',
      zodiac: '',
    },
  }
  */
  currentDescriptionTypes: {
    mbti: { type: String },
    enneagram: { type: String },
    zodiac: { type: String },
  },
  personalityDescriptions: { type: mongoose.Mixed },
  linkedCategories: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
  }],
  linkedSubcategories: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
    categoryId: { type: Number },
  }],
  linkedProfiles: [{
    _id: false,
    id: { type: Number },
    slug: { type: String },
  }],
  linkedPillarKeywords: { type: mongoose.Mixed },
  translatedNames: { type: mongoose.Mixed },
  /*
  {
    INTP: {
      en: '',
      es: '',
    },
  }
  */
  lastUpdated: { type: Date, default: Date.now },
  imageAttribution: { type: String },
  linkedKeywordsUpdatedAt: { type: Date },
  linkedKeywordsProcessingStartAt: { type: Date },
  duplicated: { type: Boolean },
});

profileSchema.index({
  slug: 1,
});

profileSchema.index({
  subcategories: 1,
  sort: -1,
});

profileSchema.index({
  mbti: 1,
  sort: -1,
});

profileSchema.pre('save', function (next) {
  const voteTotalCount = this.vote?.totalCount || 0;
  const imageScore = this.image ? 100 : 0;
  this.sort = voteTotalCount + imageScore
  if (this.modifiedPaths().length > 0) {
    this.lastUpdated = new Date();
  }
  next();
});

// Define methods
// =============================================================================

// Export schema
// =============================================================================
let conn = connectionLib.getPersonalityDatabaseConnection() || mongoose;
module.exports = conn.model('Profile', profileSchema);
